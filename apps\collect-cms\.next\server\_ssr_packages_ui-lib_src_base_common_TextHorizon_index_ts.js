/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_TextHorizon_index_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_TextHorizon_index_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextHorizon: () => (/* binding */ TextHorizon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./texthorizon.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss\");\n/* harmony import */ var _texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst TextHorizon = ({ cid, Headline, BlockQuote, Paragraph, isFirstSection })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper), Headline ? \"\" : \"nohead\", isFirstSection ? \"first__section\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: [\n                Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading\"),\n                    children: Headline\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content)),\n                    children: [\n                        BlockQuote && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().blockquote), \"aidigi__paragraph--lg\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: BlockQuote\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 6\n                        }, undefined),\n                        Paragraph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().paragraph), \"aidigi__paragraph--md\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: Paragraph\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 6\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 4\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n            lineNumber: 24,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n        lineNumber: 20,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts":
/*!******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/index.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextHorizon: () => (/* reexport safe */ _TextHorizon__WEBPACK_IMPORTED_MODULE_0__.TextHorizon)\n/* harmony export */ });\n/* harmony import */ var _TextHorizon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9UZXh0SG9yaXpvbi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVGV4dEhvcml6b24vaW5kZXgudHM/MzU0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL1RleHRIb3Jpem9uJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss":
/*!*********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss ***!
  \*********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"texthorizon_wrapper__OQFLj\",\n\t\"headline\": \"texthorizon_headline__QGU5y\",\n\t\"content\": \"texthorizon_content__qfSfB\",\n\t\"blockquote\": \"texthorizon_blockquote__RlgAz\",\n\t\"paragraph\": \"texthorizon_paragraph__ecVT8\"\n};\n\nmodule.exports.__checksum = \"f00ff7b5a709\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9UZXh0SG9yaXpvbi90ZXh0aG9yaXpvbi5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVGV4dEhvcml6b24vdGV4dGhvcml6b24ubW9kdWxlLnNjc3M/OTYxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwidGV4dGhvcml6b25fd3JhcHBlcl9fT1FGTGpcIixcblx0XCJoZWFkbGluZVwiOiBcInRleHRob3Jpem9uX2hlYWRsaW5lX19RR1U1eVwiLFxuXHRcImNvbnRlbnRcIjogXCJ0ZXh0aG9yaXpvbl9jb250ZW50X19xZlNmQlwiLFxuXHRcImJsb2NrcXVvdGVcIjogXCJ0ZXh0aG9yaXpvbl9ibG9ja3F1b3RlX19SbGdBelwiLFxuXHRcInBhcmFncmFwaFwiOiBcInRleHRob3Jpem9uX3BhcmFncmFwaF9fZWNWVDhcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZjAwZmY3YjVhNzA5XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss\n");

/***/ })

};
;