import { decodeBlurHash } from 'fast-blurhash'
import { rgbaToDataUri } from './png'

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface BlurHashOptions {
	/**
	 * Aspect ratio (width / height) of the BlurHash image to be decoded.
	 *
	 * @default 1 (square aspect ratio)
	 */
	ratio?: number

	/**
	 * The size of the longer edge (width or height) of the BlurHash image to be
	 * decoded, depending on the aspect ratio.
	 *
	 * Next.js recommend 10px or less for the size.
	 * Also anything more than 10px will cause the page to load slowly.
	 *
	 * https://nextjs.org/docs/pages/api-reference/components/image#blurdataurl
	 *
	 * @default 4
	 */
	size?: number
}

function getScaledDimensions(aspectRatio: number, referenceSize: number) {
	let width: number
	let height: number

	if (aspectRatio >= 1) {
		width = referenceSize
		height = Math.round(referenceSize / aspectRatio)
	} else {
		width = Math.round(referenceSize * aspectRatio)
		height = referenceSize
	}

	return { width, height }
}

export function createPngDataUri(
	hash?: string | null,
	{ ratio = 1, size = 4 }: BlurHashOptions = {}
) {
	let hashData = hash
	if (hashData === null || hashData === undefined) {
		hashData = 'L2MaR]?bfQ?b~qj[fQj[fQfQfQfQ'
	}
	const { width, height } = getScaledDimensions(ratio, size)
	const rgba = decodeBlurHash(hashData, width, height)
	const dataUri = rgbaToDataUri(width, height, rgba)
	return dataUri
}
