@import '@/styles/config';

.navigation {
	display: flex;
	button {
		cursor: pointer;
		// margin-bottom: calc(-1 * spacing(s1));
		padding: spacing(s3) spacing(s5) spacing(s4) spacing(s5);
		background-color: color('white', 0);
		position: relative;
		border-radius: spacing(s2) spacing(s2) 0 0;
		position: relative;

		&.active {
			background-color: var(--collect-primary-color-80);
			z-index: 1;

			&:not(:first-child) {
				&::before {
					content: '';
					position: absolute;
					width: spacing(s2);
					height: spacing(s2);
					background-color: var(--collect-primary-color-80);
					bottom: 0;
					left: calc(-1 * spacing(s2));
					clip-path: path('M8.78516 0C7.86741 3.97691 4.76206 7.08225 0.785156 8H8.78516V0Z');
				}
			}

			&::after {
				content: '';
				position: absolute;
				width: spacing(s2);
				height: spacing(s2);
				background-color: var(--collect-primary-color-80);
				bottom: 0;
				right: calc(-1 * spacing(s2));
				clip-path: path('M8.78516 0C7.86741 3.97691 4.76206 7.08225 0.785156 8H8.78516V0Z');
				transform: scaleX(-1);
			}
		}
	}
}

.content {
	display: grid;
	gap: spacing(s12);
}

.collection__type {
	position: relative;
	background-color: color('white', 0);
	box-shadow: 0px spacing(s1) spacing(s6) 0px rgba(69, 69, 69, 0.15);
	padding: spacing(s8);
	border-radius: 0 spacing(s2) spacing(s2) spacing(s2);
	border-top: spacing(s1) solid var(--collect-primary-color-80);

	display: grid;
	gap: spacing(s8);

	.heading {
		display: flex;
		justify-content: space-between;
		flex-flow: wrap;
		align-items: center;
		gap: spacing(s4);
	}

	.input {
		--input-height: #{spacing(s9)};
		--input-border-color: transparent;
		--input-radius: #{spacing(s2)};

		flex: 0 0 px-to(360px, rem);

		svg,
		input::placeholder {
			color: color('grey', 30);
		}

		input {
			font-weight: 400;
			box-shadow:
				0px 4px 24px 0px rgba(69, 69, 69, 0.15),
				inset 0 0 0 1px var(--input-border-color);
		}
	}
}
