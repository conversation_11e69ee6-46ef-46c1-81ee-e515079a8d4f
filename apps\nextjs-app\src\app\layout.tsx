import '@/styles/global.scss'
import '@collective/ui-lib/styles/global.scss'
import { getNavigationData } from '@collective/integration-lib/cms'
import { searchGeneralSetting, type GeneralSettingProps } from '@collective/integration-lib/search'
import GeneralSettingProvider from '@collective/ui-lib/contexts/GeneralSettingContext'
import NavigationProvider from '@collective/ui-lib/contexts/NavigationContext'
import QuickNavigationContext from '@collective/ui-lib/contexts/QuickNavigationContext'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	return children
}
