'use client'

import { LayoutEditor } from '@/components/Builder/LayoutEditor'
import { PageBuilderProvider, type BuilderBaseProps } from 'contexts/BuilderContext'
import { LeftSidebarLayout } from './LeftSidebarLayout'
import styles from './pagebuilderlayout.module.scss'
import { RightSidebarLayout } from './RightSidebarLayout'
import { ToolbarLayout } from './ToolbarLayout'

export const PageBuilderLayout = ({
	children,
	value,
}: {
	children: React.ReactNode
	value: BuilderBaseProps
}) => {
	return (
		<PageBuilderProvider value={value}>
			<div className={styles.wrapper}>
				<ToolbarLayout />
				<main className={styles.main}>
					<LeftSidebarLayout />
					<LayoutEditor>{children}</LayoutEditor>
					<RightSidebarLayout />
				</main>
			</div>
		</PageBuilderProvider>
	)
}
