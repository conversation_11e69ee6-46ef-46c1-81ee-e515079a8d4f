'use client'

import {
	Button,
	checkIsLoginClient,
	Icon,
	Input,
	setCookie,
	useAnim,
	useIsomorphicLayoutEffect,
	type AnimVars,
} from '@collective/core'
import {
	getCustomPathData,
	getDataClient,
	postJsonCustomPathData,
} from '@collective/integration-lib/cms'
import { useModal } from '@collective/ui-lib/contexts/ModalContext'
import cn from 'classnames'
import { s } from 'framer-motion/client'
import Image from 'next/image'
import React, { useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import styles from './bubblechat.module.scss'

type ChatProps = {
	uuid: string | null
}

type UserProps = {
	username: string
	fullName: string
	email: string
	avatar?: string
}

export const BubbleChat = ({ uuid }: ChatProps) => {
	const { onOpenModal } = useModal()
	const [CoChat, setCoChat] = useState([
		{
			role: 'assistant',
			content: 'We are currently finishing this feature. Stay tune for update!',
			time: formatTime(new Date().toISOString()),
		},
	])
	const [value, setValue] = useState('')
	const [prompt, setPrompt] = useState('')
	const [isLoading, setIsLoading] = useState(true)
	const chatBodyRef = useRef<HTMLDivElement | null>(null)
	const [guestChatCount, setguestChatCount] = useState(0)
	const [accMethod, setAccMethod] = useState<string>()
	const [userData, setUserData] = useState<UserProps | null>(null)

	const getChatData = async () => {
		if (!uuid) {
			uuid = sessionStorage.getItem('uuid')
		}
		const response = await getCustomPathData(`/api/getChat?uuid=${uuid}`)
		if (response.messages.length > 0) {
			setCoChat(response.messages)
		}
	}

	const handleChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
		const val = evt.target?.value
		setValue(val)
	}

	const handleCreateNewChat = async () => {
		const res = await postJsonCustomPathData<{ uuid: string }>('/api/createChat', '')
		sessionStorage.setItem('uuid', res.uuid)
		return res.uuid
	}

	const handleChat = async () => {
		if (!value.trim()) return
		setValue('')
		setIsLoading(true)
		try {
			const time = formatTime(new Date().toISOString())
			setCoChat((prevState) => [...prevState, { role: 'user', content: value, time: time }])
			if (!uuid) {
				const newUuid = await handleCreateNewChat()
				const response = await postJsonCustomPathData<{ answer: string }>(
					'/api/ask',
					JSON.stringify({
						text: value,
						time: time,
						uuid: newUuid,
					})
				)
				setCoChat((prevState) => [
					...prevState,
					{ role: 'assistant', content: response.answer, time: time },
				])
			} else {
				const response = await postJsonCustomPathData<{ answer: string }>(
					'/api/ask',
					JSON.stringify({
						text: value,
						time: time,
						uuid: sessionStorage.getItem('uuid'),
					})
				)
				setCoChat((prevState) => [
					...prevState,
					{ role: 'assistant', content: response.answer, time: time },
				])
			}
		} catch (error) {
			console.error(error)
		} finally {
			setIsLoading(false)
		}
	}

	const handlePromptClick = (prompt: string) => {
		setPrompt(prompt)
		setValue(prompt)
	}

	useIsomorphicLayoutEffect(() => {
		if (prompt) {
			handleChat()
		}
	}, [prompt])

	useIsomorphicLayoutEffect(() => {
		getChatData()
	}, [uuid])

	useIsomorphicLayoutEffect(() => {
		if (chatBodyRef.current) {
			chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight
		}
	}, [CoChat])

	const userHasContent = CoChat.some((message) => message.role === 'user')
	const samplePromts = [
		{ Label: 'What makes a brand truly memorable?', categoryName: 'Branding Design' },
		{ Label: 'What makes a brand truly memorable?', categoryName: 'Typography' },
		{
			Label: 'How do you determine the right color palette for the brand or product?',
			categoryName: 'Color',
		},
		{ Label: 'What makes a brand truly memorable?', categoryName: 'Voice and Tone' },
	]
	const [isShow, setIsShow] = useState(false)
	const { current: blockProps } = useRef<AnimVars>({
		config: {
			paused: true,
		},
		timeline: [
			{
				targets: '.aidigi__chat--button',
				vars: {
					fromTo: [
						{
							width: 64,
							height: 64,
							background: `linear-gradient(180deg, #190B3F 0%, #411DA5 100%)`,
						},
						{
							width: 400,
							height: 600,
							duration: 0.5,
							borderRadius: '25px',
							background: '#F6F6F7',
							ease: 'power3.inOut',
							boxShadow: `0px 4px 30px 0px rgba(0, 0, 0, 0.2)`,
						},
					],
				},
			},
			{
				targets: '.star__icon',
				vars: {
					fromTo: [
						{
							opacity: 1,
						},
						{
							opacity: 0,
							duration: 0.25,
							ease: 'power3.inOut',
						},
					],
				},
				position: 0,
			},
			{
				targets: '.aidigi__chat--dialog',
				vars: {
					fromTo: [
						{
							opacity: 0,
							visibility: 'hidden',
						},
						{
							opacity: 1,

							visibility: 'visible',
							duration: 0.25,
							ease: 'power3.inOut',
						},
					],
				},
			},
		],
	})

	// const [ref, tl] = useAnim({ ...blockProps, deps: [isShow] })
	// Disable deps: [isShow] to fix the flicking issue
	// TODO: Investigate the flicking issue @hoangvietfreelancer
	const [ref, tl] = useAnim({ ...blockProps })

	useIsomorphicLayoutEffect(() => {
		isShow ? tl.play() : tl.reverse()
	}, [isShow])

	useIsomorphicLayoutEffect(() => {
		const getUser = async () => {
			// Check login
			const user = await checkIsLoginClient()
			user && setAccMethod('user')
			if (user !== null) {
				const data = await getDataClient(`users/me`, user?.strapiToken, 4)
				setUserData({
					username: data.username,
					email: data.email,
					fullName: data.fullName,
					avatar: data.avatar,
				})
				user && setAccMethod('user')
			} else {
				localStorage.removeItem('token')
				sessionStorage.removeItem('token')
				setCookie('token', '', 0)
			}
		}
		getUser()
	}, [])

	const FirstAccessSection = () => {
		return (
			<div className="aidigi__buttons">
				<Button onClick={() => onOpenModal('signin')} className="aidigi__button outline">
					Sign in
				</Button>
				<Button onClick={() => setAccMethod('guest')} className="aidigi__link">
					Stay as guest
				</Button>
			</div>
		)
	}

	const ExceedLimitSection = () => {
		return (
			<div className="exceed">
				<small>
					You have exceeded the message limit.
					<Button className="adigi__link">Sign in</Button> to continue with Cōsmo.
				</small>
			</div>
		)
	}
	return (
		<div ref={ref}>
			<button
				className={cn(styles.chat, 'aidigi__chat--button')}
				onClick={() => setIsShow(!isShow)}
			>
				<Icon className="star__icon" variant="star" />
			</button>
			<div className={cn(styles.dialog, 'aidigi__chat--dialog')}>
				<div className={styles.dialog__header}>
					<div className={styles.chat__status}>
						<Image src="/CoAI.png" alt="Avatar" className={styles.avatar} width={50} height={50} />
						<p className="aidigi__paragraph--md">Cōsmo</p>
					</div>
					<button className={styles.close} onClick={() => setIsShow(false)}>
						<Icon variant="x" />
					</button>
				</div>
				<div className={styles.dialog__body} ref={chatBodyRef}>
					{CoChat.map((value, index) => {
						if (value.role === 'assistant') {
							return (
								<div key={index} data-message-role="bot">
									<Image
										src="/CoAI.png"
										alt="Avatar"
										className={styles.avatar}
										width={50}
										height={50}
									/>
									<div className={styles.message}>
										<div className={styles.box__chat}>
											<div className={cn('aidigi__paragraph--md', styles.box)}>
												<ReactMarkdown rehypePlugins={[rehypeRaw]}>
													{value.content.replaceAll('\n    ', '\n')}
												</ReactMarkdown>
											</div>
										</div>

										{/* <span className={styles.time}>CōAI {value.time}</span> */}
									</div>
								</div>
							)
						} else {
							return (
								<div key={index} data-message-role="user">
									<div className={styles.message}>
										<div className={styles.box__chat}>
											<div className={cn('aidigi__paragraph--md', styles.box)}>{value.content}</div>
										</div>
										{/* <span className={styles.time}>Username {value.time}</span> */}
									</div>
								</div>
							)
						}
					})}
				</div>
				<div className={styles.dialog__footer}>
					{/* Ẩn phần prompt nếu có content của user */}
					{!userHasContent && (
						<div className={styles.prompt__list}>
							{samplePromts.map((item, index) => (
								<Button
									key={index}
									onClick={() => {
										handlePromptClick(item.Label)
									}}
								>
									{item.Label}
								</Button>
							))}
						</div>
					)}

					{accMethod ? (
						accMethod === 'guest' && guestChatCount === 3 ? (
							<ExceedLimitSection />
						) : (
							<Input
								value={value}
								// disabled
								onChange={handleChange}
								onKeyDown={(e) => {
									if (e.key === 'Enter' && !e.shiftKey) {
										handleChat()
									}
								}}
								placeholder="Write a message"
								endIcon={
									<Button onClick={handleChat} disabled={isLoading}>
										<Icon variant="send" />
									</Button>
								}
								className={styles.chat__input}
							/>
						)
					) : (
						<FirstAccessSection />
					)}
				</div>
			</div>
		</div>
	)
}

const formatTime = (isoTime: string) => {
	return new Date(isoTime).toLocaleTimeString([], {
		hour: '2-digit',
		minute: '2-digit',
		hour12: true,
	})
}
