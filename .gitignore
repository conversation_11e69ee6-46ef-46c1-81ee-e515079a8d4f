# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# and https://github.com/github/gitignore for examples

# local env files (followinf dotenv-flow / nextjs convention)

.env.local
.env.*.local

# turbo
.turbo

# dependencies
node_modules
.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*

# testing
/coverage
.out/

# Debug

**/.debug

# Build directories (next.js...)
/.next/
/out/
/build

# Cache
**/tsconfig.build.tsbuildinfo
**/tsconfig.tsbuildinfo
**/.eslintcache
.cache/*

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# IDE
.idea/*
.project
.classpath
*.launch
*.sublime-workspace

.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Docker overrides

./docker-compose.override.yml

# Deployment platforms

.vercel
