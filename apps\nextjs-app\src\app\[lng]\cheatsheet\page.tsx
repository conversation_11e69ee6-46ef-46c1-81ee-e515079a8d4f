import { Wrapper } from '@collective/ui-lib/src/base/Wrapper'
import { InitialData as MockData } from '@/mock/CheatSheets'
import styles from './page.module.scss'

// export const runtime = 'edge'
export default function Page() {
	return (
		<div className={styles.wrapper}>
			{MockData.Components.map((component, index) => (
				<Wrapper key={index} commonData={{ locales: 'en' }} data={component} />
			))}
		</div>
	)
}
