@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	background-color: color('black', 25);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.modal {
	width: px-to(480px, rem);
	background-color: color('white', 100);
	border-radius: spacing(s6);
	position: relative;

	&__wrapper {
		padding: px-to(80px, rem) spacing(s10) spacing(s6);
	}

	&__confirm {
		padding: px-to(80px, rem) spacing(s6) spacing(s6);

		.modal__header {
			position: absolute;
			top: spacing(s8);
			left: spacing(s6);
			margin: 0;
		}
		.modal__body {
			padding-bottom: spacing(s6);
		}
		.modal__footer {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			button {
				padding: spacing(s2) spacing(s5);
			}
		}
	}

	&__header {
		display: grid;
		gap: spacing(s6);
		margin-bottom: spacing(s8);
		h2 {
			font-weight: 500;
		}
	}

	&__body {
		display: grid;
		gap: spacing(s6);
		margin-bottom: spacing(s6);
	}

	&__footer {
		display: grid;
		gap: spacing(s3);
	}

	:global(button.submit) {
		width: 100%;
		border-color: color('light-gray', 100);
		font-weight: 500;
		@include fluid($font-size) {
			font-size: size('paragraph', 'lg');
		}
	}
}

button.close {
	position: absolute;
	top: spacing(s3);
	right: spacing(s3);
	padding: 0;
	color: color('neutral-gray');
	svg {
		--icon-size: #{px-to(36px, rem)};
	}
}

.controller {
	display: grid;
	gap: spacing(s1);
	position: relative;

	span {
		margin-bottom: spacing(s2) !important;
		font-weight: 500 !important;
		--label-color: #{color('neutral-gray')};
		@include fluid($font-size) {
			font-size: size('paragraph', 'md') !important;
		}
	}
	input {
		--input-radius: #{px-to(8px, rem)};
		--input-padding-x: #{spacing(s6)};
		--input-padding-y: #{spacing(s4)};
		border: px-to(1px, rem) solid color('border');
		box-shadow: none;
		font-weight: 400 !important;
		@include fluid($font-size) {
			font-size: size('paragraph', 'md') !important;
		}
		&::placeholder {
			color: color('light-gray', 100);
		}
		&:focus {
			box-shadow: none;
		}
	}
	&__error {
		// height: spacing(s5);
		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
			color: color('red', 100);
		}

		input {
			border: px-to(1px, rem) solid color('red', 100);
			--input-bg: #{color('red', 5)};
		}

		&__msg {
			min-height: px-to(18px, rem);
			color: color('red', 100);
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
		}
	}
}

.remember {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: spacing(s2) 0 spacing(s3);

	* {
		color: color('neutral-gray');
		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}

		&:hover {
			color: color('black', 100);
		}
	}

	input {
		& + label {
			display: flex;
			align-items: center;
			gap: spacing(s2);
			--checkbox-size: #{px-to(18px, rem)};
			--checkbox-radius: #{px-to(2px, rem)};
			--checkbox-color: #{color('light-gray', 100)};
			--checkbox-active-color: #{color('black', 100)};
		}
		& + label::after {
			top: px-to(-1.5px, rem) !important;
			left: px-to(-4px, rem) !important;
			width: px-to(26px, rem) !important;
			height: px-to(26px, rem) !important;
		}

		&:checked + label::after {
			background-color: color('white', 100);
		}
	}
}

.alert {
	text-align: center;
	color: color('red', 100);
	min-height: px-to(18px, rem);
	@include fluid($font-size) {
		font-size: size('paragraph', 'md');
	}
}

button.showhide {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 10;
	padding: 0;
	color: color('neutral-gray');
	--icon-size: #{px-to(24px, rem)};
	@include fluid($font-size) {
		font-size: size('paragraph', 'md');
	}
	&:hover {
		color: color('black', 100);
	}
	svg {
		margin-top: px-to(-3px, rem);
	}
}
