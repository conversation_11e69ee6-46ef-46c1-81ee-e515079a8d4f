@import '@/styles/config';

.user_menu {
	position: relative;
}

.user_button {
	display: flex;
	align-items: center;
	gap: spacing(s3);
	padding: spacing(s2) spacing(s3);
	border-radius: spacing(s2);
	cursor: pointer;
	transition: background-color 0.2s;
	
	&:hover {
		background-color: color('grey', 20);
	}
}

.user_avatar {
	width: px-to(32px, rem);
	height: px-to(32px, rem);
	border-radius: 50%;
	background-color: var(--collect-primary-color-80);
	color: color('white', 0);
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	font-size: px-to(14px, rem);
}

.user_avatar_large {
	width: px-to(48px, rem);
	height: px-to(48px, rem);
	border-radius: 50%;
	background-color: var(--collect-primary-color-80);
	color: color('white', 0);
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	font-size: px-to(18px, rem);
}

.user_name {
	font-weight: 500;
	color: color('grey', 80);
}

.chevron {
	transition: transform 0.2s;
	color: color('grey', 60);
	
	&.open {
		transform: rotate(180deg);
	}
}

.dropdown_menu {
	position: absolute;
	top: calc(100% + spacing(s2));
	right: 0;
	width: px-to(240px, rem);
	background-color: color('white', 0);
	border-radius: spacing(s2);
	box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
	z-index: 100;
	overflow: hidden;
}

.dropdown_header {
	padding: spacing(s4);
	display: flex;
	align-items: center;
	gap: spacing(s3);
}

.user_info {
	display: flex;
	flex-direction: column;
}

.user_fullname {
	font-weight: 600;
	color: color('grey', 90);
}

.user_username {
	font-size: px-to(12px, rem);
	color: color('grey', 60);
}

.dropdown_divider {
	height: 1px;
	background-color: color('grey', 20);
	margin: 0;
}

.dropdown_items {
	padding: spacing(s2);
}

.dropdown_item {
	display: flex;
	align-items: center;
	gap: spacing(s3);
	padding: spacing(s3) spacing(s4);
	width: 100%;
	text-align: left;
	border-radius: spacing(s1);
	transition: background-color 0.2s;
	color: color('grey', 80);
	
	&:hover {
		background-color: color('grey', 10);
	}
	
	&:last-child {
		color: color('red', 60);
	}
}
