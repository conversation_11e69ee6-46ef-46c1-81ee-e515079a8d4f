'use client'

import { ObjectUtils } from '@collective/core'
import React, { useMemo, useState, type Dispatch, type SetStateAction } from 'react'
import type { configurationApiData, initApiData } from '@/mock/Builder'
import { defaultConfig } from '@/mock/Builder'
import type { IServerComponentProps, IResultDataProps, IContentTypeProps } from 'common'

export type Entry = [string, object]
export type ChildComponentProps = {
	id: number
	name: string
	value: object
	fields: Entry[]
	onChange?: (props: { field: string; value: unknown }) => void
	handleRemove?: (idx: number) => void
	handleDuplicate?: (idx: number) => void
	entryIndex?: number // Index of this entry in the parent component
}

export interface BuilderBaseProps {
	globals: typeof initApiData
	data: { data: IResultDataProps }
	contentType: { data: IContentTypeProps }
	components: { data: IServerComponentProps[] }
	configuration: typeof configurationApiData
	locale: string
	slug: string[]
	screenTypes?: {
		[key: string]: number
	}
}

export interface PageBuilderContextProps extends BuilderBaseProps {
	setData: Dispatch<SetStateAction<{ data: IResultDataProps }>>
	historyChanges: object
	setHistoryChanges: Dispatch<SetStateAction<object>>
	isPreview: boolean
	setIsPreview: Dispatch<SetStateAction<boolean>>
	expandedSidebar: { left: boolean; right: boolean }
	setExpandedSidebar: Dispatch<SetStateAction<{ left: boolean; right: boolean }>>
	screenSize: number
	setScreenSize: Dispatch<SetStateAction<number>>
	updatedAt: number
	setUpdatedAt: Dispatch<SetStateAction<number>>
	editingIden: { key: string; id: number }
	setEditingIden: Dispatch<SetStateAction<{ key: string; id: number }>>
	normalizedData: IResultDataProps
	childComponentData: ChildComponentProps[]
	setChildComponentData: Dispatch<SetStateAction<ChildComponentProps[]>>
	layerPos: string
	setLayerPos: Dispatch<SetStateAction<string>>
}

export const PageBuilderContext = React.createContext({} as PageBuilderContextProps)

export const PageBuilderProvider = (props: {
	value: BuilderBaseProps
	children: React.ReactNode
}) => {
	const propsValue = props.value ?? {}

	const [globals] = useState(propsValue.globals ?? {})
	const [data, setData] = useState(propsValue.data ?? {})
	const [contentType] = useState(propsValue.contentType ?? {})
	const [components] = useState(propsValue.components ?? {})
	const [locale] = useState(propsValue.locale ?? 'en')
	const [configuration] = useState(propsValue.configuration)
	const [historyChanges, setHistoryChanges] = useState({})
	const [isPreview, setIsPreview] = useState(false)
	const [slug] = useState(propsValue.slug ?? [])
	const [expandedSidebar, setExpandedSidebar] = useState({ left: true, right: true })
	const [screenTypes] = useState(propsValue.screenTypes ?? defaultConfig.screenTypes)
	const [screenSize, setScreenSize] = useState(Object.values(screenTypes)[0] as number)
	const [updatedAt, setUpdatedAt] = useState(Date.now())
	const [editingIden, setEditingIden] = useState({ key: '', id: -1 })
	const [childComponentData, setChildComponentData] = useState<ChildComponentProps[]>([])
	const [layerPos, setLayerPos] = useState('')

	// Normalize data input
	const normalizedData = useMemo(() => {
		const { data: commonData } = data
		const orgData = commonData ?? {}
		const { components, ...props } = ObjectUtils.elevateProperty(
			orgData,
			'data'
		) as typeof commonData

		const isTempKeyExisted = components.some((component) => component.__temp_key__)

		return {
			...props,
			components: isTempKeyExisted
				? components
				: components.map((component, index: number) => ({ ...component, __temp_key__: index + 1 })),
		}
	}, [data])

	const value: PageBuilderContextProps = {
		globals,
		components,
		data,
		setData,
		contentType,
		locale,
		configuration,
		historyChanges,
		setHistoryChanges,
		isPreview,
		setIsPreview,
		slug,
		expandedSidebar,
		setExpandedSidebar,
		screenTypes,
		screenSize,
		setScreenSize,
		updatedAt,
		setUpdatedAt,
		editingIden,
		setEditingIden,
		normalizedData,
		childComponentData,
		setChildComponentData,
		layerPos,
		setLayerPos,
	}

	return <PageBuilderContext.Provider value={value}>{props.children}</PageBuilderContext.Provider>
}
