import { getRandomInt } from './get-random-int'
// eslint-disable-next-line @typescript-eslint/naming-convention
type PlainObject = { [key: string]: string }

export class ArrayUtils {
	static getRandom<T>(items: T[]): T | undefined {
		if (items.length === 0) {
			throw new Error('Array is empty')
		}
		return items[getRandomInt(0, items.length - 1)]
	}

	static getItem<T>(arr: T[], item: T): T | undefined {
		const index = arr.indexOf(item)
		if (index > -1) {
			return arr[index]
		}
		return undefined
	}

	static updateItem<T>(arr: T[], item: T, data: T): T[] {
		const index = arr.indexOf(item)
		if (index > -1) {
			arr[index] = data
		}
		return arr
	}

	static removeItem<T>(arr: T[], item: T): T[] {
		const index = arr.indexOf(item)
		if (index > -1) {
			arr.splice(index, 1)
		}
		return arr
	}

	static groupBy<T extends PlainObject, K extends keyof T>(
		array: T[],
		propertyName: K
	): Record<T[K], T[]> {
		return array.reduce(
			(accumulator, obj) => {
				const key = obj[propertyName]
				if (!accumulator[key]) {
					accumulator[key] = []
				}
				accumulator[key].push(obj)
				return accumulator
			},
			{} as Record<T[K], T[]>
		)
	}
}
