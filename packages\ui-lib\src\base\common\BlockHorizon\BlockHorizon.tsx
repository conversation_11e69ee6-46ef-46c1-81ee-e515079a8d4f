import cn from 'classnames'
import Markdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import styles from './blockhorizon.module.scss'

type BlockHorizonProps = {
	cid?: string
	isWrapContent: boolean
	Blocks: {
		Title?: string
		Content?: string
	}[]
}

export const BlockHorizon = ({ cid, isWrapContent, Blocks }: BlockHorizonProps) => (
	<section id={cid} className={cn(styles.wrapper)}>
		<div className={cn('aidigi__grid', isWrapContent ? '' : 'unwrap__wrapper')}>
			{Blocks.map((item, idx) => (
				<div key={idx} className={cn(isWrapContent ? '' : 'unwrap', styles.block)}>
					{item.Title && (
						<h4 className={cn('aidigi__heading', styles.block__title)}>{item.Title}</h4>
					)}
					{item.Content && (
						<div className={styles.block__content}>
							<Markdown rehypePlugins={[rehypeRaw]}>{item.Content}</Markdown>
						</div>
					)}
				</div>
			))}
		</div>
	</section>
)
