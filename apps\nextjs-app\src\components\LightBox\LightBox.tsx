import { Icon, useIsomorphicLayoutEffect } from '@collective/core'
import { motion, AnimatePresence } from 'framer-motion'
import { useCallback, useEffect, useState } from 'react'
import styles from './lightbox.module.scss'

type Props = {
	curPath?: string
}

type ImageData = {
	src: string
	alt?: string
}

export const LightBox = ({ curPath }: Props) => {
	const [images, setImages] = useState<ImageData[]>([])
	const [currentIndex, setCurrentIndex] = useState<number | null>(null)
	const [zoom, setZoom] = useState(1)

	useIsomorphicLayoutEffect(() => {
		setTimeout(() => {
			const allImages = Array.from(
				document.querySelectorAll(".page__content img:not([data-lightbox='false'])")
			).map((img) => {
				const image = img as HTMLImageElement
				return { src: image.src, alt: image.alt }
			})

			setImages(allImages)

			allImages.forEach((image, index) => {
				const imgElement = document.querySelector(`img[src="${image.src}"]`)
				if (imgElement) {
					imgElement.addEventListener('click', () => openLightbox(index))
				}
			})

			return () => {
				allImages.forEach((image) => {
					const imgElement = document.querySelector(`img[src="${image.src}"]`)
					if (imgElement) {
						imgElement.removeEventListener('click', () => openLightbox(images.indexOf(image)))
					}
				})
			}
		}, 1000)
	}, [curPath])

	const openLightbox = (index: number) => {
		setCurrentIndex(index)
		setZoom(1)
	}

	const closeLightbox = () => {
		setCurrentIndex(null)
		setZoom(1)
	}

	const nextImage = () => {
		if (currentIndex !== null) {
			setCurrentIndex((currentIndex + 1) % images.length)
			setZoom(1)
		}
	}

	const prevImage = () => {
		if (currentIndex !== null) {
			setCurrentIndex((currentIndex - 1 + images.length) % images.length)
			setZoom(1)
		}
	}

	const handleKeyDown = useCallback(
		(e: KeyboardEvent) => {
			if (currentIndex === null) return
			if (e.key === 'Escape') closeLightbox()
			if (e.key === 'ArrowRight') nextImage()
			if (e.key === 'ArrowLeft') prevImage()
		},
		[currentIndex]
	)

	useIsomorphicLayoutEffect(() => {
		document.addEventListener('keydown', handleKeyDown)
		return () => document.removeEventListener('keydown', handleKeyDown)
	}, [handleKeyDown, zoom])

	return (
		<AnimatePresence>
			{currentIndex !== null && (
				<motion.div
					className={styles.lightboxOverlay}
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
				>
					<button className={styles.closeButton} onClick={closeLightbox}>
						<Icon variant="x" />
					</button>
					<button className={`${styles.navButton} ${styles.prevButton}`} onClick={prevImage}>
						<Icon variant="prev" />
					</button>

					<motion.div
						key={currentIndex}
						initial={{ opacity: 0, x: 100 }}
						animate={{ opacity: 1, x: 0 }}
						exit={{ opacity: 0, x: -100 }}
						transition={{ duration: 0.3 }}
						style={{ '--zoom': zoom } as React.CSSProperties}
						className={styles.lightboxWrapper}
					>
						<img
							src={images[currentIndex]?.src}
							alt={images[currentIndex]?.alt}
							className={styles.lightboxImage}
						/>
					</motion.div>

					<button className={`${styles.navButton} ${styles.nextButton}`} onClick={nextImage}>
						<Icon variant="next" />
					</button>

					<div className={styles.zoomControls}>
						<button
							className={styles.zoomButton}
							onClick={() => setZoom((prev) => Math.min(3, prev + 0.2))}
						>
							<Icon variant="zoom-in" />
						</button>
						<button
							className={styles.zoomButton}
							onClick={() => setZoom((prev) => Math.max(1, prev - 0.2))}
						>
							<Icon variant="zoom-out" />
						</button>
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	)
}
