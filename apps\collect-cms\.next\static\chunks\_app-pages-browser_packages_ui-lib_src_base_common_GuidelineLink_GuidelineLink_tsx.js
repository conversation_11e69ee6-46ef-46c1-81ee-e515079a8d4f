/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_GuidelineLink_GuidelineLink_tsx"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss":
/*!*************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss ***!
  \*************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"guidelinelink_wrapper__2aoiX\",\"content\":\"guidelinelink_content__dM8Jn\",\"link\":\"guidelinelink_link__087_f\",\"link__icon\":\"guidelinelink_link__icon__q2qEL\",\"link__label\":\"guidelinelink_link__label__k4pQn\"};\n    if(true) {\n      // 1747630336559\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"8987be04b102\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0d1aWRlbGluZUxpbmsvZ3VpZGVsaW5lbGluay5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQjtBQUNsQixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUosY0FBYyxzREFBc0Q7QUFDclAsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9HdWlkZWxpbmVMaW5rL2d1aWRlbGluZWxpbmsubW9kdWxlLnNjc3M/MmU1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wid3JhcHBlclwiOlwiZ3VpZGVsaW5lbGlua193cmFwcGVyX18yYW9pWFwiLFwiY29udGVudFwiOlwiZ3VpZGVsaW5lbGlua19jb250ZW50X19kTThKblwiLFwibGlua1wiOlwiZ3VpZGVsaW5lbGlua19saW5rX18wODdfZlwiLFwibGlua19faWNvblwiOlwiZ3VpZGVsaW5lbGlua19saW5rX19pY29uX19xMnFFTFwiLFwibGlua19fbGFiZWxcIjpcImd1aWRlbGluZWxpbmtfbGlua19fbGFiZWxfX2s0cFFuXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDc2MzAzMzY1NTlcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovQ0RBL3JlcG9zL2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUvYXBwcy9jb2xsZWN0LWNtcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI4OTg3YmUwNGIxMDJcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx":
/*!*****************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GuidelineLink: function() { return /* binding */ GuidelineLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./guidelinelink.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss\");\n/* harmony import */ var _guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3__);\nvar _this = undefined;\n\n\n\n\n\nvar GuidelineLink = function(param) {\n    var cid = param.cid, Variant = param.Variant, Links = param.Links;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().content)),\n                style: {\n                    \"--position\": Variant == \"fill\" ? 0 : 7,\n                    \"--total\": Variant == \"fill\" ? 12 : 6\n                },\n                children: Links === null || Links === void 0 ? void 0 : Links.map(function(item, idx) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link)),\n                        style: {\n                            \"--width\": Variant == \"fill\" ? 6 : 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.Link,\n                            target: \"_blank\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                    variant: \"book-open\",\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link__icon))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link__label)),\n                                    children: item.Label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 7\n                        }, _this)\n                    }, idx, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 6\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                lineNumber: 18,\n                columnNumber: 4\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, _this);\n};\n_c = GuidelineLink;\nvar _c;\n$RefreshReg$(_c, \"GuidelineLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx\n"));

/***/ })

}]);