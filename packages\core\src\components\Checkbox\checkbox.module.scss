@use '@/styles/config' as *;

.wrapper {
	--checkbox-size: #{px-to(20px, rem)};
	--checkbox-radius: #{px-to(5px, rem)};
	--checkbox-border-size: #{px-to(1px, rem)};
	--checkbox-color: #000;
	--checkbox-hover-color: var(--checkbox-color);
	--checkbox-active-color: transparent;

	label {
		user-select: none;
		cursor: pointer;
		display: inline-flex;
		gap: px-to(6px, rem);
		line-height: 1.5;
		position: relative;
		transition: 0.2s;
	}

	&.radio {
		label::before {
			content: '';
			border-radius: 50%;
			flex: 1 0 var(--checkbox-size);
			width: var(--checkbox-size);
			height: var(--checkbox-size);
			border: 1px solid;
			display: inline-block;
		}

		input:checked + label::before {
			background: radial-gradient(currentcolor 40%, transparent 45%);
		}
	}

	&.checkbox {
		label {
			&::before {
				content: '';
				border-radius: var(--checkbox-radius);
				flex-basis: var(--checkbox-size);
				width: var(--checkbox-size);
				flex: 0 0 var(--checkbox-size);
				height: var(--checkbox-size);
				border: var(--checkbox-border-size) solid var(--checkbox-color);
				display: inline-block;
				transition: 0.2s background-color;
			}

			&::after {
				content: '';
				position: absolute;
				width: var(--checkbox-size);
				height: var(--checkbox-size);
				left: 0;
				top: 0;
				mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="9" viewBox="0 0 12 9" fill="none"><path d="M1 2.84615L5.09091 7L11 1" stroke="currentColor" stroke-width="2"/></svg>');
				mask-repeat: no-repeat;
				mask-position: center center;
				background-color: currentcolor;
				mask-size: 50%;
				opacity: 0;
				transition: 0.2s opacity;
			}

			&:hover {
				&::before {
					border-color: var(--checkbox-hover-color);
				}
			}
		}

		input:checked + label {
			&::before {
				border-color: var(--checkbox-active-color);
				background-color: var(--checkbox-active-color);
			}
			&::after {
				opacity: 1;
			}
		}
	}

	&.alignCenter {
		label {
			align-items: center;
		}

		input:checked + label::after {
			top: 50%;
			transform: translateY(-50%);
		}
	}

	&:global(.indeterminate) {
		label {
			&::before {
				background-color: currentcolor;
			}

			&::after {
				content: '';
				position: absolute;
				width: px-to(12px, rem);
				height: px-to(3px, rem);
				left: calc(var(--checkbox-size) / 2 - px-to(6px, rem));
				top: calc(var(--checkbox-size) / 2 - px-to(1.5px, rem));
				background-color: currentcolor;
				transition: none;
			}
		}
	}
}
