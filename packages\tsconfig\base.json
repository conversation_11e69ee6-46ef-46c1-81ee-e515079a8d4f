{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"moduleResolution": "node", "verbatimModuleSyntax": true, "strict": true, "useUnknownInCatchVariables": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": true, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "resolveJsonModule": true, "skipLibCheck": true, "noEmit": true, "esModuleInterop": true, "incremental": true, "newLine": "lf"}, "exclude": ["**/node_modules", "**/.*/"]}