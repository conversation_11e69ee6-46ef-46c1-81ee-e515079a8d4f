@import '@/styles/config';

.wrapper {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	gap: spacing(s8);
	padding: spacing(s6);
	border-radius: spacing(s2);
	border: px-to(1px, rem) solid color('grey', 15);
	background-color: color('grey', 10);
	&.multiple {
		gap: px-to(1px, rem);
		padding: 0;
	}
	&.builder {
		gap: 0;
		border: none;
		padding: 0;

		.add__button {
			background-color: color('grey', 10);
			color: color('grey', 60);
			border: px-to(1px, rem) solid color('grey', 15);
			border-top: none;
			padding: spacing(s4) spacing(s5) spacing(s2);
			margin-top: calc(-1 * spacing(s2));
			position: relative;
			z-index: 1;
			justify-content: flex-start;
		}
	}
	& > div {
		padding: 0;
	}

	:global(.accordion) {
		display: grid;
		width: 100%;
		margin: 0 !important;
		gap: px-to(1px, rem);
		border-radius: spacing(s2) spacing(s2) 0 0;
		grid-column: span 12;
		overflow: hidden;
		box-shadow: none !important;
		:global(.accordion__title) {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			text-align: left;
			svg {
				--size: #{spacing(s3)};
			}
			:global(.accordion__title-content) {
				width: 90%;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
		:global(.accordion__item) {
			padding: 0 !important;
			overflow: hidden;
			background-color: color('white', 0);
			outline: none;
		}
		:global(button.accordion__trigger) {
			padding: spacing(s2) spacing(s4);
			gap: spacing(s2);
			svg {
				--size: #{spacing(s4)};
				color: color('grey', 30);
			}
		}
		:global(.accordion__content) {
			background-color: color('white', 5);
		}
		:global(.content__inner) {
			display: grid;
			grid-template-columns: repeat(12, 1fr);
			padding: 0 !important;
		}
	}
}

.add__button {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: spacing(s2) spacing(s4);
	grid-column: span 12;
	background-color: color('white', 0);
	border-radius: 0 0 spacing(s2) spacing(s2);
	cursor: pointer;
	font-weight: 600;
	gap: spacing(s2);
	@include fluid($font-size) {
		font-size: size('button', 'lg');
	}
	svg {
		--size: #{size('button', 'md')};
	}
	&.no__entry {
		flex-direction: column;
		padding: spacing(s6);
		border-radius: spacing(s2);
		border: px-to(1px, rem) solid color('grey', 15);
		svg {
			--size: #{size('button', 'xl')};
		}
	}
}

.remove__button {
	color: color('grey', 30);
	cursor: pointer;
	&:hover {
		color: color('red');
		svg {
			color: color('red');
		}
	}
}

.component {
	&__wrapper {
		display: grid;
		gap: spacing(s2);
		grid-column: span 12;
		padding: spacing(s3) spacing(s5) !important;
		border: px-to(1px, rem) solid color('grey', 15);
		border-radius: spacing(s2);
		background-color: color('white', 0);
		position: relative;
		z-index: 2;
	}

	&__item {
		border-radius: spacing(s2);
		border: px-to(1px, rem) solid color('grey', 20);
		background-color: color('white', 2);
		display: flex;
		align-items: center;
		gap: spacing(s3);
		padding: spacing(s4) spacing(s3) calc(spacing(s4) - 2px);
		overflow: hidden;
		position: relative;
		transition: all 0.2s var(--ease-transition-2);

		&::after {
			content: '';
			background-color: color('yellow', 80);
			position: absolute;
			bottom: 0;
			left: 0;
			width: 0;
			opacity: 0;
			height: px-to(3px, rem);
			transition: all 0.2s var(--ease-transition-2);
		}
		&:hover {
			background-color: color('white', 0);
			&::after {
				width: 100%;
				opacity: 1;
			}
			:global(.accordion__title-content) {
				width: 45%;
			}
			.component__action {
				display: flex;
			}
		}

		:global(.accordion__title-content) {
			font-weight: 600;
			width: 100%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	&__action {
		position: absolute;
		right: spacing(s3);
		top: 50%;
		transform: translateY(-50%);
		display: none;
		align-items: center;
		gap: spacing(s2);
		button {
			--size: #{px-to(16px, rem)};
			width: spacing(s5);
			height: spacing(s5);
			display: flex;
			align-items: center;
			justify-content: center;
			color: color('grey', 90);
			cursor: pointer;
		}
	}

	&__drag {
		color: color('grey', 30);
		--size: #{px-to(16px, rem)};
		cursor: grab;
	}
}
