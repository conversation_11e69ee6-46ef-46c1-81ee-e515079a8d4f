@use 'sass:math';
@use 'sass:string';
@use 'sass:list';
@use 'sass:map';
@use 'sass:meta';

@function to-number($value) {
	@if type-of($value) == 'number' {
		@return $value;
	} @else if type-of($value) != 'string' {
		@warn ('Value for `to-number` should be a number or a string.');
	}

	$result: 0;
	$digits: 0;
	$minus: string.slice($value, 1, 1) == '-';
	$numbers: (
		'0': 0,
		'1': 1,
		'2': 2,
		'3': 3,
		'4': 4,
		'5': 5,
		'6': 6,
		'7': 7,
		'8': 8,
		'9': 9,
	);

	@for $i from if($minus, 2, 1) through str-length($value) {
		$character: string.slice($value, $i, $i);

		@if not(index(map-keys($numbers), $character) or $character== '.') {
			@return to-length(if($minus, -$result, $result), string.slice($value, $i));
		}

		@if $character== '.' {
			$digits: 1;
		} @else if $digits==0 {
			$result: $result * 10 + map.get($numbers, $character);
		} @else {
			$digits: $digits * 10;
			$result: $result + math.div(map.get($numbers, $character), $digits);
		}
	}

	@return if($minus, -$result, $result);
}

@function to-length($value, $unit) {
	$units: (
		'px': 1px,
		'cm': 1cm,
		'mm': 1mm,
		'%': 1%,
		'ch': 1ch,
		'pc': 1pc,
		'in': 1in,
		'em': 1em,
		'rem': 1rem,
		'pt': 1pt,
		'ex': 1ex,
		'vw': 1vw,
		'vh': 1vh,
		'vmin': 1vmin,
		'vmax': 1vmax,
	);

	@if not index(map-keys($units), $unit) {
		@warn ('Invalid unit `#{$unit}`.');
	}

	@return $value * map.get($units, $unit);
}

@function px-to($pxValue, $unit, $base-ratio: 16px) {
	@return math.div($pxValue, $base-ratio) * to-number(1 + #{$unit});
}

$breakpoints: (
	'xs': px-to(320px, rem),
	'sm': px-to(744px, rem),
	'md': px-to(1024px, rem),
	'2md': px-to(1440px, rem),
	'lg': px-to(1920px, rem),
	'xl': px-to(2560px, rem),
) !default;

@mixin min-width($breakpoint) {
	@media (min-width: map.get($map: $breakpoints, $key: $breakpoint)) {
		@content;
	}
}

@mixin max-width($breakpoint) {
	@media not all and (min-width: map.get($map: $breakpoints, $key: $breakpoint)) {
		@content;
	}
}

@mixin range-width($min-width, $max-width) {
	@media (map-get($map: $breakpoints, $key: $min-width) <=width < map-get($map: $breakpoints, $key: $max-width)) {
		@content;
	}
}

// font-family
$font-family: (
	'primary': 'system-ui, sans-serif',
) !default;
$font-size: (
	'@sm': (
		'heading': (
			'h1': 48px,
			'h2': 38px,
			'h3': 32px,
			'h4': 28px,
			'h5': 24px,
			'h6': 20px,
		),

		'paragraph': (
			'lg': 20px,
			'md': 18px,
			'sm': 16px,
		),
		'sub-header': 18px,
		'ui-text': 14px,
		'button': (
			'md': 16px,
			'underline': 14px,
			'sm': 12px,
		),
	),
	'@md': (
		'heading': (
			'h1': 48px,
			'h2': 38px,
			'h3': 32px,
			'h4': 28px,
			'h5': 24px,
			'h6': 20px,
		),

		'paragraph': (
			'lg': 20px,
			'md': 18px,
			'sm': 16px,
		),
		'sub-header': 18px,
		'ui-text': 14px,
		'button': (
			'md': 16px,
			'underline': 14px,
			'sm': 12px,
		),
	),
) !default;
$spacing: (
	's0': 0,
	's1': 4px,
	's2': 8px,
	's3': 12px,
	's4': 16px,
	's5': 20px,
	's6': 24px,
	's7': 28px,
	's8': 32px,
	's9': 36px,
	's10': 44px,
	's11': 64px,
	's12': 88px,
	's13': 120px,
	's14': 160px,
) !default;

@function spacing($keys...) {
	@return px-to(map.get($spacing, $keys...), 'rem');
}

@mixin fluid($fluid-types) {
	$list-breakpoints: ();
	$list-values: () !global;

	@each $breakpoint, $value in $fluid-types {
		$breakpoint-value: map.get($breakpoints, string.slice($breakpoint, 2, -1));
		$list-breakpoints: list.append($list-breakpoints, $breakpoint-value);
		$list-values: list.append($list-values, $value) !global;
	}

	$breakpoint-small: to-number(list.nth($list-breakpoints, 1)) !global;
	$breakpoint-large: to-number(list.nth($list-breakpoints, 2)) !global;

	@content;
}

@function size($type, $keys...) {
	@if $list-values {
		$value-small: px-to(map.get(list.nth($list-values, 1), $type, $keys...), 'rem');
		$value-large: px-to(map.get(list.nth($list-values, 2), $type, $keys...), 'rem');
		$y-int: math.div($value-large - $value-small, $breakpoint-large - $breakpoint-small);
		$y: $y-int * 100vw;
		$x: $value-small - $y-int * $breakpoint-small;

		@return clamp(#{$value-small}, calc(#{$x} + #{$y}), #{$value-large});
	}

	@return map.get($font-sizes, $type, '@lg', $keys...);
}

@function set-lightness($colors, $alpha: 1) {
	@if $colors {
		@return rgba($color: $colors, $alpha: $alpha);
	}

	@return $colors;
}

// colors
$colors: (
	'default': (
		'primary': #ffc700,
		'neutral': (
			'100': #000,
			'30': #454545,
			'60': #9e9e9e,
			'70': #b4b4b4,
			'80': #d9d9d9,
			'90': #f5f5f5,
		),
		'black': #000,
		'white': #fff,
		'error': (
			'100': #d02b20,
		),
		'success': (
			'100': #1f9311,
		),
		'surface': (
			'100': #f7f7f7,
			'90': #e8e8e8,
		),
		'blue': #009ad8,
	),
	'dark': (
		'primary': #1f9311,
	),
) !default;

// border-radius
$border-radius: (
	'sm': 0.25em,
	'2md': 0.313em,
	'md': 0.5em,
	'2lg': 0.625em,
	'lg': 0.75em,
) !default;

// z-index
$z-index: (
	'header': '3',
	'popover': '5',
	'fixed-element': '10',
	'overlay': '15',
) !default;

@mixin theme {
	@each $theme, $map in $colors {
		:global(.theme--#{$theme}) & {
			$theme-map: () !global;

			@each $key, $submap in $map {
				$value: map.get(map.get($colors, $theme), '#{$key}');
				$theme-map: map.merge(
					$theme-map,
					(
						$key: $value,
					)
				) !global;
			}

			@content;

			$theme-map: null !global;
		}
	}
}

@mixin gtheme {
	@each $theme, $map in $colors {
		.theme--#{$theme} & {
			$theme-map: () !global;

			@each $key, $submap in $map {
				$value: map.get(map.get($colors, $theme), '#{$key}');
				$theme-map: map.merge(
					$theme-map,
					(
						$key: $value,
					)
				) !global;
			}

			@content;

			$theme-map: null !global;
		}
	}
}

@function color($keys...) {
	// Check theme-map first
	@if variable-exists(theme-map) and $theme-map {
		@if map.has-key($theme-map, $keys...) {
			$val: map.get($theme-map, $keys...);
			@if type-of($val) == 'map' {
				@warn "Color for keys: #{$keys} is a map, not a valid CSS value. Returning null.";
				@return null;
			}
			@return $val;
		}
	}

	// Check colors map next
	@if variable-exists(colors) and $colors {
		@if map.has-key($colors, 'default') and map.has-key(map.get($colors, 'default'), $keys...) {
			$val: map.get($colors, 'default', $keys...);
			@if type-of($val) == 'map' {
				@warn "Color for keys: #{$keys} is a map, not a valid CSS value. Returning null.";
				@return null;
			}
			@return $val;
		}
	}

	// If no valid key is found
	@warn "Color not found for keys: #{$keys}. Returning null.";
	@return null;
}

@mixin center-layout() {
	display: flex;
	align-items: center;
	justify-content: center;
}

@mixin border($top, $right, $bottom, $left, $style, $color) {
	border-top: $top $style $color;
	border-right: $right $style $color;
	border-bottom: $bottom $style $color;
	border-left: $left $style $color;
}

// position
@mixin position($type, $top, $right, $bottom, $left) {
	position: $type;
	inset: $top $right $bottom $left;
}

@function encodecolor($string) {
	@if type-of($string) == 'color' {
		$hex: string.slice(color.ie-hex-str($string), 4);
		$string: string.unquote('#{$hex}');
	}

	$string: '%23' + $string;

	@return $string;
}
