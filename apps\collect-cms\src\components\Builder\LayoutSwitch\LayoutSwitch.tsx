import { Icon, useWindowDimensions } from '@collective/core'
import cn from 'classnames'
import { useIsomorphicLayoutEffect } from 'framer-motion'
import { useContext, useRef, useState, type RefObject } from 'react'
import { createPortal } from 'react-dom'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './layoutswitch.module.scss'

export const LayoutSwitch = () => {
	const context = useContext(PageBuilderContext)
	const { isPreview, setIsPreview } = context
	const [showLayout, setShowLayout] = useState(false)
	const triggerRef = useRef<HTMLButtonElement>(null)

	return (
		<>
			<div className={styles.wrapper}>
				<div className={styles.inner}>
					<div className={cn(styles.bg, isPreview ? styles.preview : styles.edit)} />
					<button
						ref={triggerRef}
						className={cn(isPreview && styles.active)}
						onClick={() => {
							setIsPreview(true)
							isPreview && setShowLayout(!showLayout)
						}}
					>
						<Icon variant="eye" type="cms" />
						{isPreview && <Icon className={styles.chevron} variant="chevron-down" type="cms" />}
					</button>
					<button
						className={cn(!isPreview && styles.active)}
						onClick={() => {
							setIsPreview(false)
						}}
					>
						<Icon variant="edit" type="cms" />
					</button>
				</div>
			</div>
			{showLayout &&
				createPortal(
					<DeviceList onClose={() => setShowLayout(false)} holder={triggerRef} />,
					document.body
				)}
		</>
	)
}

const DeviceList = ({
	holder,
	onClose,
}: {
	holder: RefObject<HTMLButtonElement>
	onClose: () => void
}) => {
	const context = useContext(PageBuilderContext)
	const { screenTypes, screenSize, setScreenSize } = context
	const element = useRef<HTMLDivElement>(null)
	const windowDimension = useWindowDimensions()

	useIsomorphicLayoutEffect(() => {
		if (!holder.current || !element.current) return

		const container = holder.current
		const menu = element.current
		const { left, top, height, width } = container.getBoundingClientRect()

		menu.focus()
		menu.style.left = `${left}px`
		menu.style.top = `${top + height}px`
		menu.style.width = `${width}px`
	}, [holder, element, windowDimension])

	return (
		<div
			role="menu"
			tabIndex={0}
			ref={element}
			className={styles.device__list}
			onBlur={(e) =>
				e.relatedTarget !== holder.current && !e.target.contains(e.relatedTarget) && onClose()
			}
		>
			{screenTypes &&
				Object.entries(screenTypes).map((item, idx) => (
					<button
						className={cn(item[1] === screenSize && styles.active)}
						key={idx}
						onClick={() => {
							setScreenSize(item[1])
							onClose()
						}}
					>
						<Icon variant={item[0]} type="cms" />
					</button>
				))}
		</div>
	)
}
