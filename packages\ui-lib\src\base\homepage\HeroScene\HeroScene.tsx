import { Image } from '@collective/core'
import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import styles from './heroscene.module.scss'

type HeroSceneProps = {
	cid?: string
	Media: IMediaProps
}

export const HeroScene = ({ cid, Media }: HeroSceneProps) => (
	<section id={cid} className={cn(styles.wrapper)}>
		<Image className={cn(styles.media)} media={Media} alt="" />
	</section>
)
