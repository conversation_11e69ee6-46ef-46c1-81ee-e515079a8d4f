import React from 'react';
import { type OptionData } from '../Select';
type IconProps = {
    startIcon: React.ReactElement;
    endIcon?: never;
} | {
    endIcon: React.ReactElement;
    startIcon?: never;
} | {
    endIcon?: undefined;
    startIcon?: undefined;
};
type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
    label: string;
    placeholder: string;
    required: boolean;
} & IconProps;
export declare const Multiple: React.ForwardRefExoticComponent<ButtonProps & React.RefAttributes<HTMLButtonElement>>;
export declare function MultipleItem({ value, children, className, }: {
    value: string;
    children?: React.ReactNode | ((data: OptionData) => React.ReactNode);
    className?: string;
}): import("react/jsx-runtime").JSX.Element;
export {};
