# CollectCMS Application Flow

This document details the main user flows and processes within the CollectCMS application.

## Table of Contents

1. [Authentication Flow](#authentication-flow)
2. [Content Management Flow](#content-management-flow)
3. [Page Builder Flow](#page-builder-flow)
4. [Navigation Flow](#navigation-flow)
5. [Data Fetching Flow](#data-fetching-flow)

## Authentication Flow

```mermaid
flowchart TD
    A[User Access] --> B{Has Valid Token?}
    B -->|Yes| C[Access Protected Routes]
    B -->|No| D[Redirect to Login]
    D --> E[Enter Credentials]
    E --> F{Valid Credentials?}
    F -->|Yes| G[Store Token]
    G --> C
    F -->|No| H[Show Error]
    H --> E
```

1. **Initial Access**:
   - User attempts to access a protected route
   - Middleware checks for a valid JWT token in cookies

2. **Authentication Check**:
   - If token exists and is valid, user proceeds to the requested route
   - If no token or invalid token, user is redirected to login page

3. **Login Process**:
   - User enters email and password
   - Credentials are sent to Strapi backend
   - If valid, JWT token is returned and stored in:
     - Cookies (for server-side authentication)
     - LocalStorage or SessionStorage (based on "Remember me" option)
   - User is redirected to the originally requested page

4. **Session Management**:
   - Token expiration is handled based on "Remember me" option
   - User profile information is stored for display in the UI

## Content Management Flow

```mermaid
flowchart TD
    A[Access Content Manager] --> B[Select Content Type]
    B --> C[View Content List]
    C --> D{Action?}
    D -->|Create| E[Open Create Form]
    D -->|Edit| F[Open Edit Form]
    D -->|Delete| G[Confirm Delete]
    E --> H[Fill Form]
    F --> H
    H --> I[Save Content]
    I --> J[Update Content List]
    G -->|Confirm| K[Delete Content]
    K --> J
```

1. **Content Type Selection**:
   - User navigates to Content Manager
   - Selects a content type from the sidebar navigation

2. **Content Listing**:
   - System fetches and displays a list of content entries
   - Pagination, sorting, and filtering options are available

3. **Content Operations**:
   - **Create**: User clicks "Create new entry" and fills the form
   - **Edit**: User selects an existing entry and modifies it
   - **Delete**: User selects an entry and confirms deletion

4. **Form Handling**:
   - Dynamic forms are generated based on content type schema
   - Different field types have specialized editors
   - Validation is performed before submission

5. **Data Persistence**:
   - Form data is sent to Strapi backend
   - Success/error messages are displayed
   - List view is updated with the changes

## Page Builder Flow

```mermaid
flowchart TD
    A[Access Page Builder] --> B[Select Content]
    B --> C[Load Page Builder Interface]
    C --> D[Edit Page Structure]
    D --> E{Action?}
    E -->|Add Component| F[Select Component Type]
    E -->|Edit Component| G[Open Component Editor]
    E -->|Reorder| H[Drag and Drop]
    E -->|Delete| I[Remove Component]
    F --> J[Configure Component]
    G --> J
    J --> K[Preview Changes]
    H --> K
    I --> K
    K --> L[Save Page]
    L --> M[Publish or Save Draft]
```

1. **Page Selection**:
   - User navigates to Page Builder
   - Selects a page to edit

2. **Builder Interface**:
   - Layout editor loads with the current page structure
   - Components are displayed in a visual representation

3. **Component Operations**:
   - **Add**: User selects a component type from the menu
   - **Edit**: User clicks on a component to modify its properties
   - **Reorder**: User drags components to change their order
   - **Delete**: User removes unwanted components

4. **Component Configuration**:
   - Each component has a specific editor with relevant fields
   - Changes are reflected in the visual preview

5. **Page Saving**:
   - User saves changes to the page
   - Option to publish immediately or save as draft

## Navigation Flow

```mermaid
flowchart TD
    A[Application Start] --> B[Load Navigation Data]
    B --> C[Render Sidebar]
    C --> D[User Selects Navigation Item]
    D --> E{Item Type?}
    E -->|Content Type| F[Navigate to Content Type List]
    E -->|Single Type| G[Navigate to Single Type Editor]
    E -->|Group| H[Expand Group]
    H --> D
    F --> I[Content Manager Flow]
    G --> J[Content Editor Flow]
```

1. **Navigation Loading**:
   - Navigation structure is loaded from the backend or mock data
   - Sidebar is rendered with groups and items

2. **Navigation Structure**:
   - Groups contain content types and can be expanded/collapsed
   - Items can be pinned for quick access

3. **Navigation Selection**:
   - User selects an item from the navigation
   - System routes to the appropriate view based on item type:
     - Collection Type: List view of entries
     - Single Type: Direct editor for the single entry

## Data Fetching Flow

```mermaid
sequenceDiagram
    participant Component
    participant API Layer
    participant Strapi
    
    Component->>API Layer: Request Data
    API Layer->>API Layer: Construct URL with Parameters
    API Layer->>API Layer: Add Authentication Headers
    API Layer->>Strapi: Make API Request
    Strapi->>API Layer: Return Response
    API Layer->>API Layer: Process Response
    API Layer->>Component: Return Formatted Data
    Component->>Component: Update UI
```

1. **Request Initiation**:
   - Component needs data (e.g., content types, entries)
   - Calls appropriate function from API layer

2. **Request Configuration**:
   - API function constructs the URL with parameters
   - Adds authentication token to headers
   - Sets caching and revalidation options

3. **API Communication**:
   - Request is sent to Strapi backend
   - Response is received and processed

4. **Data Handling**:
   - Formatted data is returned to the component
   - Component updates its state and UI
   - Error handling for failed requests
