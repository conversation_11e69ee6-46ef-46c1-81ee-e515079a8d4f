import { getRandomInt } from './get-random-int';
export class ArrayUtils {
    static getRandom(items) {
        if (items.length === 0) {
            throw new Error('Array is empty');
        }
        return items[getRandomInt(0, items.length - 1)];
    }
    static getItem(arr, item) {
        const index = arr.indexOf(item);
        if (index > -1) {
            return arr[index];
        }
        return undefined;
    }
    static updateItem(arr, item, data) {
        const index = arr.indexOf(item);
        if (index > -1) {
            arr[index] = data;
        }
        return arr;
    }
    static removeItem(arr, item) {
        const index = arr.indexOf(item);
        if (index > -1) {
            arr.splice(index, 1);
        }
        return arr;
    }
    static groupBy(array, propertyName) {
        return array.reduce((accumulator, obj) => {
            const key = obj[propertyName];
            if (!accumulator[key]) {
                accumulator[key] = [];
            }
            accumulator[key].push(obj);
            return accumulator;
        }, {});
    }
}
