'use client'

import type { GeneralSettingProps } from '@collective/integration-lib/search'
import { createContext } from 'react'

const defaultContext: GeneralSettingProps = {
	locale: 'en',
	Social: [],
}

export const GeneralSettingContext = createContext(defaultContext)

export default function GeneralSettingProvider({
	data,
	children,
}: {
	data: GeneralSettingProps
	children: React.ReactNode
}) {
	return <GeneralSettingContext.Provider value={data}>{children}</GeneralSettingContext.Provider>
}
