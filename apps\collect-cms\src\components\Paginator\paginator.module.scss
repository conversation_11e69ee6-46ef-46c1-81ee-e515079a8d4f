@import '@/styles/config';

.wrapper {
	display: grid;
	grid-template-columns: 1fr 2fr 1fr;
	align-items: center;
	gap: spacing(s2);

	button {
		cursor: pointer;
	}

	.navigation {
		place-self: center;
		align-items: center;
		display: flex;
		gap: spacing(s4);
	}

	.pages {
		display: flex;

		li {
			width: px-to(30px, rem);
			height: px-to(30px, rem);
			text-align: center;

			&.current__pagination button {
				background-color: color('white', 0);
			}
		}
		button {
			width: 100%;
			height: 100%;
			display: grid;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			transition: 0.2s;
		}
	}

	.page__options {
		display: flex;
		align-items: center;
		gap: spacing(s5);
		justify-content: flex-end;

		> div {
			display: flex;
			gap: spacing(s4);
			align-items: center;
		}

		.input {
			--input-height: #{px-to(35px, rem)};
			--input-radius: #{px-to(6px, rem)};
			--input-padding-x: #{spacing(s2)};
			--input-padding-y: #{spacing(s2)};
			--input-bg: #{color('white', 0)};
			--input-border-color: #{color('grey', 30)};

			font-size: px-to(14px, rem);

			input {
				width: calc(1.25 * var(--input-height));
			}

			input,
			button {
				box-shadow: inset 0 0 0 2px var(--input-border-color);
			}
		}
	}
}

div.dropdown {
	--dropdown-bg-color: #{color('white', 0)};
	--dropdown-bg-active-color: var(--collect-primary-color-80);

	div[role='button'] {
		font-weight: 400;
		@include fluid($font-size) {
			font-size: size('body', 'md');
		}
	}
}
