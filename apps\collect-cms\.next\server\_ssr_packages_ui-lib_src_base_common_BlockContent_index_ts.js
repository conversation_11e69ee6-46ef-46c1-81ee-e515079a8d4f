/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockContent_index_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockContent_index_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx":
/*!***************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContent: () => (/* binding */ BlockContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockcontent.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss\");\n/* harmony import */ var _blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst BlockContent = ({ cid, Headline, Variant, Blocks, isFirstSection })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper), Headline ? \"\" : \"nohead\", isFirstSection ? \"first__section\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: [\n                Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading\"),\n                    children: Headline\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 17\n                }, undefined),\n                Blocks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content), Variant === \"grid\" ? (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content__grid) : \"\"),\n                    style: {\n                        \"--isHasHeadline\": Headline ? 0 : 7\n                    },\n                    children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block)),\n                            children: [\n                                item.Title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\", (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title)),\n                                    children: item.Title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 9\n                                }, undefined),\n                                item.Content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                        rehypePlugins: [\n                                            rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                        ],\n                                        children: item.Content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 10\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 7\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n            lineNumber: 27,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n        lineNumber: 23,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/index.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/index.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContent: () => (/* reexport safe */ _BlockContent__WEBPACK_IMPORTED_MODULE_0__.BlockContent)\n/* harmony export */ });\n/* harmony import */ var _BlockContent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContent */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRlbnQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0Jsb2NrQ29udGVudC9pbmRleC50cz80M2JiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vQmxvY2tDb250ZW50J1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontent_wrapper__8O_bU\",\n\t\"headline\": \"blockcontent_headline__cMJyZ\",\n\t\"content\": \"blockcontent_content__KH33h\",\n\t\"content__grid\": \"blockcontent_content__grid__GajH2\",\n\t\"block\": \"blockcontent_block__8eue2\",\n\t\"block__title\": \"blockcontent_block__title__Ck2bf\",\n\t\"block__content\": \"blockcontent_block__content__CS7WJ\"\n};\n\nmodule.exports.__checksum = \"7e7468d3bd30\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRlbnQvYmxvY2tjb250ZW50Lm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250ZW50L2Jsb2NrY29udGVudC5tb2R1bGUuc2Nzcz8yZGM3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2NvbnRlbnRfd3JhcHBlcl9fOE9fYlVcIixcblx0XCJoZWFkbGluZVwiOiBcImJsb2NrY29udGVudF9oZWFkbGluZV9fY01KeVpcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250ZW50X2NvbnRlbnRfX0tIMzNoXCIsXG5cdFwiY29udGVudF9fZ3JpZFwiOiBcImJsb2NrY29udGVudF9jb250ZW50X19ncmlkX19HYWpIMlwiLFxuXHRcImJsb2NrXCI6IFwiYmxvY2tjb250ZW50X2Jsb2NrX184ZXVlMlwiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGVudF9ibG9ja19fdGl0bGVfX0NrMmJmXCIsXG5cdFwiYmxvY2tfX2NvbnRlbnRcIjogXCJibG9ja2NvbnRlbnRfYmxvY2tfX2NvbnRlbnRfX0NTN1dKXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjdlNzQ2OGQzYmQzMFwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss\n");

/***/ })

};
;