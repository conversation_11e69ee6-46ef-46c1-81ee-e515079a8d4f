"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/contexts/BuilderContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/BuilderContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageBuilderContext: function() { return /* binding */ PageBuilderContext; },\n/* harmony export */   PageBuilderProvider: function() { return /* binding */ PageBuilderProvider; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ObjectUtils!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/utils/object-util.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mock_Builder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/mock/Builder */ \"(app-pages-browser)/./src/mock/Builder.ts\");\n/* __next_internal_client_entry_do_not_use__ PageBuilderContext,PageBuilderProvider auto */ \n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\nvar PageBuilderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext({});\nvar PageBuilderProvider = function(props) {\n    _s();\n    var _props_value;\n    var propsValue = (_props_value = props.value) !== null && _props_value !== void 0 ? _props_value : {};\n    var _propsValue_globals;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_globals = propsValue.globals) !== null && _propsValue_globals !== void 0 ? _propsValue_globals : {}), 1), globals = _useState[0];\n    var _propsValue_data;\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_data = propsValue.data) !== null && _propsValue_data !== void 0 ? _propsValue_data : {}), 2), data = _useState1[0], setData = _useState1[1];\n    var _propsValue_contentType;\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_contentType = propsValue.contentType) !== null && _propsValue_contentType !== void 0 ? _propsValue_contentType : {}), 1), contentType = _useState2[0];\n    var _propsValue_components;\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_components = propsValue.components) !== null && _propsValue_components !== void 0 ? _propsValue_components : {}), 1), components = _useState3[0];\n    var _propsValue_locale;\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_locale = propsValue.locale) !== null && _propsValue_locale !== void 0 ? _propsValue_locale : \"en\"), 1), locale = _useState4[0];\n    var _useState5 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propsValue.configuration), 1), configuration = _useState5[0];\n    var _useState6 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}), 2), historyChanges = _useState6[0], setHistoryChanges = _useState6[1];\n    var _useState7 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isPreview = _useState7[0], setIsPreview = _useState7[1];\n    var _propsValue_slug;\n    var _useState8 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_slug = propsValue.slug) !== null && _propsValue_slug !== void 0 ? _propsValue_slug : []), 1), slug = _useState8[0];\n    var _useState9 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        left: true,\n        right: true\n    }), 2), expandedSidebar = _useState9[0], setExpandedSidebar = _useState9[1];\n    var _propsValue_screenTypes;\n    var _useState10 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_screenTypes = propsValue.screenTypes) !== null && _propsValue_screenTypes !== void 0 ? _propsValue_screenTypes : _mock_Builder__WEBPACK_IMPORTED_MODULE_3__.defaultConfig.screenTypes), 1), screenTypes = _useState10[0];\n    var _useState11 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Object.values(screenTypes)[0]), 2), screenSize = _useState11[0], setScreenSize = _useState11[1];\n    var _useState12 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now()), 2), updatedAt = _useState12[0], setUpdatedAt = _useState12[1];\n    var _useState13 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key: \"\",\n        id: -1\n    }), 2), editingIden = _useState13[0], setEditingIden = _useState13[1];\n    var _useState14 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), childComponentData = _useState14[0], setChildComponentData = _useState14[1];\n    var _useState15 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), layerPos = _useState15[0], setLayerPos = _useState15[1];\n    // Normalize data input\n    var normalizedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        var commonData = data.data;\n        var orgData = commonData !== null && commonData !== void 0 ? commonData : {};\n        var _ObjectUtils_elevateProperty = _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__.ObjectUtils.elevateProperty(orgData, \"data\"), components = _ObjectUtils_elevateProperty.components, _$props = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__._)(_ObjectUtils_elevateProperty, [\n            \"components\"\n        ]);\n        var isTempKeyExisted = components.some(function(component) {\n            return component.__temp_key__;\n        });\n        return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, _$props), {\n            components: isTempKeyExisted ? components : components.map(function(component, index) {\n                return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, component), {\n                    __temp_key__: index + 1\n                });\n            })\n        });\n    }, [\n        data\n    ]);\n    var value = {\n        globals: globals,\n        components: components,\n        data: data,\n        setData: setData,\n        contentType: contentType,\n        locale: locale,\n        configuration: configuration,\n        historyChanges: historyChanges,\n        setHistoryChanges: setHistoryChanges,\n        isPreview: isPreview,\n        setIsPreview: setIsPreview,\n        slug: slug,\n        expandedSidebar: expandedSidebar,\n        setExpandedSidebar: setExpandedSidebar,\n        screenTypes: screenTypes,\n        screenSize: screenSize,\n        setScreenSize: setScreenSize,\n        updatedAt: updatedAt,\n        setUpdatedAt: setUpdatedAt,\n        editingIden: editingIden,\n        setEditingIden: setEditingIden,\n        normalizedData: normalizedData,\n        childComponentData: childComponentData,\n        setChildComponentData: setChildComponentData,\n        layerPos: layerPos,\n        setLayerPos: setLayerPos\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageBuilderContext.Provider, {\n        value: value,\n        children: props.children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\contexts\\\\BuilderContext.tsx\",\n        lineNumber: 128,\n        columnNumber: 9\n    }, _this);\n};\n_s(PageBuilderProvider, \"AvNW/sbNVbpvSH6vJuoyI8wHyzA=\");\n_c = PageBuilderProvider;\nvar _c;\n$RefreshReg$(_c, \"PageBuilderProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/BuilderContext.tsx\n"));

/***/ })

});