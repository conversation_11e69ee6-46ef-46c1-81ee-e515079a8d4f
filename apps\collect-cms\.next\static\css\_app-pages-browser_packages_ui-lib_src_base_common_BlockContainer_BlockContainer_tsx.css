/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockcontainer_wrapper__9pM15 {
  margin-bottom: var(--section-mg-btm);
}
.blockcontainer_wrapper__9pM15 .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .blockcontainer_wrapper__9pM15 .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockcontainer_wrapper__9pM15 .blockcontainer_content__CVQDR {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .blockcontainer_wrapper__9pM15 .blockcontainer_content__CVQDR {
    grid-column-start: var(--position);
    grid-column-end: span var(--total);
    grid-template-columns: repeat(var(--total), 1fr);
  }
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__FgkM7 {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
  grid-column: span var(--layout);
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ {
  color: #a8a8a8;
  display: flex;
  gap: 0.5rem;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ h4 {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ svg {
  --size: 1.125rem;
  margin-top: 0.125rem;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc i,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc em {
  font-style: italic;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc b,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc strong {
  font-weight: 700;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc a,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc del {
  text-decoration: line-through;
}
.blockcontainer_wrapper__9pM15 .block__green {
  background-color: rgba(105, 192, 105, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__green .blockcontainer_block__title__yzrl_ {
  color: #69c069;
}
.blockcontainer_wrapper__9pM15 .block__red {
  background-color: rgba(238, 29, 82, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__red .blockcontainer_block__title__yzrl_ {
  color: #ee1d52;
}
.blockcontainer_wrapper__9pM15 .block__yellow {
  background-color: rgba(195, 127, 0, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__yellow .blockcontainer_block__title__yzrl_ {
  color: #c37f00;
}
