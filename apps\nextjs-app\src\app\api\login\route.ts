import type { IJwtBaseProps, IUserJWTProps } from '@collective/core'
import { postJsonData, type StrapiLoginUserProps } from '@collective/integration-lib/cms'
import * as jose from 'jose'
import type { NextRequest } from 'next/server'

const PRIVATE_KEY_JWK = {
	kty: 'EC',
	x: 'EY9lKsuie8a2G-9S3R6arvFOxW12DXkqihffgr2AMp4',
	y: 'BZhaUk4HBCVeJ2Lf2Uu4jhlirErcJbNSF1DMQ5CaetQ',
	crv: 'P-256',
	d: 'liqFcqRRDsYZKLwgwcAh0srGCcuyMIse1lyUQicoAhU',
}

// export const runtime = 'edge'
export async function POST(request: NextRequest) {
	const res = await request.json()
	if (res.identifier === undefined || res.password === undefined) {
		return Response.json({ title: 'Invalid email or password' }, { status: 400 })
	}
	const privateKey = await jose.importJWK(PRIVATE_KEY_JWK, 'ES256')
	try {
		const loginData = await postJsonData<StrapiLoginUserProps>(
			`auth/local`,
			JSON.stringify({
				identifier: res.identifier,
				password: res.password,
			})
		)
		const strapiJwt = loginData.jwt
		const { id, email, fullName, username, avatar } = loginData.user
		const userData: IJwtBaseProps & IUserJWTProps = {
			id,
			fullName,
			username,
			email,
			avatar,
			strapiToken: strapiJwt,
			exp: new Date().getTime() + 7 * 24 * 60 * 60 * 1000, // 7 days
			nbf: new Date().getTime(), // now
			iat: new Date().getTime(), // now
		}

		const jws = await new jose.CompactSign(new TextEncoder().encode(JSON.stringify(userData)))
			.setProtectedHeader({ alg: 'ES256' })
			.sign(privateKey)

		return Response.json({ title: 'Login success', token: jws }, { status: 200 })
	} catch (error) {
		const errorData = JSON.parse(error as string)

		// Handle error response
		if (errorData.error.message === 'Invalid identifier or password') {
			return Response.json({ title: 'Invalid email or password' }, { status: 400 })
		} else {
			return Response.json({ title: 'Internal server error' }, { status: 500 })
		}
	}
}
