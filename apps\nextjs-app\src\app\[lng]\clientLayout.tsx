'use client'

import type { INavigationProps } from '@collective/integration-lib/cms'
import ChatListProvider from '@collective/ui-lib/contexts/ChatListContext'
import { ModalProvider } from '@collective/ui-lib/contexts/ModalContext'
import NavigationProvider from '@collective/ui-lib/contexts/NavigationContext'
import { ToastProvider } from '@collective/ui-lib/contexts/ToastContext'
import { usePathname } from 'next/navigation'
import { BubbleChat } from '@/components/BubbleChat/BubbleChat'
import { LightBox } from '@/components/LightBox'
import { Modal } from '@/components/Modal/Modal'
import { Sidebar } from '@/components/Sidebar/Sidebar'
import { Toast } from '@/components/Toast/Toast'

export default function ClientLayout({
	children,
	pagesData,
	lng,
	uuid,
}: Readonly<{
	children: React.ReactNode
	pagesData: INavigationProps[]
	uuid: string
	lng: string
}>) {
	const pathname = usePathname()
	return (
		<NavigationProvider data={pagesData}>
			<ModalProvider>
				<ToastProvider>
					<ChatListProvider>
						<Sidebar />
						<main className="aidigi__main">{children}</main>
						{pathname !== '/cosmo' && <BubbleChat uuid={uuid} />}
						<Modal />
						<Toast />
						<LightBox curPath={pathname} />
					</ChatListProvider>
				</ToastProvider>
			</ModalProvider>
		</NavigationProvider>
	)
}
