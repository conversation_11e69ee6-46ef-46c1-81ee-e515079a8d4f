'use client'

import { useCallback, useState } from 'react'
import { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'

export function useWindowDimensions() {
	const hasWindow = typeof window !== 'undefined'

	const getWindowDimensions = useCallback(() => {
		const width = hasWindow ? window.innerWidth : null
		const height = hasWindow ? window.innerHeight : null
		return {
			width,
			height,
		}
	}, [hasWindow])

	const [windowDimensions, setWindowDimensions] = useState(getWindowDimensions())

	useIsomorphicLayoutEffect(() => {
		function handleResize() {
			setWindowDimensions(getWindowDimensions())
		}

		if (hasWindow) {
			handleResize()
			window.addEventListener('resize', handleResize)
			return () => window.removeEventListener('resize', handleResize)
		}
	}, [getWindowDimensions, hasWindow])

	return windowDimensions
}
