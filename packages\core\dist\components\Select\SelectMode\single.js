import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import React, { useContext } from 'react';
import styles from '../../Input/input.module.scss';
import { SelectContext } from '../Select';
import styleSelect from '../select.module.scss';
export const Single = React.forwardRef(({ id, className, startIcon, endIcon, label, required }, ref) => {
    const { option, setIsShow, isShow } = useContext(SelectContext);
    const defaultOption = option;
    return (_jsxs("div", { className: cn('form__wrapper', styles['form-control-wrapper'], className, startIcon && styles['form-control-wrapper--icon-left'], endIcon && styles['form-control-wrapper--icon-right'], isShow && 'is__triggered'), children: [label && (_jsxs("span", { className: styles.label, children: [label, required && _jsx("span", { className: styles.mark, children: "*" })] })), _jsxs("div", { className: styles.wrapper, children: [startIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--left'], 'icon--left'), children: startIcon })), _jsx("button", { id: id, type: "button", className: cn(styles['form-control']), onClick: () => setIsShow(!isShow), ref: ref, children: _jsx("span", { className: "form-label", children: defaultOption.label }) }), endIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--right'], 'icon--right'), children: endIcon }))] })] }));
});
export function SingleItem({ value, children, className }) {
    const { option, setOption, setIsShow, onChange } = useContext(SelectContext);
    const defaultOption = option;
    const handleChange = (optionValue, optionLabel) => {
        setIsShow(false);
        if (defaultOption.value.toString() === optionValue)
            return;
        const currentOption = {
            value: optionValue,
            label: optionLabel,
        };
        setOption(currentOption);
        onChange?.(currentOption);
    };
    const optionData = {
        isActive: defaultOption.value.toString() === value,
    };
    const RenderChildren = () => (typeof children === 'function' ? children(optionData) : children);
    return (_jsx("div", { role: "button", tabIndex: 0, className: cn(styleSelect.option, defaultOption.value.toString() === value && styleSelect.active, defaultOption.value.toString() === value && 'option__active', className), onClick: () => handleChange(value, children), children: _jsx(RenderChildren, {}) }));
}
