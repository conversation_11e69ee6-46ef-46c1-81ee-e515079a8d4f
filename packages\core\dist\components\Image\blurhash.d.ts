export interface BlurHashOptions {
    /**
     * Aspect ratio (width / height) of the BlurHash image to be decoded.
     *
     * @default 1 (square aspect ratio)
     */
    ratio?: number;
    /**
     * The size of the longer edge (width or height) of the BlurHash image to be
     * decoded, depending on the aspect ratio.
     *
     * Next.js recommend 10px or less for the size.
     * Also anything more than 10px will cause the page to load slowly.
     *
     * https://nextjs.org/docs/pages/api-reference/components/image#blurdataurl
     *
     * @default 4
     */
    size?: number;
}
export declare function createPngDataUri(hash?: string | null, { ratio, size }?: BlurHashOptions): string;
