import { Icon, Accordion, AccordionItem, useIsomorphicLayoutEffect } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import { usePathname } from 'next/navigation'
import { useContext, useState, useMemo } from 'react'
import { PageBuilderContext, type Entry } from '@/contexts/BuilderContext'
import { FieldEditor, type FieldProps } from '../../FieldEditor'
import styles from './component.module.scss'

export interface ComponentProps<T> extends FieldProps<T> {
	value?: T
	component?: string
	repeatable?: boolean
	onChange: (props: { field: string; value: unknown }) => void
}

const isArray = (value: unknown) => Array.isArray(value)

export const Component = <T,>(props: ComponentProps<T>) => {
	const context = useContext(PageBuilderContext)
	const pathname = usePathname()
	const {
		components,
		globals,
		childComponentData,
		setChildComponentData,
		layerPos: contextLayerPos,
		setLayerPos,
	} = context
	const fieldSizes = globals.data.fieldSizes
	const { value, onChange, name, component, repeatable, layerPos } = props
	const [propsValue, setPropsValue] = useState(isArray(value) ? (value ?? []) : (value ?? Object))
	const cmpData = components.data.find((item) => item.uid === component)
	// Filter for object-type attributes only
	const filteredComponents = (obj: IComponentProps) =>
		Object.entries(obj || {}).filter(([, value]) => typeof value === 'object' && value !== null)

	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])

	useIsomorphicLayoutEffect(() => {
		props.value !== propsValue && setPropsValue(props.value as unknown as NonNullable<T>)
	}, [props.value])

	if (!cmpData) return null

	const handleAdd = () => {
		if (repeatable && isArray(propsValue)) {
			// Create empty entry for repeatable array
			const newEntry = {}
			const newValue = [...propsValue, newEntry]
			setPropsValue(newValue as unknown as NonNullable<T>)
			onChange({ field: name as string, value: newValue })
		} else {
			// Create empty entry for single object
			const newEntry = {}
			setPropsValue(newEntry as unknown as NonNullable<T>)
			onChange({ field: name as string, value: newEntry })
		}
	}

	const handleRemove = (idx: number) => {
		console.log(idx)
		const childCmp = childComponentData
		if (repeatable && isArray(propsValue)) {
			const newValue = [...propsValue]
			newValue.splice(idx, 1)
			console.log('delete target:', propsValue[idx], childComponentData)
			setPropsValue(newValue as unknown as NonNullable<T>)
			onChange({ field: name as string, value: newValue })
			setChildComponentData(childCmp.filter((item) => item.value !== propsValue[idx]))
		} else {
			setPropsValue('' as unknown as NonNullable<T>)
			onChange({ field: name as string, value: null })
			childCmp.pop()
			setChildComponentData(childCmp)
		}
	}

	const handleDuplicate = (idx: number) => {
		console.log(idx)
		const newValue = [...propsValue]
		newValue.push(newValue[idx])
		setPropsValue(newValue as unknown as NonNullable<T>)
		onChange({ field: name as string, value: newValue })
	}

	if (repeatable && isArray(propsValue)) {
		// Handle repeatable component with multiple entries
		return (
			<div className={cn(styles.wrapper, styles.multiple, isBuilderMode ? styles.builder : '')}>
				{propsValue.length > 0 ? (
					<>
						{isBuilderMode ? (
							<>
								<div className={styles.component__wrapper}>
									{propsValue.map((mValue, idx) => {
										return (
											<div key={idx} className={styles.component__item}>
												<button className={styles.component__drag}>
													<Icon variant="more" type="cms" />
												</button>
												<span className="accordion__title-content">
													{Object.values(mValue).find((v) => typeof v === 'string') ||
														`New entry #${idx + 1}`}
												</span>
												<div className={styles.component__action}>
													{repeatable && isArray(propsValue) && (
														<button
															title="Duplicate this entry"
															onClick={() => handleDuplicate(idx)}
														>
															<Icon variant="duplicate" type="cms" />
														</button>
													)}
													<button
														className={styles.remove__button}
														title="Remove this entry"
														onClick={() => handleRemove(idx)}
													>
														<Icon variant="remove" type="cms" />
													</button>
													<button
														title="Edit this entry"
														onClick={() => {
															setLayerPos(props.layerPos as string)
															const newEntry = {
																name:
																	(Object.values(mValue).find(
																		(v) => typeof v === 'string'
																	) as string) || `New entry #${idx + 1}`,
																value: mValue,
																fields: filteredComponents(cmpData?.schema.attributes) as Entry[],
																onChange: (props: { field: string; value: unknown }) => {
																	if (!name) return
																	propsValue[idx][props.field] = props.value
																	onChange({ field: name, value: propsValue })
																},
															}

															// Kiểm tra xem entry đã tồn tại trong childComponentData chưa
															const entryExists = childComponentData.some(
																(item) =>
																	item.name === newEntry.name && item.value === newEntry.value
															)

															const entrySameLevel = propsValue.includes(newEntry.value)

															if (
																layerPos !== contextLayerPos ||
																(entrySameLevel && childComponentData.length < 2)
															) {
																setChildComponentData([newEntry])
															} else {
																if (!entryExists) {
																	const newValue = [...childComponentData]
																	newValue.push(newEntry)
																	setChildComponentData(newValue)
																}
															}
														}}
													>
														<Icon variant="edit" type="cms" />
													</button>
												</div>
											</div>
										)
									})}
								</div>
							</>
						) : (
							<Accordion>
								{propsValue.map((mValue, idx) => {
									return (
										<AccordionItem
											key={idx}
											title={
												<>
													<span className="accordion__title-content">
														{Object.values(mValue).find((v) => typeof v === 'string') ||
															`New entry #${idx + 1}`}
													</span>
													<button
														className={styles.remove__button}
														title="Remove this entry"
														onClick={() => handleRemove(idx)}
													>
														<Icon variant="remove" type="cms" />
													</button>
												</>
											}
											icon={<Icon type="cms" variant="chevron-down" />}
										>
											{filteredComponents(cmpData?.schema.attributes).map(([key, value]) => {
												const val = value as {
													type: string
												}
												return (
													<FieldEditor
														key={key}
														{...val}
														layerPos={props.layerPos}
														name={`${key} ${isArray(mValue[key]) ? `(${mValue[key].length})` : ''}`}
														size={fieldSizes[val.type as keyof typeof fieldSizes]?.default}
														value={mValue[key]}
													/>
												)
											})}
										</AccordionItem>
									)
								})}
							</Accordion>
						)}
					</>
				) : null}
				{propsValue.length > 0 ? (
					<button className={styles.add__button} onClick={handleAdd}>
						<Icon type="cms" variant="add" /> Add an entry
					</button>
				) : (
					<button className={cn(styles.add__button, styles.no__entry)} onClick={handleAdd}>
						<Icon type="cms" variant="add" /> No entry yet. Click to add one.
					</button>
				)}
			</div>
		)
	} else {
		// Handle non-repeatable component (single entry)
		return propsValue ? (
			<div className={cn(styles.wrapper, isBuilderMode ? styles.builder : '')}>
				{isBuilderMode ? (
					<>
						<div className={styles.component__wrapper}>
							<div className={styles.component__item}>
								<button className={styles.component__drag}>
									<Icon variant="more" type="cms" />
								</button>
								<span className="accordion__title-content">
									{Object.values(propsValue).find((v) => typeof v === 'string') || 'New Entry'}
								</span>
								<div className={styles.component__action}>
									<button title="Duplicate this entry">
										<Icon variant="duplicate" type="cms" />
									</button>
									<button
										className={styles.remove__button}
										title="Remove this entry"
										onClick={() => handleRemove(0)} // input any number here
									>
										<Icon variant="remove" type="cms" />
									</button>
									<button
										title="Edit this entry"
										onClick={() => {
											setLayerPos(props.layerPos as string)
											const newEntry = {
												name:
													(Object.values(propsValue).find(
														(v) => typeof v === 'string'
													) as string) || 'New Entry',
												value: (propsValue as object) || {},
												fields: filteredComponents(cmpData?.schema.attributes) as Entry[],
												onChange: (props: { field: string; value: unknown }) => {
													if (!name) return
													propsValue[props.field] = props.value
													onChange({ field: name, value: propsValue })
												},
											}

											// Kiểm tra xem entry đã tồn tại trong childComponentData chưa
											const entryExists = childComponentData.some(
												(item) => item.name === newEntry.name && item.value === newEntry.value
											)
											if (layerPos !== contextLayerPos) {
												setChildComponentData([newEntry])
											} else {
												if (!entryExists) {
													const newValue = [...childComponentData]
													newValue.push(newEntry)
													setChildComponentData(newValue)
												}
											}
										}}
									>
										<Icon variant="edit" type="cms" />
									</button>
								</div>
							</div>
						</div>
					</>
				) : (
					<>
						{filteredComponents(cmpData?.schema.attributes).map(([key, value]) => {
							const val = value as {
								type: string
								default?: string // Default value for field
							}
							return (
								<FieldEditor
									key={key}
									{...val}
									layerPos={layerPos}
									name={key}
									size={fieldSizes[val.type as keyof typeof fieldSizes]?.default}
									value={propsValue[key as keyof typeof propsValue] || val.default}
								/>
							)
						})}
					</>
				)}
			</div>
		) : (
			<button className={cn(styles.add__button, styles.no__entry)} onClick={handleAdd}>
				<Icon type="cms" variant="add" /> No entry yet. Click to add one.
			</button>
		)
	}
}
