/**
 * Encodes an RGBA image to a PNG data URI. RGB should not be premultiplied by A.
 *
 * @remarks
 * This is optimized for speed and simplicity and does not optimize for size
 * at all. This doesn't do any compression (all values are stored uncompressed).
 *
 * @see https://github.com/evanw/thumbhash
 * <AUTHOR>
 * @license MIT
 */
export declare function rgbaToDataUri(
/** The width of the input image. Must be ≤100px. */
w: number, 
/** The height of the input image. Must be ≤100px. */
h: number, 
/** The pixels in the input image, row-by-row. Must have w*h*4 elements. */
rgba: ArrayLike<number>): string;
