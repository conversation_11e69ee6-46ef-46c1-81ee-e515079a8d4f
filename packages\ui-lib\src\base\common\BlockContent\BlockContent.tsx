import cn from 'classnames'
import Markdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import styles from './blockcontent.module.scss'

type BlockContentProps = {
	cid?: string
	Headline?: string
	Variant: string
	Blocks: {
		Title?: string
		Content?: string
	}[]
}

type CommonDataProps = {
	isFirstSection?: boolean
}

type Props = BlockContentProps & CommonDataProps

export const BlockContent = ({ cid, Headline, Variant, Blocks, isFirstSection }: Props) => (
	<section
		id={cid}
		className={cn(styles.wrapper, Headline ? '' : 'nohead', isFirstSection ? 'first__section' : '')}
	>
		<div className={cn('aidigi__grid')}>
			{Headline && <h2 className={cn(styles.headline, 'aidigi__heading')}>{Headline}</h2>}
			{Blocks && (
				<div
					className={cn(styles.content, Variant === 'grid' ? styles.content__grid : '')}
					style={{ '--isHasHeadline': Headline ? 0 : 7 } as React.CSSProperties}
				>
					{Blocks.map((item, idx) => (
						<div key={idx} className={cn(styles.block)}>
							{item.Title && (
								<h4 className={cn('aidigi__heading', styles.block__title)}>{item.Title}</h4>
							)}
							{item.Content && (
								<div className={styles.block__content}>
									<Markdown rehypePlugins={[rehypeRaw]}>{item.Content}</Markdown>
								</div>
							)}
						</div>
					))}
				</div>
			)}
		</div>
	</section>
)
