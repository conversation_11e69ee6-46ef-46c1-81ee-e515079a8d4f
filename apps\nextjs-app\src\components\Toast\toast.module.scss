@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	position: absolute;
	min-width: px-to(300px, rem);
	display: grid;
	gap: spacing(s2);
	z-index: 10;

	&.top {
		top: spacing(s4);
		left: 50%;
		transform: translateX(-50%);
	}

	&.top-right {
		top: spacing(s4);
		right: spacing(s4);
	}

	&.top-left {
		top: spacing(s4);
		left: spacing(s4);
	}

	&.bottom {
		bottom: spacing(s4);
		left: 50%;
		transform: translateX(-50%);
	}

	&.bottom-right {
		bottom: spacing(s4);
		right: spacing(s4);
	}

	&.bottom-left {
		bottom: spacing(s4);
		left: spacing(s4);
	}
}

.toast {
	border-radius: spacing(s2);
	background-color: color('white', 100);
	border: px-to(1px, rem) solid color('border');
	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
	overflow: hidden;

	&__inner {
		display: flex;
		align-items: center;
		padding: spacing(s2) spacing(s12) spacing(s2) spacing(s2);
		gap: spacing(s1);
		color: color('white', 100);
		background-color: var(--type-bg);
		position: relative;
	}

	&__icon {
		margin-top: px-to(2px, rem);
		--size: #{spacing(s4)};
	}

	&__message {
		width: 100%;
		font-weight: 500;
		@include fluid($font-size) {
			font-size: size('paragraph', 'sm');
		}

		strong {
			font-weight: 700;
		}
	}

	button.toast__close {
		padding: 0;
		--size: #{spacing(s6)};
		position: absolute;
		right: spacing(s2);
		top: spacing(s1);
	}
}

.type {
	&--success {
		--type-bg: #{color('green', 100)};
		--type-color: #{color('green', 100)};
	}
	&--error {
		--type-bg: #{color('red', 100)};
		--type-color: #{color('red', 100)};
	}
	&--warning {
		--type-bg: #{color('yellow', 100)};
		--type-color: #{color('yellow', 100)};
	}
}
