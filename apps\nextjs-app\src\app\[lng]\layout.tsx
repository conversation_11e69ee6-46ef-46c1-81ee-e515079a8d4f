import { languages } from '@collective/i18n'
import { getCmsData, type INavigationProps } from '@collective/integration-lib/cms'
// import NavigationProvider from '@collective/ui-lib/contexts/NavigationContext'
import { dir } from 'i18next'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
// import { BubbleChat } from '@/components/BubbleChat/BubbleChat'
// import { Modal } from '@/components/Modal/Modal'
// import { Sidebar } from '@/components/Sidebar/Sidebar'
import ClientLayout from './clientLayout'

const inter = Inter({ subsets: ['latin'], weight: ['400', '500', '600'] })

export const metadata: Metadata = {
	title: 'Create Next App',
	description: 'Generated by create next app',
}

export async function generateStaticParams() {
	return languages.map((lng) => ({ lng }))
}

type ISideBarProps = {
	Headline: string
	slug: string
	isLocked: boolean
	Pages?: ISideBarProps[]
}

async function getPagesData(lng: string): Promise<INavigationProps[]> {
	const CmsData = await getCmsData<ISideBarProps, 'multiple'>({
		path: `categories`,
		deep: 4,
		locale: lng,
		filter: 'sort=order:asc',
	})

	const Pages = CmsData.data.map((page) => ({
		label: page.Headline,
		path: page.slug,
		isLocked: page.isLocked,
		children: page.Pages?.map((child) => ({
			label: child.Headline,
			path: child.slug,
			isLocked: child.isLocked,
		})),
	}))

	return Pages
}

export default async function RootLayout({
	children,
	uuid,
	params: { lng },
}: Readonly<{
	children: React.ReactNode
	uuid: string
	params: { lng: string }
}>) {
	const pagesData = await getPagesData(lng)
	return (
		<html lang={lng} dir={dir(lng)}>
			<body className={inter.className}>
				<ClientLayout pagesData={pagesData} lng={lng} uuid={uuid}>
					{children}
				</ClientLayout>
			</body>
		</html>
	)
}
