"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize";
exports.ids = ["vendor-chunks/micromark-util-subtokenize"];
exports.modules = {

/***/ "(ssr)/../../node_modules/micromark-util-subtokenize/dev/index.js":
/*!******************************************************************!*\
  !*** ../../node_modules/micromark-util-subtokenize/dev/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/../../node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/../../node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(ssr)/../../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\n\n\n\n\n\n// Hidden API exposed for testing.\n\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow &&\n      events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n            }\n\n            otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else if (\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix ||\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent\n        ) {\n          // Move past.\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, 'expected `contentType` on subtokens')\n\n  let tokenizer = token._tokenizer\n\n  if (!tokenizer) {\n    tokenizer = context.parser[token.contentType](token.start)\n\n    if (token._contentTypeTextTrailing) {\n      tokenizer._contentTypeTextTrailing = true\n    }\n  }\n\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay11dGlsLXN1YnRva2VuaXplL2Rldi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQSxZQUFZLHFCQUFxQjtBQUNqQzs7QUFFbUM7QUFDVTtBQUNLO0FBQ0M7O0FBRW5EO0FBQ21EOztBQUVuRDtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUCxhQUFhLHdCQUF3QjtBQUNyQztBQUNBO0FBQ0EsYUFBYSxPQUFPO0FBQ3BCO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxhQUFhLE9BQU87QUFDcEI7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQztBQUNBLHFCQUFxQiwrREFBWTs7QUFFakM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isd0RBQUs7QUFDN0Isd0NBQXdDLHdEQUFLO0FBQzdDO0FBQ0EsTUFBTSwwQ0FBTTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDBDQUEwQyx3REFBSztBQUMvQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDBDQUEwQyx3REFBSztBQUMvQztBQUNBO0FBQ0EsZ0RBQWdELHdEQUFLO0FBQ3JEO0FBQ0E7O0FBRUEsZ0RBQWdELHdEQUFLO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLGlDQUFpQyx3REFBSztBQUN0QyxpQ0FBaUMsd0RBQUs7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsOENBQThDLHdEQUFLO0FBQ25EOztBQUVBLGlDQUFpQyx3REFBSztBQUN0QztBQUNBO0FBQ0EsVUFBVTtBQUNWLGlDQUFpQyx3REFBSztBQUN0QyxpQ0FBaUMsd0RBQUs7QUFDdEM7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHdCQUF3Qjs7QUFFeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxFQUFFLDhEQUFNO0FBQ1I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGVBQWU7QUFDNUI7QUFDQSxFQUFFLDJDQUFNOztBQUVSOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QztBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0EsYUFBYSxjQUFjO0FBQzNCO0FBQ0EsYUFBYSxtQkFBbUI7QUFDaEM7QUFDQTtBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLElBQUksMENBQU07QUFDVjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNOztBQUVWOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0Isd0RBQUs7QUFDekI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMENBQU07QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1YsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi4vLi4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay11dGlsLXN1YnRva2VuaXplL2Rldi9pbmRleC5qcz8yYTczIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Q2h1bmssIEV2ZW50LCBUb2tlbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7c3BsaWNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaHVua2VkJ1xuaW1wb3J0IHtjb2RlcywgdHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcbmltcG9ydCB7U3BsaWNlQnVmZmVyfSBmcm9tICcuL2xpYi9zcGxpY2UtYnVmZmVyLmpzJ1xuXG4vLyBIaWRkZW4gQVBJIGV4cG9zZWQgZm9yIHRlc3RpbmcuXG5leHBvcnQge1NwbGljZUJ1ZmZlcn0gZnJvbSAnLi9saWIvc3BsaWNlLWJ1ZmZlci5qcydcblxuLyoqXG4gKiBUb2tlbml6ZSBzdWJjb250ZW50LlxuICpcbiAqIEBwYXJhbSB7QXJyYXk8RXZlbnQ+fSBldmVudHNBcnJheVxuICogICBMaXN0IG9mIGV2ZW50cy5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIHN1YnRva2VucyB3ZXJlIGZvdW5kLlxuICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgY29tcGxleGl0eVxuZXhwb3J0IGZ1bmN0aW9uIHN1YnRva2VuaXplKGV2ZW50c0FycmF5KSB7XG4gIC8qKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgbnVtYmVyPn0gKi9cbiAgY29uc3QganVtcHMgPSB7fVxuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge0V2ZW50fSAqL1xuICBsZXQgZXZlbnRcbiAgLyoqIEB0eXBlIHtudW1iZXIgfCB1bmRlZmluZWR9ICovXG4gIGxldCBsaW5lSW5kZXhcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIGxldCBvdGhlckluZGV4XG4gIC8qKiBAdHlwZSB7RXZlbnR9ICovXG4gIGxldCBvdGhlckV2ZW50XG4gIC8qKiBAdHlwZSB7QXJyYXk8RXZlbnQ+fSAqL1xuICBsZXQgcGFyYW1ldGVyc1xuICAvKiogQHR5cGUge0FycmF5PEV2ZW50Pn0gKi9cbiAgbGV0IHN1YmV2ZW50c1xuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCBtb3JlXG4gIGNvbnN0IGV2ZW50cyA9IG5ldyBTcGxpY2VCdWZmZXIoZXZlbnRzQXJyYXkpXG5cbiAgd2hpbGUgKCsraW5kZXggPCBldmVudHMubGVuZ3RoKSB7XG4gICAgd2hpbGUgKGluZGV4IGluIGp1bXBzKSB7XG4gICAgICBpbmRleCA9IGp1bXBzW2luZGV4XVxuICAgIH1cblxuICAgIGV2ZW50ID0gZXZlbnRzLmdldChpbmRleClcblxuICAgIC8vIEFkZCBhIGhvb2sgZm9yIHRoZSBHRk0gdGFza2xpc3QgZXh0ZW5zaW9uLCB3aGljaCBuZWVkcyB0byBrbm93IGlmIHRleHRcbiAgICAvLyBpcyBpbiB0aGUgZmlyc3QgY29udGVudCBvZiBhIGxpc3QgaXRlbS5cbiAgICBpZiAoXG4gICAgICBpbmRleCAmJlxuICAgICAgZXZlbnRbMV0udHlwZSA9PT0gdHlwZXMuY2h1bmtGbG93ICYmXG4gICAgICBldmVudHMuZ2V0KGluZGV4IC0gMSlbMV0udHlwZSA9PT0gdHlwZXMubGlzdEl0ZW1QcmVmaXhcbiAgICApIHtcbiAgICAgIGFzc2VydChldmVudFsxXS5fdG9rZW5pemVyLCAnZXhwZWN0ZWQgYF90b2tlbml6ZXJgIG9uIHN1YnRva2VucycpXG4gICAgICBzdWJldmVudHMgPSBldmVudFsxXS5fdG9rZW5pemVyLmV2ZW50c1xuICAgICAgb3RoZXJJbmRleCA9IDBcblxuICAgICAgaWYgKFxuICAgICAgICBvdGhlckluZGV4IDwgc3ViZXZlbnRzLmxlbmd0aCAmJlxuICAgICAgICBzdWJldmVudHNbb3RoZXJJbmRleF1bMV0udHlwZSA9PT0gdHlwZXMubGluZUVuZGluZ0JsYW5rXG4gICAgICApIHtcbiAgICAgICAgb3RoZXJJbmRleCArPSAyXG4gICAgICB9XG5cbiAgICAgIGlmIChcbiAgICAgICAgb3RoZXJJbmRleCA8IHN1YmV2ZW50cy5sZW5ndGggJiZcbiAgICAgICAgc3ViZXZlbnRzW290aGVySW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmNvbnRlbnRcbiAgICAgICkge1xuICAgICAgICB3aGlsZSAoKytvdGhlckluZGV4IDwgc3ViZXZlbnRzLmxlbmd0aCkge1xuICAgICAgICAgIGlmIChzdWJldmVudHNbb3RoZXJJbmRleF1bMV0udHlwZSA9PT0gdHlwZXMuY29udGVudCkge1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoc3ViZXZlbnRzW290aGVySW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmNodW5rVGV4dCkge1xuICAgICAgICAgICAgc3ViZXZlbnRzW290aGVySW5kZXhdWzFdLl9pc0luRmlyc3RDb250ZW50T2ZMaXN0SXRlbSA9IHRydWVcbiAgICAgICAgICAgIG90aGVySW5kZXgrK1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEVudGVyLlxuICAgIGlmIChldmVudFswXSA9PT0gJ2VudGVyJykge1xuICAgICAgaWYgKGV2ZW50WzFdLmNvbnRlbnRUeXBlKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24oanVtcHMsIHN1YmNvbnRlbnQoZXZlbnRzLCBpbmRleCkpXG4gICAgICAgIGluZGV4ID0ganVtcHNbaW5kZXhdXG4gICAgICAgIG1vcmUgPSB0cnVlXG4gICAgICB9XG4gICAgfVxuICAgIC8vIEV4aXQuXG4gICAgZWxzZSBpZiAoZXZlbnRbMV0uX2NvbnRhaW5lcikge1xuICAgICAgb3RoZXJJbmRleCA9IGluZGV4XG4gICAgICBsaW5lSW5kZXggPSB1bmRlZmluZWRcblxuICAgICAgd2hpbGUgKG90aGVySW5kZXgtLSkge1xuICAgICAgICBvdGhlckV2ZW50ID0gZXZlbnRzLmdldChvdGhlckluZGV4KVxuXG4gICAgICAgIGlmIChcbiAgICAgICAgICBvdGhlckV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpbmVFbmRpbmcgfHxcbiAgICAgICAgICBvdGhlckV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpbmVFbmRpbmdCbGFua1xuICAgICAgICApIHtcbiAgICAgICAgICBpZiAob3RoZXJFdmVudFswXSA9PT0gJ2VudGVyJykge1xuICAgICAgICAgICAgaWYgKGxpbmVJbmRleCkge1xuICAgICAgICAgICAgICBldmVudHMuZ2V0KGxpbmVJbmRleClbMV0udHlwZSA9IHR5cGVzLmxpbmVFbmRpbmdCbGFua1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBvdGhlckV2ZW50WzFdLnR5cGUgPSB0eXBlcy5saW5lRW5kaW5nXG4gICAgICAgICAgICBsaW5lSW5kZXggPSBvdGhlckluZGV4XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgIG90aGVyRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGluZVByZWZpeCB8fFxuICAgICAgICAgIG90aGVyRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGlzdEl0ZW1JbmRlbnRcbiAgICAgICAgKSB7XG4gICAgICAgICAgLy8gTW92ZSBwYXN0LlxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKGxpbmVJbmRleCkge1xuICAgICAgICAvLyBGaXggcG9zaXRpb24uXG4gICAgICAgIGV2ZW50WzFdLmVuZCA9IHsuLi5ldmVudHMuZ2V0KGxpbmVJbmRleClbMV0uc3RhcnR9XG5cbiAgICAgICAgLy8gU3dpdGNoIGNvbnRhaW5lciBleGl0IHcvIGxpbmUgZW5kaW5ncy5cbiAgICAgICAgcGFyYW1ldGVycyA9IGV2ZW50cy5zbGljZShsaW5lSW5kZXgsIGluZGV4KVxuICAgICAgICBwYXJhbWV0ZXJzLnVuc2hpZnQoZXZlbnQpXG4gICAgICAgIGV2ZW50cy5zcGxpY2UobGluZUluZGV4LCBpbmRleCAtIGxpbmVJbmRleCArIDEsIHBhcmFtZXRlcnMpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gVGhlIGNoYW5nZXMgdG8gdGhlIGBldmVudHNgIGJ1ZmZlciBtdXN0IGJlIGNvcGllZCBiYWNrIGludG8gdGhlIGV2ZW50c0FycmF5XG4gIHNwbGljZShldmVudHNBcnJheSwgMCwgTnVtYmVyLlBPU0lUSVZFX0lORklOSVRZLCBldmVudHMuc2xpY2UoMCkpXG4gIHJldHVybiAhbW9yZVxufVxuXG4vKipcbiAqIFRva2VuaXplIGVtYmVkZGVkIHRva2Vucy5cbiAqXG4gKiBAcGFyYW0ge1NwbGljZUJ1ZmZlcjxFdmVudD59IGV2ZW50c1xuICogICBFdmVudHMuXG4gKiBAcGFyYW0ge251bWJlcn0gZXZlbnRJbmRleFxuICogICBJbmRleC5cbiAqIEByZXR1cm5zIHtSZWNvcmQ8c3RyaW5nLCBudW1iZXI+fVxuICogICBHYXBzLlxuICovXG5mdW5jdGlvbiBzdWJjb250ZW50KGV2ZW50cywgZXZlbnRJbmRleCkge1xuICBjb25zdCB0b2tlbiA9IGV2ZW50cy5nZXQoZXZlbnRJbmRleClbMV1cbiAgY29uc3QgY29udGV4dCA9IGV2ZW50cy5nZXQoZXZlbnRJbmRleClbMl1cbiAgbGV0IHN0YXJ0UG9zaXRpb24gPSBldmVudEluZGV4IC0gMVxuICAvKiogQHR5cGUge0FycmF5PG51bWJlcj59ICovXG4gIGNvbnN0IHN0YXJ0UG9zaXRpb25zID0gW11cbiAgYXNzZXJ0KHRva2VuLmNvbnRlbnRUeXBlLCAnZXhwZWN0ZWQgYGNvbnRlbnRUeXBlYCBvbiBzdWJ0b2tlbnMnKVxuXG4gIGxldCB0b2tlbml6ZXIgPSB0b2tlbi5fdG9rZW5pemVyXG5cbiAgaWYgKCF0b2tlbml6ZXIpIHtcbiAgICB0b2tlbml6ZXIgPSBjb250ZXh0LnBhcnNlclt0b2tlbi5jb250ZW50VHlwZV0odG9rZW4uc3RhcnQpXG5cbiAgICBpZiAodG9rZW4uX2NvbnRlbnRUeXBlVGV4dFRyYWlsaW5nKSB7XG4gICAgICB0b2tlbml6ZXIuX2NvbnRlbnRUeXBlVGV4dFRyYWlsaW5nID0gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGNoaWxkRXZlbnRzID0gdG9rZW5pemVyLmV2ZW50c1xuICAvKiogQHR5cGUge0FycmF5PFtudW1iZXIsIG51bWJlcl0+fSAqL1xuICBjb25zdCBqdW1wcyA9IFtdXG4gIC8qKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgbnVtYmVyPn0gKi9cbiAgY29uc3QgZ2FwcyA9IHt9XG4gIC8qKiBAdHlwZSB7QXJyYXk8Q2h1bms+fSAqL1xuICBsZXQgc3RyZWFtXG4gIC8qKiBAdHlwZSB7VG9rZW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCBwcmV2aW91c1xuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge1Rva2VuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgY3VycmVudCA9IHRva2VuXG4gIGxldCBhZGp1c3QgPSAwXG4gIGxldCBzdGFydCA9IDBcbiAgY29uc3QgYnJlYWtzID0gW3N0YXJ0XVxuXG4gIC8vIExvb3AgZm9yd2FyZCB0aHJvdWdoIHRoZSBsaW5rZWQgdG9rZW5zIHRvIHBhc3MgdGhlbSBpbiBvcmRlciB0byB0aGVcbiAgLy8gc3VidG9rZW5pemVyLlxuICB3aGlsZSAoY3VycmVudCkge1xuICAgIC8vIEZpbmQgdGhlIHBvc2l0aW9uIG9mIHRoZSBldmVudCBmb3IgdGhpcyB0b2tlbi5cbiAgICB3aGlsZSAoZXZlbnRzLmdldCgrK3N0YXJ0UG9zaXRpb24pWzFdICE9PSBjdXJyZW50KSB7XG4gICAgICAvLyBFbXB0eS5cbiAgICB9XG5cbiAgICBhc3NlcnQoXG4gICAgICAhcHJldmlvdXMgfHwgY3VycmVudC5wcmV2aW91cyA9PT0gcHJldmlvdXMsXG4gICAgICAnZXhwZWN0ZWQgcHJldmlvdXMgdG8gbWF0Y2gnXG4gICAgKVxuICAgIGFzc2VydCghcHJldmlvdXMgfHwgcHJldmlvdXMubmV4dCA9PT0gY3VycmVudCwgJ2V4cGVjdGVkIG5leHQgdG8gbWF0Y2gnKVxuXG4gICAgc3RhcnRQb3NpdGlvbnMucHVzaChzdGFydFBvc2l0aW9uKVxuXG4gICAgaWYgKCFjdXJyZW50Ll90b2tlbml6ZXIpIHtcbiAgICAgIHN0cmVhbSA9IGNvbnRleHQuc2xpY2VTdHJlYW0oY3VycmVudClcblxuICAgICAgaWYgKCFjdXJyZW50Lm5leHQpIHtcbiAgICAgICAgc3RyZWFtLnB1c2goY29kZXMuZW9mKVxuICAgICAgfVxuXG4gICAgICBpZiAocHJldmlvdXMpIHtcbiAgICAgICAgdG9rZW5pemVyLmRlZmluZVNraXAoY3VycmVudC5zdGFydClcbiAgICAgIH1cblxuICAgICAgaWYgKGN1cnJlbnQuX2lzSW5GaXJzdENvbnRlbnRPZkxpc3RJdGVtKSB7XG4gICAgICAgIHRva2VuaXplci5fZ2ZtVGFza2xpc3RGaXJzdENvbnRlbnRPZkxpc3RJdGVtID0gdHJ1ZVxuICAgICAgfVxuXG4gICAgICB0b2tlbml6ZXIud3JpdGUoc3RyZWFtKVxuXG4gICAgICBpZiAoY3VycmVudC5faXNJbkZpcnN0Q29udGVudE9mTGlzdEl0ZW0pIHtcbiAgICAgICAgdG9rZW5pemVyLl9nZm1UYXNrbGlzdEZpcnN0Q29udGVudE9mTGlzdEl0ZW0gPSB1bmRlZmluZWRcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBVbnJhdmVsIHRoZSBuZXh0IHRva2VuLlxuICAgIHByZXZpb3VzID0gY3VycmVudFxuICAgIGN1cnJlbnQgPSBjdXJyZW50Lm5leHRcbiAgfVxuXG4gIC8vIE5vdywgbG9vcCBiYWNrIHRocm91Z2ggYWxsIGV2ZW50cyAoYW5kIGxpbmtlZCB0b2tlbnMpLCB0byBmaWd1cmUgb3V0IHdoaWNoXG4gIC8vIHBhcnRzIGJlbG9uZyB3aGVyZS5cbiAgY3VycmVudCA9IHRva2VuXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGlsZEV2ZW50cy5sZW5ndGgpIHtcbiAgICBpZiAoXG4gICAgICAvLyBGaW5kIGEgdm9pZCB0b2tlbiB0aGF0IGluY2x1ZGVzIGEgYnJlYWsuXG4gICAgICBjaGlsZEV2ZW50c1tpbmRleF1bMF0gPT09ICdleGl0JyAmJlxuICAgICAgY2hpbGRFdmVudHNbaW5kZXggLSAxXVswXSA9PT0gJ2VudGVyJyAmJlxuICAgICAgY2hpbGRFdmVudHNbaW5kZXhdWzFdLnR5cGUgPT09IGNoaWxkRXZlbnRzW2luZGV4IC0gMV1bMV0udHlwZSAmJlxuICAgICAgY2hpbGRFdmVudHNbaW5kZXhdWzFdLnN0YXJ0LmxpbmUgIT09IGNoaWxkRXZlbnRzW2luZGV4XVsxXS5lbmQubGluZVxuICAgICkge1xuICAgICAgYXNzZXJ0KGN1cnJlbnQsICdleHBlY3RlZCBhIGN1cnJlbnQgdG9rZW4nKVxuICAgICAgc3RhcnQgPSBpbmRleCArIDFcbiAgICAgIGJyZWFrcy5wdXNoKHN0YXJ0KVxuICAgICAgLy8gSGVscCBHQy5cbiAgICAgIGN1cnJlbnQuX3Rva2VuaXplciA9IHVuZGVmaW5lZFxuICAgICAgY3VycmVudC5wcmV2aW91cyA9IHVuZGVmaW5lZFxuICAgICAgY3VycmVudCA9IGN1cnJlbnQubmV4dFxuICAgIH1cbiAgfVxuXG4gIC8vIEhlbHAgR0MuXG4gIHRva2VuaXplci5ldmVudHMgPSBbXVxuXG4gIC8vIElmIHRoZXJl4oCZcyBvbmUgbW9yZSB0b2tlbiAod2hpY2ggaXMgdGhlIGNhc2VzIGZvciBsaW5lcyB0aGF0IGVuZCBpbiBhblxuICAvLyBFT0YpLCB0aGF04oCZcyBwZXJmZWN0OiB0aGUgbGFzdCBwb2ludCB3ZSBmb3VuZCBzdGFydHMgaXQuXG4gIC8vIElmIHRoZXJlIGlzbuKAmXQgdGhlbiBtYWtlIHN1cmUgYW55IHJlbWFpbmluZyBjb250ZW50IGlzIGFkZGVkIHRvIGl0LlxuICBpZiAoY3VycmVudCkge1xuICAgIC8vIEhlbHAgR0MuXG4gICAgY3VycmVudC5fdG9rZW5pemVyID0gdW5kZWZpbmVkXG4gICAgY3VycmVudC5wcmV2aW91cyA9IHVuZGVmaW5lZFxuICAgIGFzc2VydCghY3VycmVudC5uZXh0LCAnZXhwZWN0ZWQgbm8gbmV4dCB0b2tlbicpXG4gIH0gZWxzZSB7XG4gICAgYnJlYWtzLnBvcCgpXG4gIH1cblxuICAvLyBOb3cgc3BsaWNlIHRoZSBldmVudHMgZnJvbSB0aGUgc3VidG9rZW5pemVyIGludG8gdGhlIGN1cnJlbnQgZXZlbnRzLFxuICAvLyBtb3ZpbmcgYmFjayB0byBmcm9udCBzbyB0aGF0IHNwbGljZSBpbmRpY2VzIGFyZW7igJl0IGFmZmVjdGVkLlxuICBpbmRleCA9IGJyZWFrcy5sZW5ndGhcblxuICB3aGlsZSAoaW5kZXgtLSkge1xuICAgIGNvbnN0IHNsaWNlID0gY2hpbGRFdmVudHMuc2xpY2UoYnJlYWtzW2luZGV4XSwgYnJlYWtzW2luZGV4ICsgMV0pXG4gICAgY29uc3Qgc3RhcnQgPSBzdGFydFBvc2l0aW9ucy5wb3AoKVxuICAgIGFzc2VydChzdGFydCAhPT0gdW5kZWZpbmVkLCAnZXhwZWN0ZWQgYSBzdGFydCBwb3NpdGlvbiB3aGVuIHNwbGljaW5nJylcbiAgICBqdW1wcy5wdXNoKFtzdGFydCwgc3RhcnQgKyBzbGljZS5sZW5ndGggLSAxXSlcbiAgICBldmVudHMuc3BsaWNlKHN0YXJ0LCAyLCBzbGljZSlcbiAgfVxuXG4gIGp1bXBzLnJldmVyc2UoKVxuICBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBqdW1wcy5sZW5ndGgpIHtcbiAgICBnYXBzW2FkanVzdCArIGp1bXBzW2luZGV4XVswXV0gPSBhZGp1c3QgKyBqdW1wc1tpbmRleF1bMV1cbiAgICBhZGp1c3QgKz0ganVtcHNbaW5kZXhdWzFdIC0ganVtcHNbaW5kZXhdWzBdIC0gMVxuICB9XG5cbiAgcmV0dXJuIGdhcHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/../../node_modules/micromark-util-symbol/lib/constants.js\");\n\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nclass SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;