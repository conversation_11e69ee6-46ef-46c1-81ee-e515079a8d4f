@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		grid-template-columns: repeat(calc(var(--count) / 3), 1fr);
		// padding: 0;

		@include min-width('md') {
			grid-template-columns: repeat(calc(var(--count) / 2), 1fr);
		}

		@include min-width('2md') {
			grid-template-columns: repeat(calc(var(--count)), 1fr);
		}
	}

	.media {
		background-color: #f6f6f6;
		height: auto;
		min-height: clamp(
			px-to(200px, rem),
			calc(px-to(471px, rem) - (px-to(67.75px, rem) * (var(--count) - 2))),
			px-to(471px, rem)
		);
		border-radius: px-to(25px, rem);
		overflow: hidden;
		display: flex;
		flex-direction: column;
		position: relative;
	}

	.inner {
		position: relative;
		width: 100%;
		flex: 1 1 100%;
		display: flex;
		justify-content: center;
	}

	.download {
		position: absolute;
		right: 0;
		bottom: 0;
		width: px-to(56px, rem);
		height: px-to(54px, rem);
		display: flex;
		align-items: center;
		justify-content: center;
		border-top-left-radius: px-to(25px, rem);
		border-bottom-right-radius: px-to(25px, rem);
		background-color: #f0f0f0;

		svg {
			--size: #{px-to(18px, rem)};
		}
	}

	img {
		position: absolute;
		width: auto;
		height: auto;
		max-height: 100%;
		object-fit: cover;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.full__scale img {
		width: 100%;
		height: 100%;
		max-height: none;
	}
	.auto__height {
		min-height: unset;
		img {
			position: static;
			transform: none;
		}
	}

	p {
		padding: px-to(10px, rem);
		color: #646464;
		font-size: clamp(
			px-to(14px, rem),
			calc(px-to(20px, rem) - (px-to(1.2px, rem) * (var(--count) - 2))),
			px-to(20px, rem)
		);
	}
}
