# Collect CMS

A modern, flexible headless CMS frontend built with Next.js, designed to integrate seamlessly with Strapi backend.

## Overview

Collect CMS is a content management system frontend that provides a user-friendly interface for managing digital content. It's built using Next.js and is designed to work with Strapi as the backend CMS. The system supports dynamic content types, components, and multilingual content management.

## Features

- **Modern React-based UI**: Built with Next.js for optimal performance and developer experience
- **Strapi Integration**: Seamless integration with Strapi headless CMS
- **Component-based Architecture**: Modular design for easy customization and extension
- **Multilingual Support**: Built-in internationalization with i18n
- **Dynamic Content Types**: Support for various content types and dynamic zones
- **Rich Text Editing**: Integrated CKEditor for rich content creation
- **Responsive Design**: Works across all device sizes
- **Page Builder**: Visual interface for creating and editing pages
- **Media Management**: Integrated media library for managing images and other media
- **User Authentication**: Secure login and user management

## Prerequisites

- Node.js (>= 18.17.0)
- Yarn (>= 1.22.0)
- Strapi backend instance

## Environment Variables

The following environment variables are required:

```
NEXT_PUBLIC_STRAPI_HOST=https://your-strapi-instance.com
NEXT_PUBLIC_STRAPI_API_KEY=your-strapi-api-key
NEXT_PUBLIC_STRAPI_POST_API_KEY=your-strapi-post-api-key
STRAPI_COLLECT_CMS_API_KEY=your-strapi-cms-api-key
```

## Getting Started

### Run the development server

```bash
# From the root of the monorepo
yarn dev:cms

# Or from the collect-cms directory
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Building for Production

```bash
# Standard build
yarn build

# Build for Cloudflare
yarn build-cloudflare
```

## Project Structure

- `src/app`: Next.js app router pages
- `src/components`: React components
- `src/common`: Common utilities and functions
- `src/contexts`: React context providers
- `src/layouts`: Layout components
- `src/styles`: Global styles and SCSS modules

## Key Components

### Builder Components

The Builder components are responsible for the page builder interface:

- **LayoutEditor**: Main component for editing page layouts
- **ComponentEditor**: For editing dynamic components
- **FieldEditor**: For editing different field types
- **ComponentMenu**: Menu for adding new components
- **Dnd**: Drag and drop functionality for rearranging components

### CMS Integration

The CMS integration is handled by the `src/common/cms` directory:

- **strapiV5**: Integration with Strapi V5
- **interface.ts**: Common interfaces for CMS data

## API Integration

Collect CMS communicates with Strapi through several API endpoints:

- Content types and components are fetched from Strapi
- Authentication is handled via JWT tokens
- Media is stored and managed in Strapi
- Content is created, updated, and published through Strapi APIs

## Authentication

The CMS uses JWT authentication with Strapi. The token is stored in localStorage as 'adminJwt' and is used for all API requests.

## Internationalization

The CMS supports multiple languages through the `@collective/i18n` package. The language is specified in the URL path, e.g., `/en/dashboard`.

## Styling

The CMS uses SCSS modules for styling. Global styles are defined in `src/styles/globals.scss`.

## Dependencies

Key dependencies include:

- **Next.js**: React framework
- **CKEditor**: Rich text editor
- **SCSS**: For styling
- **Cloudflare**: For deployment (optional)

## Deployment

The CMS can be deployed to:

- **Vercel**: For standard deployment
- **Cloudflare Pages**: Using the `build-cloudflare` script

## Troubleshooting

If you encounter issues:

1. Check that all environment variables are correctly set
2. Ensure your Strapi backend is running and accessible
3. Check browser console for any errors
4. Verify that you have the correct permissions in Strapi

## Support

For support, please contact the Collective team.
