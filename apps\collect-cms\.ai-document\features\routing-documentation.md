# Collect CMS Routing Documentation

This document provides a comprehensive overview of the routing system in the Collect CMS application, explaining how different routes are structured, authenticated, and rendered.

## Table of Contents

1. [Overview](#overview)
2. [Directory Structure](#directory-structure)
3. [Route Types](#route-types)
4. [Authentication Flow](#authentication-flow)
5. [Internationalization](#internationalization)
6. [Route Groups](#route-groups)
7. [Dynamic Routes](#dynamic-routes)
8. [Middleware](#middleware)
9. [Navigation Structure](#navigation-structure)
10. [Error Handling](#error-handling)

## Overview

Collect CMS is built with Next.js using the App Router pattern. The routing system follows Next.js 13+ conventions with folder-based routing, where each folder represents a route segment. The application supports internationalization, authentication, and different layout groups.

## Directory Structure

The main routing structure is located in `apps/collect-cms/src/app` with the following key directories:

```
apps/collect-cms/src/app/
├── [lng]/                      # Language parameter (e.g., 'en', 'fr')
│   ├── (dashboard)/            # Route group for dashboard pages
│   │   ├── page.tsx            # Dashboard home page
│   │   ├── content-manager/    # Content management routes
│   │   │   └── [...slug]/      # Dynamic catch-all route for content types
│   │   ├── login/              # Login page
│   │   └── layout.tsx          # Layout for dashboard pages
│   ├── (pagebuilder)/          # Route group for page builder
│   │   ├── content-builder/    # Content builder routes
│   │   │   └── [...slug]/      # Dynamic catch-all route for content editing
│   ├── [..rest]/               # Catch-all route for undefined routes
│   ├── not-found.tsx           # 404 page for language-specific routes
│   └── layout.tsx              # Root layout for language-specific routes
├── not-found.tsx               # Global 404 page
└── layout.tsx                  # Root layout for the entire application
```

## Route Types

The Collect CMS app has several types of routes:

1. **Static Routes**: Fixed routes like `/dashboard` or `/login`
2. **Dynamic Routes**: Routes with parameters like `[lng]` for language
3. **Catch-all Routes**: Routes that handle multiple segments like `[...slug]`
4. **Route Groups**: Logical groupings using parentheses like `(dashboard)` that don't affect the URL path

## Authentication Flow

Authentication is handled primarily through the middleware:

1. When a user accesses a protected route without authentication, they are redirected to the login page
2. The login page (`/[lng]/login`) handles authentication against the Strapi backend
3. Upon successful login, a JWT token is stored in:
   - Cookies (`adminJwt`) for server-side authentication
   - LocalStorage or SessionStorage (based on "Remember me" option) for client-side authentication
4. The user is then redirected to their original destination or the dashboard

### Public vs. Protected Routes

The middleware (`src/middleware.ts`) defines public routes that don't require authentication:

```typescript
const isPublicRoute =
	request.nextUrl.pathname.includes("/login") ||
	request.nextUrl.pathname === `/${lng}` ||
	request.nextUrl.pathname === `/${lng}/` ||
	request.nextUrl.pathname.includes("/forgot-password") ||
	request.nextUrl.pathname.includes("/reset-password")
```

All other routes require authentication, and users without a valid token are redirected to the login page.

## Internationalization

The app supports internationalization through:

1. **URL-based Locale Detection**: The first segment of the URL path indicates the language (e.g., `/en/dashboard`)
2. **Default Language Fallback**: If no language is specified, the app defaults to the fallback language (defined in `@collective/i18n/settings`)
3. **Language Parameter**: All routes include a `[lng]` parameter that is passed to components

The internationalization is implemented using:

- `next-i18n-router` for routing
- `accept-language` for header-based language detection
- `i18next` for translation management

## Route Groups

The app uses route groups (denoted by parentheses) to organize routes without affecting the URL structure:

1. **(dashboard)**: Contains administrative pages like the dashboard, content manager, and login
2. **(pagebuilder)**: Contains the content builder interface for editing content

These groups allow for different layouts and behaviors while maintaining a clean URL structure.

## Dynamic Routes

Several dynamic routes are used:

1. **`[lng]`**: Captures the language parameter
2. **`[...slug]`**: Catch-all route for content manager paths
3. **`[...rest]`**: Catch-all route for handling undefined routes (redirects to 404)

The content manager routes follow this pattern:

- `/[lng]/content-manager/[group]/[type]`: List view for a content type
- `/[lng]/content-manager/[group]/[type]/[id]`: Edit view for a specific content item
- `/[lng]/content-builder/[group]/[type]/[id]`: Page builder for a specific content item

## Middleware

The middleware (`src/middleware.ts`) handles several key functions:

1. **Authentication**: Checks for the presence of a valid JWT token
2. **Redirection**: Redirects unauthenticated users to the login page
3. **Internationalization**: Handles language detection and routing
4. **Root Path Handling**: Redirects the root path (`/`) to the login page with the default language

The middleware is applied to all routes except static files and API routes:

```typescript
export const config = {
	matcher: ["/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js).*)"],
}
```

## Navigation Structure

The navigation structure is defined in `src/mock/Navigation.ts` and follows this pattern:

```typescript
export const NavigationData: INavigationProps[] = [
	{
		apiId: "group-id",
		uid: "group-uid",
		isDisplayed: true,
		isPinned: true / false,
		info: { displayName: "Group Name", description: "Group Description" },
		kind: "group",
		layouts: [
			{
				name: "Content Type Name",
				apiId: "content-type-id",
				uid: "content-type-uid",
				visible: true,
				kind: "singleType" / "collectionType",
				identifierField: "slug",
			},
			// More content types...
		],
	},
	// More groups...
]
```

This structure is used to generate the sidebar navigation in the admin interface.

## Error Handling

Error handling for routes includes:

1. **Not Found Pages**:
   - Global not found page: `app/not-found.tsx`
   - Language-specific not found page: `app/[lng]/not-found.tsx`
2. **Catch-all Routes**:

   - `app/[lng]/[...rest]/page.tsx` catches undefined routes and redirects to the not found page

3. **Dynamic Validation**:
   - Routes like `/content-manager/[...slug]` validate the parameters and call `notFound()` if invalid

## Layout Hierarchy

The app uses a nested layout structure:

1. **Root Layout** (`app/layout.tsx`): Sets up global providers and styles
2. **Language Layout** (`app/[lng]/layout.tsx`): Sets up language-specific context
3. **Group Layouts**:
   - Dashboard Layout (`app/[lng]/(dashboard)/layout.tsx`): Admin interface layout
   - Page Builder Layout (`app/[lng]/(pagebuilder)/layout.tsx`): Page builder interface layout

Each layout wraps its children with appropriate context providers and UI elements.
