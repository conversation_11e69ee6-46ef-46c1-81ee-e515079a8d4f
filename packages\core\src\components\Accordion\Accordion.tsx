'use client'
import cn from 'classnames'
import React, { useRef, useState } from 'react'
import { useAnim, useIsomorphicLayoutEffect, useWindowDimensions } from '../../hooks'
import styles from './accordion.module.scss'

export const Accordion = ({ children }: { children: React.ReactNode }) => {
	return <div className={cn(styles.accordion, 'accordion')}>{children}</div>
}

export const AccordionItem = ({
	title,
	children,
	onActive,
	isActive = false,
	isDisabled = false,
	icon,
}: {
	title: React.ReactNode
	children: React.ReactNode
	onActive?: () => void
	isActive?: boolean
	isDisabled?: boolean
	icon?: React.ReactNode
}) => {
	const [active, setActive] = useState(isActive)
	const [contentHeight, setContentHeight] = useState(0)
	const contentRef = useRef<HTMLDivElement>(null)
	const windowDimensions = useWindowDimensions()
	const { current: blockProps } = useRef({
		config: {
			paused: true,
		},
		timeline: [
			{
				targets: '.accordion__content',
				vars: {
					fromTo: [
						{ height: 0 },
						{
							height: 'auto',
							duration: 0.5,
							ease: 'power3.inOut',
						},
					],
				},
			},
		],
	})

	const [block, tl] = useAnim({ ...blockProps, deps: [contentHeight] })

	useIsomorphicLayoutEffect(() => {
		if (!contentRef.current) return
		const innerContent = contentRef.current.firstElementChild as HTMLElement
		if (contentHeight !== innerContent.clientHeight) setContentHeight(innerContent.clientHeight)
	}, [windowDimensions])

	useIsomorphicLayoutEffect(() => {
		active ? tl.play() : tl.reverse()
	}, [active, tl])

	useIsomorphicLayoutEffect(() => {
		setActive(isActive)
	}, [isActive])

	const handleActive = () => {
		if (isDisabled) return
		setActive(!active)
		onActive?.()
	}

	return (
		<div className={cn(styles.accordion__item, 'accordion__item')} ref={contentRef}>
			<button
				className={cn(styles.trigger, 'accordion__trigger', active && 'is__active')}
				onClick={handleActive}
			>
				<h4 className="accordion__title">{title}</h4>
				{icon ? (
					icon
				) : (
					<svg x="0px" y="0px" viewBox="0 0 14 14" className="plus__icon svg__icon accordion__icon">
						<rect
							className="plus__icon-horizontal"
							y="6"
							width="14"
							height="2"
							fill="currentColor"
						></rect>
						<rect
							className="plus__icon-vertical"
							x="6"
							width="2"
							height="14"
							fill="currentColor"
						></rect>
					</svg>
				)}
			</button>
			<div
				className="accordion__content"
				style={{ height: 0, overflow: 'hidden', willChange: 'auto' }}
				ref={block}
			>
				<div className="content__inner">{children}</div>
			</div>
		</div>
	)
}
