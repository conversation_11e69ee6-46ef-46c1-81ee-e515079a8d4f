'use client'

import {
	Accordion,
	AccordionI<PERSON>,
	But<PERSON>,
	checkIsLoginClient,
	getCookie,
	Icon,
	Image,
	setCookie,
	useAnim,
	useIsomorphicLayoutEffect,
} from '@collective/core'
import { deleteCustomPath, getDataClient } from '@collective/integration-lib/cms'
import { ChatListContext } from '@collective/ui-lib/contexts/ChatListContext'
import { useModal } from '@collective/ui-lib/contexts/ModalContext'
import { NavigationContext } from '@collective/ui-lib/contexts/NavigationContext'
import { useToast } from '@collective/ui-lib/contexts/ToastContext'
import cn from 'classnames'
import { nanoid } from 'nanoid'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useContext, useRef, useState, type FC } from 'react'
// import { InitialData as catsData } from '@/mock/Categories'
import logo from '@/public/logo.svg'
import styles from './sidebar.module.scss'

type UserProps = {
	username: string
	fullName: string
	email: string
	avatar?: string
}

type ChatProps = {
	id: string
	uuid: string
	updatedAt: string
	messages: {
		content: string
		role: string
		time: string
	}[]
}

type HistoryProps = {
	label: string
	chats: ChatProps[]
}

export type SideBarProps = {
	Headline: string
	slug: string
	Pages: {
		data: {
			attributes: {
				Headline: string
				slug: string
			}
		}[]
	}
}

const UserSection = () => {
	const [data, setData] = useState<UserProps | null>(null)
	useIsomorphicLayoutEffect(() => {
		const getUser = async () => {
			// Check login
			const user = await checkIsLoginClient()

			if (user !== null) {
				const data = await getDataClient(`users/me`, user?.strapiToken, 4)
				setData({
					username: data.username,
					email: data.email,
					fullName: data.fullName,
					avatar: data.avatar,
				})
			} else {
				localStorage.removeItem('token')
				sessionStorage.removeItem('token')
				setCookie('token', '', 0)
			}
		}
		getUser()
	}, [])

	const handleSignOut = () => {
		localStorage.removeItem('token')
		sessionStorage.removeItem('token')
		setCookie('token', '', 0)
		setData(null)
		window.location.reload()
	}
	return (
		<div className={styles.user}>
			<Image src={data?.avatar || '/CoAI.png'} alt="Avatar" className={styles.user__avatar} />
			<div className={styles.user__info}>
				<span className={styles.user__username}>{data?.fullName}</span>
				<span className={styles.user__email}>{data?.email}</span>
			</div>
			<Button onClick={handleSignOut} className={cn(styles.user__signout)}>
				<Icon variant="signout" />
			</Button>
		</div>
	)
}

const filteredHistory = (chatsData: ChatProps[]): HistoryProps[] => {
	const now = new Date()
	const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
	const yesterday = new Date(today)
	yesterday.setDate(yesterday.getDate() - 1)
	const sevenDaysAgo = new Date(today)
	sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
	const thirtyDaysAgo = new Date(today)
	thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

	const lastMonth = new Date(today)
	lastMonth.setMonth(lastMonth.getMonth() - 1)
	const lastMonthLabel = lastMonth.toLocaleString('en-US', { month: 'long' })

	const filtered: Record<string, ChatProps[]> = {
		Today: [],
		Yesterday: [],
		'Previous 7 days': [],
		'Previous 30 days': [],
		[lastMonthLabel]: [],
	}

	chatsData &&
		chatsData?.forEach((chat) => {
			const updatedDate = new Date(chat.updatedAt)

			if (updatedDate >= today) {
				filtered['Today']?.push(chat)
			} else if (updatedDate >= yesterday && updatedDate < today) {
				filtered['Yesterday']?.push(chat)
			} else if (updatedDate >= sevenDaysAgo && updatedDate < yesterday) {
				filtered['Previous 7 days']?.push(chat)
			} else if (updatedDate >= thirtyDaysAgo && updatedDate < sevenDaysAgo) {
				filtered['Previous 30 days']?.push(chat)
			} else if (
				updatedDate.getFullYear() === lastMonth.getFullYear() &&
				updatedDate.getMonth() === lastMonth.getMonth()
			) {
				filtered[lastMonthLabel]?.push(chat)
			}
		})

	return Object.entries(filtered)
		.filter(([, chats]) => chats.length > 0)
		.map(([label, chats]) => ({ label, chats: chats }))
}

const History: FC = () => {
	const chatListContext = useContext(ChatListContext)
	const { chatList, getNewChatList } = chatListContext
	const { onOpenModal, onCloseModal } = useModal()
	const { onPushToast } = useToast()
	const filteredHistories = filteredHistory(chatList)
	const router = useRouter()

	const handleDeleteChat = (chatID: string, chatTitle?: string) => {
		onOpenModal('confirm', {
			heading: 'Delete chat?',
			message: (
				<>
					This will delete <strong>{chatTitle}</strong>
				</>
			),
			onConfirm: async () => {
				try {
					const res = await deleteCustomPath<{ id: string }>(`/api/deleteChat?id=${chatID}`)
					getNewChatList()
					router.push('/cosmo')
					onCloseModal()
					onPushToast({
						tId: nanoid(),
						type: 'success',
						icon: 'tick',
						message: (
							<>
								Delete <strong>{chatTitle}</strong> successful!
							</>
						),
					})
					return res
				} catch (error) {
					onPushToast({
						tId: nanoid(),
						type: 'error',
						icon: 'x',
						message: (
							<>
								Failed to delete <strong>{chatTitle}</strong> unsuccessful! {error}
							</>
						),
					})
					console.error('Failed to delete chat:', error)
				}
			},
		})
	}

	useIsomorphicLayoutEffect(() => {
		getNewChatList()
	}, [])

	return filteredHistories.length === 0 ? (
		<div className={styles.history__notfound}>
			<Icon variant="empty" />
			<p>
				So quiet here... Send your first message, pick a suggested prompt, or sign in to continue
				where you left off!
			</p>
		</div>
	) : (
		<ul className={styles.history}>
			{filteredHistories &&
				filteredHistories?.map((item, idx) => (
					<li key={idx} className={styles.history__group}>
						<span className={styles.history__label}>{item.label}</span>
						<ul className={styles.history__items}>
							{item.chats.map((chat) => (
								<li
									key={chat.uuid}
									className={styles.history__item}
									title={chat.messages[0]?.content}
								>
									<Link href={`/cosmo/${chat.uuid}`} prefetch={false}>
										{chat.messages[0]?.content ? chat.messages[0]?.content : '[Chat Content]'}
									</Link>
									<div className={styles.more}>
										<Button
											className={styles.more__trigger}
											title="Delete this chat"
											onClick={(e) => {
												e.preventDefault()
												handleDeleteChat(chat.id, chat.messages[0]?.content)
											}}
										>
											{/* <Icon variant="more" /> */}
											<Icon variant="delete" />
										</Button>
										{/* <div className={styles.more__menu}>
											<ul className={styles.more__menu__wrapper}>
												<li className={styles.more__menu__item}>
													<Icon variant="delete" /> Delete
												</li>
											</ul>
										</div> */}
									</div>
								</li>
							))}
						</ul>
					</li>
				))}
		</ul>
	)
}

export const Sidebar = () => {
	const pagesData = useContext(NavigationContext)
	const { onOpenModal } = useModal()
	const router = useRouter()
	const pathname = usePathname()
	const [active, setActive] = useState(-1)
	const [switchBar, setSwitchBar] = useState(0)
	const [token, setToken] = useState<string | null | undefined>(null)

	useIsomorphicLayoutEffect(() => {
		setToken(localStorage.getItem('token') || sessionStorage.getItem('token') || getCookie('token'))
	}, [])

	useIsomorphicLayoutEffect(() => {
		const normalizedRoutes = pagesData.reduce(
			(arr: { path: string; children: string[] }[], route) => {
				const subRoutes = route?.children?.map((item) => item.path)
				arr.push({
					path: route.path,
					children: subRoutes || [],
				})
				return arr
			},
			[]
		)

		pathname === '/cosmo' && setSwitchBar(1)

		const activeIndex = normalizedRoutes.findIndex(
			({ path, children }) =>
				// pathname.includes(path) || children.some((item) => pathname.includes(item))
				pathname === `/${path}` ||
				pathname.startsWith(`/${path}/`) ||
				children.some((item) => pathname === `/${item}` || pathname.startsWith(`/${item}/`))
		)

		setActive(activeIndex)
	}, [pathname])

	const { current: switchProps } = useRef({
		config: {
			paused: true,
		},
		timeline: [
			{
				targets: '.switch__bar',
				vars: {
					fromTo: [
						{
							left: '0%',
							right: '50%',
							duration: 0.5,
							ease: 'power3.inOut',
						},
						{
							left: '50%',
							right: '0%',
							duration: 0.5,
							ease: 'power3.inOut',
						},
					],
				},
			},
		],
	})

	const [switchRef, tl] = useAnim({ ...switchProps })

	useIsomorphicLayoutEffect(() => {
		switchBar === 0 ? tl.reverse() : tl.play()
	}, [switchBar])

	const { onPushToast } = useToast()

	useIsomorphicLayoutEffect(() => {
		if (pathname.includes('/cosmo')) {
			setSwitchBar(1)
		} else {
			setSwitchBar(0)
		}
		// onPushToast({
		// 	type: 'success',
		// 	icon: 'tick',
		// 	message: <>Test</>,
		// })
	}, [pathname])

	return (
		<aside className="aidigi__sidebar">
			<div className={styles.brand__logo}>
				<Link href={'/'}>
					<Image src={logo} alt="" priority loading="eager" />
				</Link>
			</div>
			<div className={styles.routes}>
				<div className={styles.switch} ref={switchRef}>
					<span className="switch__bar" />
					<Link
						href="/"
						className={cn(
							switchBar === 0 && pathname !== '/cosmo' && styles.active,
							'aidigi__paragraph--md'
						)}
						onClick={() => setSwitchBar(0)}
					>
						<Icon variant="book-open" /> Guideline
					</Link>
					<Link
						href="/cosmo"
						className={cn(
							switchBar === 1 && pathname.includes('/cosmo') && styles.active,
							'aidigi__paragraph--md'
						)}
						onClick={() => setSwitchBar(1)}
					>
						<Icon variant="star" /> Cōsmo
					</Link>
				</div>
				{switchBar === 1 && pathname.includes('/cosmo') && (
					<div className={styles.newchat}>
						<Button onClick={() => router.push('/cosmo')}>
							<Icon variant="new" />
							New chat
						</Button>
					</div>
				)}
				<nav className={cn(styles.navigation, switchBar === 1 ? styles.trimleft : '')}>
					{switchBar === 0 ? (
						<Accordion>
							{pagesData.map((route, index) => {
								const handleClick = (e: React.MouseEvent) => {
									if (route.isLocked && !token) {
										e.preventDefault()
										onOpenModal('signin')
									}
								}
								return (
									<AccordionItem
										key={index}
										isActive={route.children && route.children.length > 0 && index === active}
										onActive={() =>
											!route.isLocked &&
											token &&
											route.path !== pathname &&
											router.push(`/${route.path}`)
										}
										// Arrcodion still active when go from subpage to main page
										isDisabled={
											route.isLocked ||
											!route.children ||
											route.children.length === 0 ||
											index === active
										}
										title={
											<Link
												scroll={false}
												className={cn(index === active && styles.active)}
												href={`/${route.path}`}
												onClick={handleClick}
											>
												{route.isLocked && !token && (
													<Icon variant="padlock" className={styles.lock} />
												)}
												<span>{route.label}</span>
											</Link>
										}
										icon={
											route.children && route.children.length > 0 ? (
												<Icon variant="right-arrow" className={styles.arrow} />
											) : (
												<div></div>
											)
										}
									>
										{route.children && (
											<ul className={styles.sub__route}>
												{route.children.map((child, childIndex) => {
													const handleClick = (e: React.MouseEvent) => {
														if (child.isLocked && !token) {
															e.preventDefault()
															onOpenModal('signin')
														}
													}
													return (
														<li key={childIndex} className="aidigi__heading--h6">
															<Link
																className={cn(pathname.includes(child.path) && styles.active)}
																scroll={false}
																href={`/${route.path}/${child.path}`}
																onClick={handleClick}
															>
																{(child.isLocked || route.isLocked) && !token && (
																	<Icon variant="padlock" className={styles.lock} />
																)}
																<span>{child.label}</span>
															</Link>
														</li>
													)
												})}
											</ul>
										)}
									</AccordionItem>
								)
							})}
						</Accordion>
					) : (
						<History />
					)}
				</nav>
				<div className={styles.bottom__mask} />
			</div>
			<div className={styles.account}>
				{token ? (
					<UserSection />
				) : (
					<Button
						onClick={() => onOpenModal('signin')}
						className="aidigi__button outline account__button"
					>
						Sign in
					</Button>
				)}
			</div>
		</aside>
	)
}
