// import { useDebounce, Icon } from '@collective/core'
import { debounce, Icon } from '@collective/core'
import cn from 'classnames'
import { useIsomorphicLayoutEffect } from 'framer-motion'
import Link from 'next/link'
import objectHash from 'object-hash'
import { useContext, useMemo, useState } from 'react'
import { LayoutSwitch } from '@/components/Builder/LayoutSwitch'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import { NavigationData } from '@/mock/Navigation'
import {
	publishCmsAdminPageDocument,
	putCmsAdminPageDocument,
	type IResultDataProps,
} from 'common/cms'
import styles from './pagebuilderlayout.module.scss'

const hashOptions: objectHash.NormalOption = {
	algorithm: 'sha1',
	unorderedArrays: true,
}

export const ToolbarLayout = () => {
	const context = useContext(PageBuilderContext)
	const { expandedSidebar, setExpandedSidebar, data, setData, slug: pageSlug } = context
	const [workingState, setWorkingState] = useState<'saving' | 'publishing' | 'done'>('done')
	const { data: rawData } = data ?? {}
	const pageData = rawData as unknown as IResultDataProps
	const [tmpData, setTmpData] = useState(pageData as IResultDataProps)
	const tmpDataHash = useMemo(() => objectHash(tmpData, hashOptions), [tmpData])
	const pageDataHash = useMemo(() => objectHash(pageData, hashOptions), [pageData])

	const currentNav = NavigationData.find((nav) => nav.apiId === pageSlug[0])
	const currentType = currentNav?.layouts.find((layout) => layout.apiId === pageSlug[1])

	const debouncePublish = debounce(async () => {
		setWorkingState('publishing')
		if (!currentType) {
			return
		}
		// Remove status from data as it will cause an error when saving
		const { status, ...rest } = pageData
		const response = await publishCmsAdminPageDocument({
			kind: currentType.kind,
			uid: currentType.uid || '',
			documentId: pageData?.documentId || '',
			data: { ...rest },
		})
		setData({ data: response.data })
		setTmpData(response.data)
		setWorkingState('done')
	}, 250)

	const debounceSave = debounce(async () => {
		if (!currentType) {
			setWorkingState('done')
			return
		}
		// Check if the data has changed, if not, do not save
		if (pageDataHash === tmpDataHash) {
			setWorkingState('done')
			return
		}
		// Remove status from data as it will cause an error when saving
		const { status, ...rest } = pageData
		const response = await putCmsAdminPageDocument({
			kind: currentType.kind,
			uid: currentType.uid,
			documentId: pageData?.documentId || '',
			data: { ...rest },
		})
		setData({ data: response.data })
		setTmpData(response.data)
		setWorkingState('done')
	}, 250)

	useIsomorphicLayoutEffect(() => {
		if (pageData?.status !== 'published') {
			setWorkingState('saving')
			debounceSave()
		}
	}, [pageData])

	return (
		<header className={styles.header}>
			<div className={cn(styles.left__actions, 'flex__center')}>
				<button
					title="Toggle sidebar"
					className={styles.trigger__sidebar}
					onClick={() => setExpandedSidebar({ ...expandedSidebar, left: !expandedSidebar.left })}
				>
					<Icon variant={!expandedSidebar.left ? 'panel-right' : 'panel-left'} type="cms" />
				</button>
				<div className={cn('flex__center', styles.history)}>
					<div className="flex__center">
						<button title="Undo">
							<Icon variant="undo" type="cms" />
						</button>
						<button title="Redo">
							<Icon variant="redo" type="cms" />
						</button>
					</div>
					<p className="text__w--icon collect__body--sm" title="Edited just now">
						<Icon variant="check" type="cms" />
						{workingState === 'done' && <span>Edited just now</span>}
						{workingState === 'saving' && <span>Saving...</span>}
						{workingState === 'publishing' && <span>Publishing...</span>}
					</p>
				</div>
			</div>

			<div className={cn(styles.page__meta, 'flex__center')}>
				<div className={cn(styles.navigate, 'flex__center')}>
					<Link title="Back to dashboard" href={`/`} className="flex__center">
						<Icon variant="home" type="cms" />
					</Link>
					<div className={styles.seperator} />
					<div className={cn(styles.breadcrumb, 'flex__center')}>
						{pageSlug?.length > 1 && (
							<>
								<Link href={`/content-manager/${pageSlug[0]}`}>{pageSlug[0]}</Link> /
							</>
						)}

						<p title={pageData?.Headline} className="collect__body--md">
							{pageData?.Headline || 'Post Title'}
						</p>
					</div>
				</div>
				<div
					data-status={pageData?.status || 'draft'}
					className={cn(styles.status, 'collect__body--xs')}
				>
					{pageData?.status || 'draft'}
				</div>
			</div>

			<div className={cn(styles.right__actions, 'flex__center')}>
				<LayoutSwitch />
				<button
					onClick={() => {
						pageData?.status !== 'published' ? debouncePublish() : debounceSave()
					}}
				>
					{pageData?.status === 'published' ? 'Update' : 'Publish'}
				</button>
				<button
					title="Toggle sidebar"
					className={styles.trigger__sidebar}
					onClick={() => setExpandedSidebar({ ...expandedSidebar, right: !expandedSidebar.right })}
				>
					<Icon variant={!expandedSidebar.right ? 'panel-left' : 'panel-right'} type="cms" />
				</button>
			</div>
		</header>
	)
}
