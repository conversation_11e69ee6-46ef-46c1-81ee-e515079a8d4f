export const InitialData = {
	Components: [
		{
			id: 1,
			__component: 'common.header',
			Headline: 'Color',
		},
		{
			id: 0,
			__component: 'common.divider',
		},
		{
			id: 0,
			__component: 'common.divider',
		},
		{
			id: 3,
			__component: 'common.text-horizon',
			Headline: 'Color System',
			Paragraph:
				'Primary Logo lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
		},
		{
			id: 4,
			__component: 'common.color',
			Colors: [
				{
					ColorName: 'Primary Logo',
					HexColor: '#0A060E',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#F6F6F6',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
			],
		},
		{
			id: 4,
			__component: 'common.color',
			Colors: [
				{
					ColorName: 'Primary Logo',
					HexColor: '#F4364C',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#FF521C',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#E5FF44',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#FF60BF',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
			],
		},
		{
			id: 4,
			__component: 'common.color',
			Colors: [
				{
					ColorName: 'Primary Logo',
					HexColor: '#2CEDA8',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#3F3BBD',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#440099',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
				{
					ColorName: 'Primary Logo',
					HexColor: '#211551',
					AdditionalField: [
						{
							FieldName: 'CMYK',
							FieldValue: '60, 50, 50, 100',
						},
						{
							FieldName: 'PMS',
							FieldValue: 'Black C',
						},
					],
				},
			],
		},
		{
			id: 0,
			__component: 'common.divider',
		},
		{
			id: 3,
			__component: 'common.text-horizon',
			Headline: 'Team Photoshoot',
			Paragraph:
				'Primary Logo lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
		},
		{
			id: 4,
			__component: 'common.media',
			Media: [
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-1.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Use natural light and architectural backdrops, ensuring relaxed poses and attire that reflects the brand’s personality.',
				},
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-2.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Position the subject in soft, directional lighting with minimal background distractions and natural, confident poses.',
				},
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-3.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Highlight the subject with natural shadows, complementary attire, and clean, architectural surroundings.',
				},
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-4.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Use natural light and architectural backdrops, ensuring relaxed poses and attire that reflects the brand’s personality.',
				},
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-5.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Position the subject in soft, directional lighting with minimal background distractions and natural, confident poses.',
				},
				{
					Thumbnail: {
						data: {
							attributes: {
								url: '/photo-6.png',
								width: 0,
								height: 0,
							},
						},
					},
					// IsFullScale: true,
					Caption:
						'Highlight the subject with natural shadows, complementary attire, and clean, architectural surroundings.',
				},
			],
		},
	],
}
