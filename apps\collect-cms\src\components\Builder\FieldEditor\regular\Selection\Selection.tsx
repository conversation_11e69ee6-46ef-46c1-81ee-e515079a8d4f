import {
	Icon,
	Select,
	SelectItem,
	useIsomorphicLayoutEffect,
	type OptionProps,
} from '@collective/core'
import cn from 'classnames'
import { useState } from 'react'
import type { FieldProps } from '../../FieldEditor'
import styles from './selection.module.scss'

export interface SelectionProps<T> extends FieldProps<T> {
	value?: T
	enum: string[]
	options: string[]
	onChange: (props: { field: string; value: string | number | (string | number)[] }) => void
}

export const Selection = <T,>(props: SelectionProps<T>) => {
	const { required, value, onChange, name, placeholder, enum: enumOptions, options } = props
	const selectionMode = Array.isArray(value) ? 'multiple' : 'single'
	// console.log(value)
	const reformatedValue = () => {
		// Handle null or undefined value
		if (value === null || value === undefined) {
			// For multiple selection, return empty array
			if (selectionMode === 'multiple') {
				return []
			}
			// For single selection, return undefined to use placeholder
			return undefined
		}

		// Handle array values
		if (Array.isArray(value)) {
			return value.map((v) => ({ label: v, value: v }))
		}

		// Handle single value
		return { label: value, value: value }
	}
	const [propsValue, setPropsValue] = useState(reformatedValue)
	// console.log(props)

	useIsomorphicLayoutEffect(() => {
		setPropsValue(reformatedValue)
	}, [value])

	// Determine if the component should be disabled (when value is null)
	const isDisabled = value === null

	// console.log(value, propsValue, isDisabled)

	return (
		<div className={cn(styles.wrapper, isDisabled && styles.disabled)}>
			<Select
				className="collect__input has__border"
				required={required}
				mode={selectionMode}
				endIcon={<Icon type="cms" variant="chevron-down" />}
				placeholder={isDisabled ? 'Not available' : placeholder}
				defaultOption={propsValue as OptionProps | OptionProps[]}
				onChange={(e) => {
					// If disabled, don't process changes
					if (isDisabled) return

					// console.log(e)
					const values = Array.isArray(e) ? e.map((item) => item.value) : e?.value

					onChange?.({
						field: name as string,
						value: selectionMode === 'multiple' ? values : values,
					})
				}}
			>
				{isDisabled ? (
					<SelectItem value="Not available">Not available</SelectItem>
				) : (
					(enumOptions || options).map((option, idx) => (
						<SelectItem key={idx} value={option}>
							{option}
						</SelectItem>
					))
				)}
			</Select>
		</div>
	)
}
