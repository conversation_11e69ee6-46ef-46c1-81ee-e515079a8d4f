'use client';
import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useState, useEffect, useMemo } from 'react';
const defaultContext = {
    toggleDark: () => {
        console.warn('Should have been overriden');
    },
    isDark: true,
};
const ThemeContext = createContext(defaultContext);
export const ThemeContextProvider = ({ children }) => {
    const [isDark, setIsDark] = useState(true);
    useEffect(() => {
        const localStore = localStorage.getItem('ThemeContext:isDark');
        if (localStore !== undefined && localStore !== null) {
            const lsDark = JSON.parse(localStore);
            setIsDark(lsDark);
        }
        else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
            setIsDark(false);
        }
    }, []);
    const context = useMemo(() => ({
        toggleDark: () => {
            localStorage.setItem('ThemeContext:isDark', String(!isDark));
            setIsDark(!isDark);
        },
        isDark,
    }), [isDark]);
    return _jsx(ThemeContext.Provider, { value: context, children: children });
};
