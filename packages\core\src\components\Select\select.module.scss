@use '@/styles/config' as *;

.wrapper {
	--dropdown-bg-color: #{color('white')};
	--dropdown-bg-active-color: #{color('primary')};
	--dropdown-text-color: #{color('dark')};
	--dropdown-text-active-color: #{color('white')};
	--dropdown-gap: #{spacing('s2')};
	--dropdown-max-height: #{px-to(208px, rem)};
	--dropdown-shadow-color: rgba(0, 0, 0, 0.25);
	--required-mark: red;

	.mark {
		color: var(--required-mark);
	}

	position: absolute;
	left: 0;
	top: 0;
	z-index: 9;
	background-color: var(--dropdown-bg-color);
	min-width: max-content;
	max-height: var(--dropdown-max-height);
	overflow: auto;
	animation-duration: 0.367s;
	animation-timing-function: cubic-bezier(0.1, 0.9, 0.2, 1);
	animation-fill-mode: both;
	color: var(--dropdown-text-color);
	box-shadow: 0 4px 4px 0 var(--dropdown-shadow-color);

	&.drawer__top {
		animation-name: css-0, css-12;
		margin-top: var(--dropdown-gap);
		border-radius: 0 0 px-to(5px, rem) px-to(5px, rem);
	}

	&.drawer__bottom {
		animation-name: css-0, css-13;
		margin-top: calc(-1 * var(--dropdown-gap));
		border-radius: px-to(5px, rem) px-to(5px, rem) 0 0;
	}

	.option {
		padding: spacing('s3') spacing('s4');
		cursor: pointer;
		display: flex;
		align-items: center;
		gap: spacing('s2');
		font-weight: bold;

		@include fluid($font-size) {
			font-size: size('paragraph', 'sm');
		}

		transition: 0.2s;

		&:hover,
		&.active {
			color: var(--dropdown-text-active-color);
			background-color: var(--dropdown-bg-active-color);
		}
	}
}

@keyframes css-0 {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes css-12 {
	0% {
		transform: translate3d(0, -10px, 0);
		pointer-events: none;
	}

	100% {
		transform: translate3d(0, 0, 0);
		pointer-events: auto;
	}
}

@keyframes css-13 {
	0% {
		transform: translate3d(0, 10px, 0);
		pointer-events: none;
	}

	100% {
		transform: translate3d(0, 0, 0);
		pointer-events: auto;
	}
}
