import { Input, TextArea, useIsomorphicLayoutEffect } from '@collective/core'
import { useMemo, useState } from 'react'
import type { FieldProps } from '../../FieldEditor'
import styles from './longtext.module.scss'

export interface LongTextProps<T> extends FieldProps<T> {
	value?: T
	minLength?: number
	maxLength?: number
	onChange: (props: { field: string; value: string }) => void
}

export const LongText = <T,>(props: LongTextProps<T>) => {
	const { required, value, onChange, name, placeholder, minLength, maxLength } = props
	const [propsValue, setPropsValue] = useState(value ?? '')
	const [count, setCount] = useState(0)

	// console.log(name, propsValue, props)
	useIsomorphicLayoutEffect(() => {
		setPropsValue(value ?? '')
		propsValue && setCount((propsValue as string).length)
	}, [value])

	return (
		<div className={styles.wrapper}>
			<TextArea
				className="collect__textarea"
				required={required}
				value={propsValue as string}
				placeholder={placeholder}
				minLength={minLength}
				maxLength={maxLength}
				onChange={(e) => {
					setPropsValue(e.target.value)
					onChange?.({ field: name as string, value: e.target.value })
					setCount(e.target.value.length)
				}}
			/>
			<span className={styles.counter}>{`${count}/${maxLength} Characters`}</span>
		</div>
	)
}
