export function debounce<Args extends unknown[]>(fn: (...args: Args) => void, delay: number) {
	let timeoutID: number | undefined
	let lastArgs: Args | undefined

	const run = () => {
		if (lastArgs) {
			fn(...lastArgs)
			lastArgs = undefined
		}
	}

	const debounced = (...args: Args) => {
		clearTimeout(timeoutID)
		lastArgs = args
		timeoutID = window.setTimeout(run, delay)
	}

	debounced.clear = () => {
		clearTimeout(timeoutID)
		run()
	}

	return debounced
}

export function throttle<Args extends unknown[]>(fn: (...args: Args) => void, cooldown: number) {
	let lastArgs: Args | undefined

	const run = () => {
		if (lastArgs) {
			fn(...lastArgs)
			lastArgs = undefined
		}
	}

	const throttled = (...args: Args) => {
		const isOnCooldown = !!lastArgs

		lastArgs = args

		if (isOnCooldown) {
			return
		}

		window.setTimeout(run, cooldown)
	}

	return throttled
}
