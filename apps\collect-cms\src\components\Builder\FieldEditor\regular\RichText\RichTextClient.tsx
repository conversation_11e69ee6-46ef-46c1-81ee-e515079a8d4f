'use client'

import { CKEditor } from '@ckeditor/ckeditor5-react'
import {
	ClassicEditor,
	SourceEditing,
	Bold,
	Italic,
	Underline,
	Strikethrough,
	Link,
	Code,
	Markdown,
	Paragraph,
	Essentials,
} from 'ckeditor5'
import { useEffect, useRef, useState } from 'react'
import type { RichTextProps } from './RichText'
import 'ckeditor5/ckeditor5.css'
import styles from './richtext.module.scss'

// Define interfaces for common object structures
interface ContentObject {
	content: string
}

interface HtmlObject {
	html: string
}

interface TextObject {
	text: string
}

// Define a type for the editor instance
// This is a simplified version of the actual CKEditor type
interface EditorInstance {
	getData: () => string
	setData: (data: string) => void
	commands: {
		get: (name: string) =>
			| {
					value: unknown
					execute: () => void
			  }
			| undefined
	}
}

export const RichTextClient = <T,>(props: RichTextProps<T>) => {
	const { type, value, onChange, name } = props
	const propsType = type ?? ''

	// Convert value to string safely, handling objects and other types
	let stringValue = ''
	if (typeof value === 'string') {
		stringValue = value
	} else if (typeof value === 'object' && value !== null) {
		// Try to extract content from common object structures
		if ('content' in value && typeof (value as ContentObject).content === 'string') {
			stringValue = (value as ContentObject).content
		} else if ('html' in value && typeof (value as HtmlObject).html === 'string') {
			stringValue = (value as HtmlObject).html
		} else if ('text' in value && typeof (value as TextObject).text === 'string') {
			stringValue = (value as TextObject).text
		} else {
			// Try to stringify the object if it's not null
			try {
				stringValue = JSON.stringify(value)
			} catch (e) {
				// console.error('[RichTextClient] Failed to stringify object value:', e)
			}
		}
	} else if (value !== null && value !== undefined) {
		// Convert numbers or other primitive types to string
		stringValue = String(value)
	}
	// console.log(
	// 	'[RichTextClient] Initial stringValue:',
	// 	stringValue,
	// 	'from value:',
	// 	value,
	// 	'type:',
	// 	typeof value
	// )

	// Use a ref to track if this is the first render
	const isFirstRender = useRef(true)

	// Store the editor instance
	const editorRef = useRef<EditorInstance | null>(null)

	// Local state for the editor content
	const [editorContent, setEditorContent] = useState<string>(stringValue)
	// console.log('[RichTextClient] Initial editorContent state set to:', editorContent)

	// Debug the incoming props in detail
	useEffect(() => {
		// console.log('[RichTextClient] Props changed:', {
		// 	type,
		// 	value,
		// 	valueType: typeof value,
		// 	valueIsEmpty: value === '',
		// 	valueIsUndefined: value === undefined,
		// 	valueIsNull: value === null,
		// 	valueStringified: JSON.stringify(value),
		// 	name,
		// 	currentEditorContent: editorContent,
		// 	editorInitialized: editorRef.current !== null,
		// 	isFirstRender: isFirstRender.current,
		// })
		// If value is an object, log its structure
		// if (typeof value === 'object' && value !== null) {
		// 	console.log('[RichTextClient] Value is an object:', value)
		// }
	}, [type, value, name, editorContent])

	// Track if changes are coming from internal onChange
	const isInternalChange = useRef(false)

	// Handle changes to the value prop
	useEffect(() => {
		// console.log('[RichTextClient] Value effect running with:', {
		// 	value,
		// 	editorContent,
		// 	isFirstRender: isFirstRender.current,
		// 	editorInitialized: editorRef.current !== null,
		// 	isInternalChange: isInternalChange.current,
		// })

		// Skip the first render since we already initialized with the value
		if (isFirstRender.current) {
			// console.log('[RichTextClient] Skipping first render effect')
			isFirstRender.current = false
			return
		}

		// Skip if this update was triggered by our own onChange handler
		if (isInternalChange.current) {
			// console.log('[RichTextClient] Skipping update because it was triggered internally')
			isInternalChange.current = false
			return
		}

		// Only update if value is a string and different from current content
		if (typeof value === 'string' && value !== editorContent) {
			// console.log('[RichTextClient] Updating editor content from prop:', value)
			setEditorContent(value)

			// If editor is initialized, update its content
			if (editorRef.current) {
				// console.log('[RichTextClient] Setting editor data:', value)
				editorRef.current.setData(value)

				// Verify the update worked
				// setTimeout(() => {
				// 	if (editorRef.current) {
				// 		console.log('[RichTextClient] Editor data after update:', editorRef.current.getData())
				// 	}
				// }, 100)
			} else {
				// console.warn('[RichTextClient] Editor not initialized yet, cannot update content')
			}
		} else {
			// console.log('[RichTextClient] No update needed:', {
			// 	valueIsString: typeof value === 'string',
			// 	valueMatchesContent: value === editorContent,
			// })
		}
	}, [value, editorContent])

	const config = {
		licenseKey: 'GPL',
		plugins: [
			Bold,
			Italic,
			Underline,
			Strikethrough,
			Link,
			Code,
			SourceEditing,
			Markdown,
			Paragraph,
			Essentials,
		],
		toolbar: ['bold', 'italic', 'underline', 'strikethrough', '|', 'link', '|', 'sourceEditing'],
	}

	// Log right before rendering
	// console.log('[RichTextClient] About to render CKEditor with data:', editorContent)

	return (
		<div className={styles.wrapper}>
			<CKEditor
				editor={ClassicEditor}
				config={config}
				data={editorContent}
				onReady={(editor) => {
					// console.log('[RichTextClient] CKEditor onReady called')

					// Store editor instance in ref
					editorRef.current = editor

					// Check if the editor has the correct data
					const currentData = editor.getData()
					// console.log('[RichTextClient] CKEditor initialized with data:', {
					// 	currentData,
					// 	expectedData: editorContent,
					// 	dataMatches: currentData === editorContent,
					// })

					// If data doesn't match what we expect, try to set it
					if (currentData !== editorContent && editorContent) {
						// console.log('[RichTextClient] Fixing initial data in onReady')
						editor.setData(editorContent)

						// Verify the fix worked
						// setTimeout(() => {
						// 	// Make sure editor is still available
						// 	if (editor) {
						// 		const editorData = editor.getData()
						// 		console.log('[RichTextClient] Data after fixing in onReady:', editorData)
						// 		// Also log the current state of the component
						// 		console.log('[RichTextClient] Current component state:', {
						// 			editorContent,
						// 			editorInitialized: !!editorRef.current,
						// 			editorHasData: editorData && editorData.length > 0,
						// 		})
						// 	}
						// }, 100)
					}

					if (propsType === 'json' && editor && editor.commands) {
						// console.log('[RichTextClient] Setting source editing mode')
						const command = editor.commands.get('sourceEditing')
						if (command && !command.value) {
							command.execute()
						}
					}
				}}
				onChange={(_, editor) => {
					// Make sure editor is available
					if (!editor) {
						// console.warn('[RichTextClient] onChange called but editor is undefined')
						return
					}

					const data = editor.getData()
					// console.log('[RichTextClient] onChange fired with data:', data)

					// Mark that this change is coming from the editor itself
					isInternalChange.current = true

					setEditorContent(data)

					// Notify parent component of change
					if (onChange) {
						// console.log('[RichTextClient] Calling parent onChange with:', {
						// 	field: name,
						// 	value: data,
						// })
						onChange({ field: name as string, value: data })
					}
				}}
			/>
		</div>
	)
}
