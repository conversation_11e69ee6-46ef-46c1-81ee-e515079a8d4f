{"name": "@collective/monorepo", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"clean:global-cache": "rimraf ./.cache", "clean:turbo": "turbo clean && turbo daemon clean && rimraf --glob '**/.turbo' '**/turbo-*.log' '**/turbo-*.log.*'", "deps:check": "npx npm-check-updates@latest --configFileName ncurc.yml --workspaces --root --mergeConfig", "deps:update": "npx npm-check-updates@latest --configFileName ncurc.yml -u --workspaces --root --mergeConfig", "g:build": "turbo run build", "g:build-cloudflare": "turbo run build-cloudflare", "g:check-dist": "turbo run check-dist", "g:format": "prettier --write \"**/*.{ts,tsx,md}\"", "g:fix-all-files": "turbo run fix-all-files", "g:lint": "TIMING=1 turbo run lint --color", "g:lint-staged-files": "lint-staged --allow-empty", "g:lint-styles": "turbo run lint-styles --color", "g:clean": "yarn clean:global-cache && yarn clean:turbo && rimraf ./out && yarn workspaces foreach -A -ptv run clean", "install:playwright": "playwright install", "install:husky": "husky", "postinstall": "run-s install:husky", "nuke:node_modules": "rimraf --glob '**/node_modules'", "dev": "yarn workspace nextjs-app dev", "dev:cms": "yarn workspace nextjs-cms dev"}, "dependencies": {"cross-env": "7.0.3", "openai": "4.85.3", "react-markdown": "10.0.0"}, "devDependencies": {"@collective/eslint-config-bases": "workspace:^", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@types/shell-quote": "1.7.5", "eslint": "8.57.1", "husky": "9.1.6", "lint-staged": "15.2.10", "npm-run-all2": "6.2.6", "plop": "^4.0.1", "prettier": "3.5.3", "rimraf": "6.0.1", "shell-quote": "1.8.1", "turbo": "2.4.4", "typescript": "5.8.2", "wrangler": "4.6.0"}, "engines": {"node": ">=18.17.0", "yarn": ">=1.22.0", "npm": "please-use-yarn"}, "resolutions": {"@types/react": "^18.3.3", "@types/react-dom": "^18.2.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "packageManager": "yarn@4.6.0"}