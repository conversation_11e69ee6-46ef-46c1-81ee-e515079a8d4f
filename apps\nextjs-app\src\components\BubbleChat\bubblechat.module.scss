@use '@collective/ui-lib/styles/config' as *;

.chat {
	cursor: pointer;
	position: fixed;
	right: spacing(s10);
	bottom: spacing(s12);
	width: spacing(s12);
	height: spacing(s12);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9;

	svg {
		color: #fff;
		--size: #{px-to(44px, rem)};
	}
}

.dialog {
	position: fixed;
	right: spacing(s10);
	bottom: spacing(s12);
	border-radius: px-to(25px, rem);
	background-color: color('white', 100);
	width: px-to(400px, rem);
	height: px-to(600px, rem);
	z-index: 9;
	opacity: 0;
	display: flex;
	flex-flow: column;
	overflow: hidden;

	.dialog__header {
		padding: spacing(s5) spacing(s6);
		background-color: color('black', 100);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.chat__status {
			align-items: center;
			display: flex;
			gap: px-to(15px, rem);
		}

		.avatar {
			border-radius: 50%;
			width: px-to(38px, rem);
			height: px-to(38px, rem);
			// background-color: color('white', 100);
			background-image: linear-gradient(#e0e8ff, #a5bafc);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				right: 0;
				bottom: px-to(5px, rem);
				width: spacing(s2);
				height: spacing(s2);
				border-radius: 50%;
				display: inline-block;
				background-color: #268750;
			}
		}

		p {
			@include fluid($font-size) {
				font-size: px-to(20px, rem);
			}
			font-weight: 400;
		}

		.close {
			cursor: pointer;
			border-radius: 50%;
			transition: 0.2s var(--ease-transition-2);

			&:hover {
				background-color: rgba(255, 255, 255, 0.1);
			}

			svg {
				color: #fff;
				--size: #{px-to(35px, rem)};
			}
		}
	}

	.dialog__body {
		padding: spacing(s5);
		display: grid;
		gap: spacing(s5);
		overflow: auto;
		height: 100%;
		align-content: flex-start;

		> div {
			max-width: px-to(314px, rem);
			color: #646464;
		}

		.message {
			display: grid;
		}

		.box__chat {
			display: grid;
			gap: spacing(s1);

			.box {
				padding: px-to(14px, rem) px-to(22px, rem);
				border-radius: px-to(10px, rem);
				line-height: 1.5;
				border: 1px solid;
			}
		}

		.time {
			font-size: spacing(s3);
			line-height: 2.5;
		}

		[data-message-role='bot'] {
			display: grid;
			grid-template-columns: auto 1fr;
			gap: spacing(s2);

			.avatar {
				width: spacing(s8);
				height: spacing(s8);
				border-radius: 50%;
				background-color: color('dark-gray', 100);
			}

			.box {
				place-self: flex-start;
				border-color: color('dark-gray', 100);
				background-color: #fff;
			}
		}

		[data-message-role='user'] {
			margin-left: auto;

			.box {
				place-self: flex-end;
				background-color: #f0f0f0;
				border-color: #f0f0f0;
				color: color('black', 100);
			}

			.time {
				place-self: flex-end;
			}
		}
	}

	.dialog__footer {
		padding: 0 spacing(s5) spacing(s3);
	}

	.chat__input {
		--input-radius: 9999px;
		--input-padding-x: #{spacing(s8)};
		--input-padding-y: #{spacing(s2)};
		--input-height: #{px-to(70px, rem)};
		--input-border-color: #e3e3e3;
		--input-bg: #fff;
		--icon-gap: #{px-to(10px, rem)};
		margin-bottom: px-to(10px, rem);

		button {
			cursor: pointer;
			pointer-events: all;
		}

		svg {
			color: color('black', 100);
			--size: #{px-to(30px, rem)};
		}

		input {
			color: #646464;

			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
			font-weight: 400;

			&:focus {
				--input-border-color: #{color('black', 50)};
			}
		}
	}

	.prompt__list {
		display: flex;
		flex-flow: wrap;
		gap: px-to(10px, rem);
		margin-bottom: px-to(10px, rem);

		button {
			cursor: pointer;
			padding: px-to(5px, rem) px-to(10px, rem);
			border-radius: px-to(10px, rem);
			background-color: color('dark-gray', 100);
			color: #646464;
		}
	}

	:global(.aidigi__buttons) {
		display: flex;
		flex-direction: column;
		gap: spacing(s2);
		margin-bottom: spacing(s3);
		* {
			width: 100%;
		}
	}

	:global(button.aidigi__link) {
		padding: 0;
		font-weight: 400;
		color: color('neutral-gray');
		&:hover {
			background: inherit;
			color: inherit;
			text-decoration: underline;
		}
	}

	:global(.exceed) {
		@include fluid($font-size) {
			font-size: ('paragraph', 'md');
		}
		font-weight: 500;
		color: color('neutral-gray');
		text-align: center;
		padding: 0 spacing(s6);
		margin-bottom: spacing(s2);
		line-height: 1.5;

		button {
			color: color('black', 100);
			padding: 0;
			&:hover {
				text-decoration: underline;
			}
		}
	}
}
