'use client'

import { Icon } from '@collective/core'
import cn from 'classnames'
import Link from 'next/link'
import { useContext } from 'react'
import { NavigationContext, type INavigationProps } from '@/contexts/NavigationContext'
import styles from './adminlayout.module.scss'

const AdminSidebarComponent = (nav: INavigationProps) => {
	const { apiId, uid, info, layouts } = nav
	const isShowmore =
		(layouts.length === 1 && layouts[0]?.kind === 'collectionType') || layouts.length > 1
	return (
		<li key={uid}>
			<button className={styles.pin}>
				<Icon variant="pin" type="cms" />
			</button>
			{/* TO-DO: choose correct link/action to direct */}
			<Link href={`/content-manager/${apiId}`} className="text-w-icon">
				<Icon variant={isShowmore ? 'box' : 'file'} type="cms" /> {info.displayName}
				{isShowmore && <Icon className={styles.chevron} variant="chevron-right" type="cms" />}
			</Link>
		</li>
	)
}

export const AdminSidebar = () => {
	const Navigation = useContext(NavigationContext)
	const pinnedNav = Navigation.filter((nav) => nav.isPinned)
	const allNav = Navigation.filter((nav) => !nav.isPinned)
	return (
		<aside>
			<div className={styles.logo__banner}>
				<Icon variant="logo" type="cms" />
			</div>
			<div className={styles.content__manager}>
				<Link target="blank" href="#" className={cn(styles.link, 'collect__body--lg')}>
					We Create Content <Icon variant="newtab" type="cms" />
				</Link>
				<div className={styles.navigation}>
					<div className={styles.navigation__block}>
						<p className="collect__label">Pinned</p>
						<ul className={styles.navigation__routes}>
							{pinnedNav.map((nav, index) => (
								<AdminSidebarComponent key={index} {...nav} />
							))}
						</ul>
					</div>
					<div className={styles.navigation__block}>
						<p className="collect__label">All pages</p>
						<ul className={styles.navigation__routes}>
							{allNav.map((nav, index) => (
								<AdminSidebarComponent key={index} {...nav} />
							))}
						</ul>
					</div>
				</div>
			</div>
		</aside>
	)
}
