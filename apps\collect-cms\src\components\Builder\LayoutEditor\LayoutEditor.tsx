import { Icon, useIsomorphicLayoutEffect, useWindowDimensions } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import { CmsWrapper } from '@collective/ui-lib/src/base/Wrapper'
import cn from 'classnames'
import React, {
	useCallback,
	useContext,
	useMemo,
	useRef,
	useState,
	type ComponentType,
} from 'react'
import { PageBuilderContext, type Entry } from '@/contexts/BuilderContext'
import { ComponentMenu } from '../ComponentMenu'
import { ComponentQuickActions } from '../ComponentQuickActions'
import { Board } from '../Dnd'
import styles from './layouteditor.module.scss'

export const LayoutEditor = ({ children }: { children: React.ReactNode }) => {
	const context = useContext(PageBuilderContext)
	const {
		data,
		contentType,
		configuration,
		setData,
		components,
		setEditingIden,
		normalizedData,
		setChildComponentData,
	} = context
	const { data: commonData } = data ?? {}
	const { data: uidConfig } = contentType ?? {}
	const { data: uiConfig } = components ?? {}

	const windowDimension = useWindowDimensions()

	// Handle Headline change
	const globalField = useMemo(() => {
		if (!contentType.data || !configuration.data) return ''
		const { settings } = configuration.data.contentType
		const mainFieldKey = settings.mainField
		return mainFieldKey
	}, [contentType, configuration])

	const [headline, setHeadline] = useState<string>(
		commonData && globalField ? (commonData[globalField as never] as string) : ''
	)
	const textareaRef = useRef<HTMLTextAreaElement>(null)

	const handleChange = (event: React.ChangeEvent) => {
		if (!textareaRef.current) return
		const target = event.target as HTMLTextAreaElement
		setHeadline(target.value)
		setData({
			...data,
			data: {
				...data.data,
				[globalField]: target.value,
			},
		})
		textareaRef.current.style.height = 'auto'
		textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
	}

	useIsomorphicLayoutEffect(() => {
		if (!textareaRef.current) return
		textareaRef.current.style.height = 'auto'
		textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
	}, [textareaRef, windowDimension])

	// Handle component menu
	const [menu, setMenu] = useState<HTMLElement | null>(null)
	const triggerMenu = (e: React.MouseEvent) => {
		const container = e.currentTarget as HTMLElement

		setMenu((prev) => {
			return prev !== container ? container : null
		})
	}

	const handleAddBlock = (component?: IComponentProps) => {
		if (!component) return setMenu(null)
		const id = Number(menu?.dataset.id)
		const defaultData = uiConfig.find((item) => item.uid === component.uid)
		const attributes = defaultData?.schema.attributes

		if (!attributes) return setMenu(null)
		const remapProps = Object.entries(attributes).reduce<Record<string, unknown>>(
			(acc, [key, value]) => {
				let newValue

				switch (value.type) {
					case 'boolean':
						newValue = value.default ?? false
						break
					case 'string':
						newValue = ''
						break
					default:
						newValue = null
						break
				}

				acc[key] = newValue
				return acc
			},
			{}
		)

		const addData = {
			__component: component.uid as string,
			...remapProps,
			__temp_key__: normalizedData.components.length + 1,
		}

		const components = normalizedData.components
		const index = components.findIndex((component) => component.__temp_key__ === id)
		components.splice(index + 1, 0, addData)

		setData({
			...data,
			data: {
				...data.data,
				components,
			},
		})
		setMenu(null)
	}

	// Get list available components
	const Modules = useMemo(() => {
		if (!uidConfig) return {}
		if (!uidConfig.schema.attributes.components) return {}
		if ('components' in uidConfig.schema.attributes.components === false) return {}
		const components: {
			[key: string]: ComponentType<IComponentProps>
		} = {}
		const arrComponents = uidConfig.schema.attributes.components.components
		arrComponents?.forEach((module) => {
			const Component = CmsWrapper({ module })
			if (Component && components) components[module] = Component
		})

		return components
	}, [uidConfig])

	// Component wrapper with hover state
	const ComponentWrapper = ({ column, index }: { column?: IComponentProps; index?: number }) => {
		const Module = column?.__component && Modules && Modules[column.__component]
		const [isHovered, setIsHovered] = useState(false)
		const handleEdit = () => {
			if (!column) return
			const id = column.id || column.__temp_key__
			setEditingIden({ key: column.__component as string, id: id as number })
			// setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })
		}
		return (
			<div
				tabIndex={0}
				role="button"
				className={styles.component__block}
				key={column?.__temp_key__}
				onClick={handleEdit}
				onKeyDown={handleEdit}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				<ComponentQuickActions
					index={index}
					id={column?.__temp_key__ as number}
					isVisible={isHovered}
				/>
				{Module ? (
					<Module {...column} />
				) : (
					<p>
						Component <b>{column?.__component}</b> failed to import/load
					</p>
				)}
			</div>
		)
	}

	// Column component for Board
	const ColumnComponent = useCallback(
		(props: { column?: IComponentProps; index?: number }) => {
			return <ComponentWrapper {...props} />
		},
		// ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[Modules, setEditingIden]
	)

	const ColumnAddBlock = useCallback(({ column }: { column?: IComponentProps }) => {
		return (
			<button
				data-id={column?.__temp_key__}
				className={cn('text__w--icon align__center add__block', styles.add__block)}
				onClick={triggerMenu}
			>
				<Icon variant="plus-circle" type="cms" />
				<span className={styles.line} />
			</button>
		)
	}, [])

	// Toggle active when trigger menu
	useIsomorphicLayoutEffect(() => {
		const allBlockBtn = document.querySelectorAll('.add__block')
		for (const button of allBlockBtn) {
			button.classList.toggle('active', menu === button)
		}
	}, [menu])

	return (
		<div className={styles.wrapper}>
			<div className={styles.header}>
				<button className={cn('text__w--icon align__center add__block', styles.add__image)}>
					<Icon variant="image" type="cms" />
					<span className="collect__body--lg">Add cover image</span>
				</button>
				<textarea
					rows={1}
					placeholder="Post Title"
					ref={textareaRef}
					className={styles.headline}
					value={headline}
					onChange={handleChange}
				/>
				<button
					data-id="0"
					className={cn('text__w--icon align__center add__block', styles.add__block)}
					onClick={triggerMenu}
				>
					<Icon variant="plus-circle" type="cms" />
					<span className="collect__heading--h6">Add block</span>
				</button>
			</div>
			{/* <div>{children}</div> */}
			<Board initial={normalizedData?.components as IComponentProps[]} className={styles.body}>
				<ColumnComponent />
				<ColumnAddBlock />
			</Board>

			<ComponentMenu trigger={menu} onClose={handleAddBlock} />
		</div>
	)
}
