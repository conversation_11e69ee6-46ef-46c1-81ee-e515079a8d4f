import * as React from 'react';
declare const variants: {
    primary: string | undefined;
    accent: string | undefined;
};
declare const sizes: {
    sm: string | undefined;
    md: string | undefined;
};
declare const shapes: {
    fill: string | undefined;
    outline: string | undefined;
};
type IconProps = {
    startIcon: React.ReactElement;
    endIcon?: never;
} | {
    endIcon: React.ReactElement;
    startIcon?: never;
} | {
    endIcon?: undefined;
    startIcon?: undefined;
};
export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
    /** Variant of button */
    variant?: keyof typeof variants;
    /** Size of button */
    size?: keyof typeof sizes;
    /** Shape of button */
    shape?: keyof typeof shapes;
    isLoading?: boolean;
    isDark?: boolean;
    href?: string;
    scroll?: boolean;
} & IconProps;
/** All type of button in CCF Project */
export declare const Button: React.ForwardRefExoticComponent<ButtonProps & React.RefAttributes<HTMLButtonElement>>;
export {};
