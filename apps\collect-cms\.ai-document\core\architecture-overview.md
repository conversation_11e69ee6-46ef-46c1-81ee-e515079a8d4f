# CollectCMS Architecture Overview

This document provides a high-level overview of the CollectCMS architecture, explaining the main components, data flow, and system organization.

## Introduction

CollectCMS is a headless CMS frontend built with Next.js, designed to integrate with a Strapi backend. It provides a user-friendly interface for content management, with features like:

- Content creation and editing
- Media management
- User authentication
- Internationalization
- Dynamic content types and components

## System Architecture

The system follows a client-server architecture:

1. **Frontend**: Next.js application (CollectCMS)
2. **Backend**: Strapi headless CMS
3. **Communication**: REST API and JWT authentication

### Key Components

```mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js App]
    NextJS --> Middleware[Authentication Middleware]
    NextJS --> LayoutSystem[Layout System]
    NextJS --> PageBuilder[Page Builder]
    NextJS --> ContentManager[Content Manager]
    NextJS --> API[API Integration Layer]
    API --> Strapi[Strapi Backend]

    subgraph "CollectCMS Frontend"
        Middleware
        LayoutSystem
        PageBuilder
        ContentManager
        API
    end
```

## Core Modules

1. **Authentication System**: Handles user login, token management, and route protection
2. **Content Manager**: Interface for managing content types and entries
3. **Page Builder**: Visual editor for creating and editing page layouts
4. **Navigation System**: Sidebar and navigation structure for the CMS
5. **API Integration Layer**: Communication with the Strapi backend

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant NextJS as Next.js App
    participant API as API Integration
    participant Strapi as Strapi Backend

    User->>Browser: Access CMS
    Browser->>NextJS: Request page
    NextJS->>API: Check authentication
    API->>Strapi: Validate token
    Strapi-->>API: Token valid/invalid

    alt Token Valid
        API-->>NextJS: Authenticated
        NextJS->>API: Fetch content types
        API->>Strapi: Request content types
        Strapi-->>API: Return content types
        API-->>NextJS: Content types data
        NextJS-->>Browser: Render CMS interface
        Browser-->>User: Display CMS
    else Token Invalid
        API-->>NextJS: Not authenticated
        NextJS-->>Browser: Redirect to login
        Browser-->>User: Show login page
    end
```

## Technology Stack

- **Frontend Framework**: Next.js with App Router
- **UI Components**: Custom React components
- **Styling**: SCSS modules
- **State Management**: React Context API
- **API Communication**: Fetch API
- **Authentication**: JWT tokens
- **Internationalization**: i18n with next-i18n-router
- **Deployment**: Supports Vercel and Cloudflare

## Directory Structure

The application follows a modular structure:

- `src/app`: Next.js App Router pages
- `src/components`: React components
- `src/common`: Common utilities and functions
- `src/contexts`: React context providers
- `src/layouts`: Layout components
- `src/styles`: Global styles and SCSS modules
- `src/mock`: Mock data for development

## Integration Points

The main integration points with the Strapi backend are:

1. **Authentication**: Login and token management
2. **Content Types**: Fetching content type definitions
3. **Content Entries**: CRUD operations on content
4. **Media Library**: Managing media assets
5. **Components**: Fetching component definitions

## Conclusion

CollectCMS provides a flexible, user-friendly interface for content management, built on modern web technologies and designed to work seamlessly with Strapi. The architecture supports extensibility, internationalization, and a component-based approach to content management.
