import type { INavigationProps } from '@/contexts/NavigationContext'

export const NavigationData: INavigationProps[] = [
	{
		apiId: 'test-really-long-string-page-group',
		uid: 'test-really-long-string-page-group',
		isDisplayed: true,
		isPinned: true,
		info: { displayName: 'Test Page Group', description: 'Test Page Group' },
		kind: 'group',
		layouts: [
			{
				name: 'Test Page',
				apiId: 'test-page-123',
				uid: 'test-page',
				visible: true,
				kind: 'singleType',
				identifierField: 'slug',
				defaultMode: 'editor',
			},
			{
				name: 'Test',
				apiId: 'test',
				uid: 'test',
				visible: true,
				kind: 'collectionType',
				identifierField: 'slug',
				defaultMode: 'builder',
			},
		],
	},
	{
		apiId: 'test-page',
		uid: 'test-page',
		isDisplayed: true,
		isPinned: true,
		info: { displayName: 'Test Page', description: 'Test Page' },
		kind: 'group',
		layouts: [
			{
				name: 'Test Page',
				apiId: 'test-page-123',
				uid: 'test-page',
				visible: true,
				kind: 'singleType',
				identifierField: 'slug',
				defaultMode: 'editor',
			},
		],
	},
	{
		apiId: 'page',
		uid: 'page-group',
		isDisplayed: true,
		isPinned: false,
		info: {
			displayName: 'Page',
			description: 'Page',
		},
		kind: 'group',
		layouts: [
			{
				name: 'Page',
				apiId: 'page',
				uid: 'page',
				visible: true,
				kind: 'collectionType',
				identifierField: 'slug',
				defaultMode: 'builder',
			},
		],
	},
	{
		apiId: 'other',
		uid: 'other',
		isDisplayed: true,
		isPinned: false,
		info: { displayName: 'Other', description: 'Other' },
		kind: 'group',
		layouts: [
			{
				name: 'Home Page',
				apiId: 'home-page',
				uid: 'home-page',
				visible: true,
				kind: 'singleType',
				identifierField: 'slug',
				defaultMode: 'editor',
			},
			{
				name: 'Test Page 2',
				apiId: 'test-page-2',
				uid: 'test-page',
				visible: true,
				kind: 'singleType',
				identifierField: 'slug',
				defaultMode: 'builder',
			},
			{
				name: 'Test',
				apiId: 'test',
				uid: 'test',
				visible: true,
				kind: 'collectionType',
				identifierField: 'slug',
				defaultMode: 'builder',
			},
			{
				name: 'Category',
				apiId: 'category',
				uid: 'category',
				visible: true,
				kind: 'collectionType',
				identifierField: 'slug',
				defaultMode: 'editor',
			},
		],
	},
]
