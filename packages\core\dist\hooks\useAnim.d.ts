import { type DependencyList, type MutableRefObject } from 'react';
export interface AnimVars {
    config?: GSAPTimelineVars;
    timeline: {
        targets: gsap.TweenTarget;
        vars: {
            [key: string]: gsap.TweenVars | gsap.TweenVars[];
        };
        config?: GSAPTimelineVars;
        position?: gsap.Position;
        matchMedia?: string;
    }[];
    deps?: DependencyList;
}
type ReturnVars = [ref: MutableRefObject<null>, tl: GSAPTimeline];
export declare const useAnim: ({ config, timeline, deps }: AnimVars) => ReturnVars;
export {};
