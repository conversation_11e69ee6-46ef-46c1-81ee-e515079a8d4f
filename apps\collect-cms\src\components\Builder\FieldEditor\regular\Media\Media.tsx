import { Button, Icon, Image, Input, useIsomorphicLayoutEffect } from '@collective/core'
import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import dayjs from 'dayjs'
import { usePathname } from 'next/navigation'
import { useState, useMemo } from 'react'
import type { FieldProps } from '../../FieldEditor'
import styles from './media.module.scss'

const formatDate = (date: string) => dayjs(date).format('D/M/YYYY')
const formatExt = (ext: string) => ext.replace('.', '')

type MediaAttType = {
	ext?: string
	size?: string
	width?: number
	height?: number
	publishedAt?: string
	extension?: string
	name?: string
	alternativeText?: string
	caption?: string
	url?: string
}

type MediaFixedInfoType = {
	size?: string
	dimensions?: string
	date?: string
	extension?: string
}

type MediaEditableInfoType = {
	fileName?: string
	altText?: string
	caption?: string
}

type MediaToolType = {
	name: string
	icon: string
	action: string
	visible?: boolean
}

export interface MediaProps<T> extends FieldProps<T> {
	value?: T
	field?: string
	multiple?: string
	onChange: (props: { field: string; value: string }) => void
}

export const Media = <T,>(props: MediaProps<T>) => {
	const { value, onChange, multiple } = props ?? {}
	const pathname = usePathname()
	const [isEdit, setisEdit] = useState(false)
	const [fixedInfo, setFixedInfo] = useState<MediaFixedInfoType>({
		size: '',
		dimensions: '',
		date: '',
		extension: '',
	})
	const [editableInfo, setEditableInfo] = useState<MediaEditableInfoType>({
		fileName: '',
		altText: '',
		caption: '',
	})
	const [propsValue, setPropsValue] = useState<MediaAttType | MediaAttType[]>(value as MediaAttType)
	const [currentMedia, setCurrentMedia] = useState<MediaAttType>(
		Array.isArray(propsValue)
			? propsValue[0] || ({} as MediaAttType)
			: propsValue || ({} as MediaAttType)
	)
	const [currentMediaIdx, setCurrentMediaIdx] = useState(0)

	const handleNextMedia = () => {
		if (Array.isArray(propsValue) && propsValue.length > 0) {
			setCurrentMediaIdx((prevIdx) => (prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0))
		}
	}

	const handlePrevMedia = () => {
		if (Array.isArray(propsValue) && propsValue.length > 0) {
			setCurrentMediaIdx((prevIdx) => (prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1))
		}
	}

	useIsomorphicLayoutEffect(() => {
		if (Array.isArray(propsValue)) {
			setCurrentMedia(propsValue[currentMediaIdx] || ({} as MediaAttType))
		} else {
			setCurrentMedia(propsValue as MediaAttType)
		}
	}, [currentMediaIdx, propsValue])

	useIsomorphicLayoutEffect(() => {
		console.log(currentMedia)
		if (isEdit && currentMedia) {
			handleShowDetail()
		}
	}, [currentMedia])

	const mediaToolbar: MediaToolType[] = [
		{
			name: 'Add',
			icon: 'add',
			action: 'add',
			visible: !multiple,
		},
		{
			name: 'Replace',
			icon: 'replace',
			action: 'replace',
		},
		{
			name: 'Duplicate',
			icon: 'duplicate',
			action: 'duplicate',
			visible: !multiple,
		},
		{
			name: 'Remove',
			icon: 'remove',
			action: 'remove',
		},
		{
			name: 'Download',
			icon: 'download',
			action: 'download',
			visible: !isEdit,
		},
	]
	const filteredMediaToolbar = mediaToolbar.filter((tool) => !tool.visible)

	const handleShowDetail = () => {
		const { size, width, height, publishedAt, ext, name, alternativeText, caption } = currentMedia
		// console.log(currentMedia, name)
		setisEdit(true)
		setFixedInfo({
			size: `${size}KB`,
			dimensions: `${width}X${height}`,
			date: formatDate(publishedAt as string),
			extension: formatExt(ext || ''),
		})
		setEditableInfo({
			fileName: name?.split('.').slice(0, -1).join('.'),
			altText: alternativeText,
			caption: caption,
		})
	}

	const handleBack = () => {
		setisEdit(false)
	}

	const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target
		setEditableInfo((prev) => ({ ...prev, [name]: value }))
	}

	const handleAction = (key: string) => {
		switch (key) {
			case 'add':
				handleAdd()
				break
			case 'replace':
				handleReplace()
				break
			case 'duplicate':
				handleDuplicate()
				break
			case 'remove':
				handleRemove()
				break
			case 'download':
				handleDownload()
				break
			default:
				break
		}
	}

	// Hàm tiện ích để tạo input file và xử lý việc chọn file
	const createFileInput = (callback: (file: File) => void) => {
		const input = document.createElement('input')
		input.type = 'file'
		input.accept = 'image/*'
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement
			if (target.files && target.files.length > 0) {
				const file = target.files[0]
				if (file) {
					// Kiểm tra kích thước file (20MB = 20 * 1024 * 1024 bytes)
					const maxSize = 20 * 1024 * 1024 // 20MB in bytes
					if (file.size > maxSize) {
						console.log('Exceeds the allowed media size limit of 20MB!')
						return
					}
					callback(file)
				}
			}
		}
		input.click()
	}

	// Hàm xử lý file đã chọn và chuyển đổi thành MediaAttType
	const processFile = (file: File): Promise<MediaAttType> => {
		return new Promise((resolve) => {
			const reader = new FileReader()
			reader.onload = (e) => {
				const img = document.createElement('img') as HTMLImageElement
				img.onload = () => {
					const now = new Date().toISOString()
					const ext = '.' + file.name.split('.').pop()

					resolve({
						name: file.name,
						ext: ext,
						size: (file.size / 1024).toFixed(2), // Convert to KB
						width: img.width,
						height: img.height,
						publishedAt: now,
						url: e.target?.result as string,
						alternativeText: '',
						caption: '',
					})
				}
				img.src = e.target?.result as string
			}
			reader.readAsDataURL(file)
		})
	}

	const handleAdd = () => {
		createFileInput(async (file) => {
			const newMedia = await processFile(file)
			console.log(newMedia)
			// Xử lý trường hợp multiple
			if (Array.isArray(propsValue)) {
				const newPropsValue = [...propsValue, newMedia]
				// Cập nhật currentMedia trước khi cập nhật propsValue và currentMediaIdx
				setCurrentMedia(newMedia)
				setPropsValue(newPropsValue)
				setCurrentMediaIdx(newPropsValue.length - 1)
				// Gọi onChange để cập nhật giá trị lên component cha
				onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
			} else {
				// Trường hợp không phải multiple, thêm mới = thay thế
				setCurrentMedia(newMedia)
				setPropsValue(newMedia)
				onChange?.({ field: props.field || '', value: JSON.stringify(newMedia) })
			}
		})
	}

	const handleReplace = () => {
		createFileInput(async (file) => {
			const newMedia = await processFile(file)

			if (Array.isArray(propsValue)) {
				// Thay thế media hiện tại trong mảng
				const newPropsValue = [...propsValue]
				newPropsValue[currentMediaIdx] = newMedia
				// Cập nhật currentMedia trước
				setCurrentMedia(newMedia)
				setPropsValue(newPropsValue)
				onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
			} else {
				// Thay thế media đơn
				setCurrentMedia(newMedia)
				setPropsValue(newMedia)
				onChange?.({ field: props.field || '', value: JSON.stringify(newMedia) })
			}
		})
	}

	const handleDuplicate = () => {
		if (!currentMedia) return

		// Tạo bản sao của media hiện tại
		const duplicatedMedia = { ...currentMedia, publishedAt: new Date().toISOString() }

		if (Array.isArray(propsValue)) {
			// Thêm bản sao vào mảng
			const newPropsValue = [...propsValue, duplicatedMedia]
			// Cập nhật currentMedia trước
			setCurrentMedia(duplicatedMedia)
			setPropsValue(newPropsValue)
			setCurrentMediaIdx(newPropsValue.length - 1)
			onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
		} else {
			// Chuyển từ đơn sang mảng
			const newPropsValue = [propsValue as MediaAttType, duplicatedMedia]
			// Cập nhật currentMedia trước
			setCurrentMedia(duplicatedMedia)
			setPropsValue(newPropsValue as unknown as MediaAttType)
			setCurrentMediaIdx(1)
			onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
		}
	}

	const handleRemove = () => {
		if (!currentMedia) return

		if (Array.isArray(propsValue)) {
			// Xóa media hiện tại khỏi mảng
			const newPropsValue = propsValue.filter((_, idx) => idx !== currentMediaIdx)

			if (newPropsValue.length === 0) {
				// Nếu không còn media nào
				setCurrentMedia(null as unknown as MediaAttType)
				setPropsValue(null as unknown as MediaAttType)
				onChange?.({ field: props.field || '', value: '' })
			} else {
				// Cập nhật lại index và media hiện tại
				const newIdx =
					currentMediaIdx >= newPropsValue.length ? newPropsValue.length - 1 : currentMediaIdx
				// Cập nhật currentMedia trước
				setCurrentMedia(newPropsValue[newIdx] || ({} as MediaAttType))
				setPropsValue(newPropsValue as unknown as MediaAttType)
				setCurrentMediaIdx(newIdx)
				onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
			}
		} else {
			// Xóa media đơn
			setCurrentMedia(null as unknown as MediaAttType)
			setPropsValue(null as unknown as MediaAttType)
			onChange?.({ field: props.field || '', value: '' })
		}
	}

	const handleDownload = () => {
		if (!currentMedia || !currentMedia.url) return

		// Tạo link tải xuống
		const link = document.createElement('a')

		// Xử lý URL dựa trên loại (data URL hoặc URL thông thường)
		const url = currentMedia.url.startsWith('data:')
			? currentMedia.url
			: `${process.env.NEXT_PUBLIC_STRAPI_HOST}${currentMedia.url}?original=true&download=true`

		link.href = url
		link.download = currentMedia.name || 'download'
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
	}

	const handleSaveMediaInfo = () => {
		if (!currentMedia) return

		// Cập nhật thông tin từ editableInfo vào currentMedia
		const updatedMedia: MediaAttType = {
			...currentMedia,
			name: editableInfo.fileName
				? `${editableInfo.fileName}${currentMedia.ext || ''}`
				: currentMedia.name,
			alternativeText: editableInfo.altText || currentMedia.alternativeText,
			caption: editableInfo.caption || currentMedia.caption,
		}

		// Cập nhật vào propsValue
		if (Array.isArray(propsValue)) {
			const newPropsValue = [...propsValue]
			newPropsValue[currentMediaIdx] = updatedMedia
			// Cập nhật currentMedia trước
			setCurrentMedia(updatedMedia)
			setPropsValue(newPropsValue as unknown as MediaAttType)
			onChange?.({ field: props.field || '', value: JSON.stringify(newPropsValue) })
		} else {
			// Cập nhật currentMedia trước
			setCurrentMedia(updatedMedia)
			setPropsValue(updatedMedia)
			onChange?.({ field: props.field || '', value: JSON.stringify(updatedMedia) })
		}

		// Quay lại chế độ xem
		setisEdit(false)
	}

	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])

	return (
		<div className={styles.wrapper}>
			<div
				className={styles.controller}
				style={
					{
						'--controller-cols': isBuilderMode ? 12 : 8,
					} as React.CSSProperties
				}
			>
				{multiple && !isEdit && (
					<div className={styles.nav}>
						<Button className={styles.nav__btn} onClick={handlePrevMedia}>
							<Icon type="cms" variant="chevron-left" />
						</Button>
						<span
							className={styles.nav__index}
						>{`${currentMediaIdx + 1}/${Array.isArray(propsValue) ? propsValue.length : 0}`}</span>
						<Button className={styles.nav__btn} onClick={handleNextMedia}>
							<Icon type="cms" variant="chevron-right" />
						</Button>
					</div>
				)}
				<div
					className={cn(
						styles.body,
						!isBuilderMode && isEdit ? (multiple ? styles.detailed__multi : styles.detailed) : ''
					)}
				>
					{currentMedia ? (
						<div
							className={styles.item}
							style={
								{
									'--height': isBuilderMode ? '160px' : '324px',
								} as React.CSSProperties
							}
						>
							<span className={styles.tag}>{formatExt(currentMedia?.ext || '')}</span>
							<div className={styles.thumbnail}>
								<Image media={currentMedia as unknown as IMediaProps} alt="" />
							</div>
							{!isEdit && (
								<div className={styles.mask} title="Edit this media">
									<Button onClick={() => handleShowDetail()}>
										<Icon type="cms" variant="edit" />
									</Button>
								</div>
							)}
						</div>
					) : (
						<div
							className={styles.empty}
							style={
								{
									'--height': isBuilderMode ? '160px' : '324px',
								} as React.CSSProperties
							}
							title="Browse file(s)"
						>
							<Icon type="cms" variant="image" />
							<p>
								Drop your file(s) here or{' '}
								<Button onClick={() => handleAction('add')}>browse</Button>
							</p>
							<small>Max. File Size: 20MB</small>
						</div>
					)}
					{isEdit && Array.isArray(propsValue) && (
						<div className={styles.items}>
							<button className={styles.items__nav} onClick={handlePrevMedia}>
								<Icon type="cms" variant="chevron-left" />
							</button>
							<div className={styles.items__list}>
								{propsValue.map((media, idx) => (
									<button
										key={idx}
										className={cn(
											styles.items__thumb,
											idx === currentMediaIdx ? styles.active : ''
										)}
										onClick={() => setCurrentMediaIdx(idx)}
									>
										<Image media={media as unknown as IMediaProps} alt="" />
									</button>
								))}
							</div>
							<button className={styles.items__nav} onClick={handleNextMedia}>
								<Icon type="cms" variant="chevron-right" />
							</button>
						</div>
					)}
				</div>

				{!isBuilderMode && (
					<div className={styles.toolbar}>
						<div className={styles.toolbar__list}>
							{filteredMediaToolbar.map((tool, idx) => (
								<button
									key={idx}
									className={styles.toolbar__button}
									onClick={() => handleAction(tool.action)}
									title={tool.name}
								>
									<Icon type="cms" variant={tool.icon} />
								</button>
							))}
						</div>

						<div className={styles.toolbar__fixed}>
							{!isEdit ? (
								<button
									className={cn(styles.toolbar__button, styles.text)}
									title="Edit"
									onClick={handleShowDetail}
								>
									Edit
								</button>
							) : (
								<button
									className={cn(styles.toolbar__button, styles.text)}
									title="Back"
									onClick={handleBack}
								>
									<Icon type="cms" variant="back" />
								</button>
							)}
						</div>
					</div>
				)}
			</div>
			{isEdit && (
				<div
					className={cn(styles.info, isBuilderMode ? styles.info__builder : '')}
					style={
						{
							'--info-cols': isBuilderMode ? 12 : 4,
						} as React.CSSProperties
					}
				>
					{isBuilderMode && (
						<>
							<div className={styles.info__title}>
								<button onClick={handleBack}>
									<Icon type="cms" variant="back" />
								</button>
								<h6 className="collect__heading collect__heading--h6">Media info</h6>
							</div>
							<div className={styles.info__media}>
								<div
									className={cn(
										styles.body,
										isEdit ? (multiple ? styles.detailed__multi : styles.detailed) : ''
									)}
								>
									{currentMedia ? (
										<div
											className={styles.item}
											style={
												{
													'--height': isBuilderMode ? '160px' : '324px',
												} as React.CSSProperties
											}
										>
											<span className={styles.tag}>{formatExt(currentMedia?.ext || '')}</span>
											<div className={styles.thumbnail}>
												<Image media={currentMedia as unknown as IMediaProps} alt="" />
											</div>
											{!isEdit && (
												<div className={styles.mask} title="Edit this media">
													<Button onClick={() => handleShowDetail()}>
														<Icon type="cms" variant="edit" />
													</Button>
												</div>
											)}
										</div>
									) : (
										<div
											className={styles.empty}
											style={
												{
													'--height': isBuilderMode ? '160px' : '324px',
												} as React.CSSProperties
											}
											title="Browse file(s)"
										>
											<Icon type="cms" variant="image" />
											<p>
												Drop your file(s) here or{' '}
												<Button onClick={() => handleAction('add')}>browse</Button>
											</p>
											<small>Max. File Size: 20MB</small>
										</div>
									)}
									{isEdit && Array.isArray(propsValue) && (
										<div className={styles.items}>
											<button className={styles.items__nav} onClick={handlePrevMedia}>
												<Icon type="cms" variant="chevron-left" />
											</button>
											<div className={styles.items__list}>
												{propsValue.map((media, idx) => (
													<button
														key={idx}
														className={cn(
															styles.items__thumb,
															idx === currentMediaIdx ? styles.active : ''
														)}
														onClick={() => setCurrentMediaIdx(idx)}
													>
														<Image media={media as unknown as IMediaProps} alt="" />
													</button>
												))}
											</div>
											<button className={styles.items__nav} onClick={handleNextMedia}>
												<Icon type="cms" variant="chevron-right" />
											</button>
										</div>
									)}
								</div>
								<div className={styles.toolbar}>
									<div className={styles.toolbar__list}>
										{filteredMediaToolbar.map((tool, idx) => (
											<button
												key={idx}
												className={styles.toolbar__button}
												onClick={() => handleAction(tool.action)}
												title={tool.name}
											>
												<Icon type="cms" variant={tool.icon} />
											</button>
										))}
									</div>
								</div>
							</div>
						</>
					)}
					<div className={styles.info__fixed}>
						{Object.entries(fixedInfo).map(([key, value]) => (
							<div key={key} className={styles.info__fixed_item}>
								<span className={styles.info__fixed_label}>{key}</span>
								<span className={styles.info__fixed_value}>{value}</span>
							</div>
						))}
					</div>
					<div className={styles.info__editable}>
						{Object.entries(editableInfo).map(([key, value]) => (
							<div key={key} className={styles.info__editable_item}>
								<label>{key}</label>
								<Input
									type="text"
									className="collect__input has__border"
									name={key}
									value={value || ''}
									placeholder={key}
									onChange={handleOnChange}
								/>
							</div>
						))}
					</div>
					<Button className="collect__button yellow" onClick={handleSaveMediaInfo}>
						Save
					</Button>
				</div>
			)}
		</div>
	)
}
