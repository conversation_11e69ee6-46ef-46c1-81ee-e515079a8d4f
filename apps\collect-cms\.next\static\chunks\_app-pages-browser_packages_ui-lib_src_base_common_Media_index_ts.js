/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_Media_index_ts"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/media.module.scss":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/media.module.scss ***!
  \*********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"media_wrapper__xyR_4\",\"media\":\"media_media__hEOOi\",\"inner\":\"media_inner__SNaNH\",\"download\":\"media_download__WLjJx\",\"full__scale\":\"media_full__scale__S6sMj\",\"auto__height\":\"media_auto__height__7lhKV\"};\n    if(true) {\n      // 1748271221835\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"d73d5736dc7f\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL01lZGlhL21lZGlhLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCO0FBQ2xCLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFtSixjQUFjLHNEQUFzRDtBQUNyUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL01lZGlhL21lZGlhLm1vZHVsZS5zY3NzPzNkMzciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcIndyYXBwZXJcIjpcIm1lZGlhX3dyYXBwZXJfX3h5Ul80XCIsXCJtZWRpYVwiOlwibWVkaWFfbWVkaWFfX2hFT09pXCIsXCJpbm5lclwiOlwibWVkaWFfaW5uZXJfX1NOYU5IXCIsXCJkb3dubG9hZFwiOlwibWVkaWFfZG93bmxvYWRfX1dMakp4XCIsXCJmdWxsX19zY2FsZVwiOlwibWVkaWFfZnVsbF9fc2NhbGVfX1M2c01qXCIsXCJhdXRvX19oZWlnaHRcIjpcIm1lZGlhX2F1dG9fX2hlaWdodF9fN2xoS1ZcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc0ODI3MTIyMTgzNVxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJEOi9DREEvcmVwb3MvYnJhbmQtY29tcGFzcy1mcm9udGVuZC10ZW1wbGF0ZS9hcHBzL2NvbGxlY3QtY21zL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICBcbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImQ3M2Q1NzM2ZGM3ZlwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/media.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/Media.tsx":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/Media.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Image!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Image!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_3__);\nvar _this = undefined;\n\n\n\n\n\nvar Media = function(param) {\n    var Media = param.Media, _param_IsFixedHeight = param.IsFixedHeight, IsFixedHeight = _param_IsFixedHeight === void 0 ? false : _param_IsFixedHeight;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            style: {\n                \"--count\": Media.map(function(item) {\n                    return item;\n                }).length\n            },\n            children: Media === null || Media === void 0 ? void 0 : Media.map(function(item, index) {\n                var media = item === null || item === void 0 ? void 0 : item.Media;\n                var mediaUrl = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\").concat(media === null || media === void 0 ? void 0 : media.url, \"?original=true&download=true\");\n                var DefaultBackground = item.DefaultBackground;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().media), item.IsFullScale && (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().full__scale), !IsFixedHeight && (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().auto__height)),\n                            style: DefaultBackground ? {\n                                backgroundColor: item.DefaultBackground\n                            } : {},\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().inner),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        media: item.Media,\n                                        placeholder: \"empty\",\n                                        alt: item.Media.caption || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 9\n                                }, _this),\n                                (item === null || item === void 0 ? void 0 : item.IsDownloadable) && (media === null || media === void 0 ? void 0 : media.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    target: \"_blank\",\n                                    href: mediaUrl,\n                                    download: true,\n                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().download),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                        variant: \"arrow-down\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 11\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 10\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 8\n                        }, _this),\n                        (media === null || media === void 0 ? void 0 : media.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"aidigi__paragraph--md\",\n                            children: media === null || media === void 0 ? void 0 : media.caption\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 27\n                        }, _this)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 7\n                }, _this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n            lineNumber: 20,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, _this);\n};\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/Media.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/index.ts":
/*!************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/index.ts ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.Media; }\n/* harmony export */ });\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/Media.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL01lZGlhL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL01lZGlhL2luZGV4LnRzPzI5MGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9NZWRpYSdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/index.ts\n"));

/***/ })

}]);