/* eslint-disable @typescript-eslint/naming-convention */

import type { ISEOProps } from '../seo'
import type { IComponentProps } from './'

export type IExampleBaseProps = {
	exampleName: string
	exampleLink: string
}

export type INavigationWrapProps = {
	id?: number
	Headline: string
	slug: string
	Pages: {
		Headline: string
		slug: string
	}[]
}

export type IPageProps = {
	Headline: string
	slug: string
	isLocked: boolean
	components: IComponentProps[]
	Category: ICategoryProps | null
	seo: ISEOProps
}

export type ICategoryProps = {
	Headline: string
	slug: string
	isLocked: boolean
	components: IComponentProps[]
	Pages: IPageProps | null
	seo: ISEOProps
}
