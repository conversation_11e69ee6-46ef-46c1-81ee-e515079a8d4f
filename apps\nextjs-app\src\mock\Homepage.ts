export const InitialData = {
	Components: [
		{
			id: 0,
			enable: true,
			__component: 'homepage.hero-scene',
			Media: {
				data: {
					attributes: {
						url: '/hero-scene.png',
						alternativeText: 'Hero Scene',
						width: 1331,
						height: 445,
					},
				},
			},
		},
		{
			id: 1,
			__component: 'common.header',
			Headline: 'We Create Content Digital Brand Book',
			Subhead: '*********',
			ActionButton: {
				ButtonText: 'Download PDF',
				ButtonLink: '#',
			},
		},
		{
			id: 3,
			__component: 'common.text-horizon',
			// Headline: 'Primary Logo',
			Paragraph:
				'Primary Logo lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
		},
		{
			id: 5,
			__component: 'homepage.search-bar',
			List: [
				[
					{
						label: 'Strategy',
						children: [
							{
								label: 'Vision, Mission, and Value',
								path: '/logotypes',
							},
							{
								label: 'Brand Tone-of-Voice',
								path: '/lock-ups',
							},
						],
					},
					{
						label: 'Logo',
						children: [
							{
								label: 'Logotypes',
								path: '/logotypes',
							},
							{
								label: 'Lock-ups',
								path: '/lock-ups',
							},
							{
								label: 'Usage',
								path: '/usage',
							},
							{
								label: 'Note',
								path: '/note',
							},
						],
					},
				],
				[
					{
						label: 'Typography',
						children: [
							{
								label: 'Brand Typeface',
								path: '/logotypes',
							},
							{
								label: 'Secondary Typeface',
								path: '/lock-ups',
							},
							{
								label: 'Typographic Hierarchy',
								path: '/usage',
							},
							{
								label: 'Expressive Type',
								path: '/note',
							},
							{
								label: 'Type In Use',
								path: '/note',
							},
							{
								label: 'Leading',
								path: '/note',
							},
							{
								label: 'Fallback Typeface',
								path: '/note',
							},
						],
					},
					{
						label: 'Color',
						children: [
							{
								label: 'Color System',
								path: '/logotypes',
							},
							{
								label: 'Color Spectrum',
								path: '/lock-ups',
							},
							{
								label: 'Background & Shapes Colour Combination',
								path: '/usage',
							},
						],
					},
				],
				[
					{
						label: 'Typography',
						children: [
							{
								label: 'Brand Typeface',
								path: '/logotypes',
							},
							{
								label: 'Secondary Typeface',
								path: '/lock-ups',
							},
							{
								label: 'Typographic Hierarchy',
								path: '/usage',
							},
							{
								label: 'Expressive Type',
								path: '/note',
							},
							{
								label: 'Type In Use',
								path: '/note',
							},
							{
								label: 'Leading',
								path: '/note',
							},
							{
								label: 'Fallback Typeface',
								path: '/note',
							},
						],
					},
					{
						label: 'Color',
						children: [
							{
								label: 'Color System',
								path: '/logotypes',
							},
							{
								label: 'Color Spectrum',
								path: '/lock-ups',
							},
							{
								label: 'Background & Shapes Colour Combination',
								path: '/usage',
							},
						],
					},
					{
						label: 'Others',
						children: [
							{
								label: 'Press',
								path: '/logotypes',
							},
							{
								label: 'Toolkit & API',
								path: '/lock-ups',
							},
							{
								label: 'External Link',
								path: '/usage',
							},
						],
					},
				],
				[
					{
						label: 'Press External Link',
						children: [
							{
								label: 'CDA Vnexpress 2024: Bài viết về partnership.',
								path: '/logotypes',
							},
							{
								label:
									'How CDA partnered with Coeus to help entrepreneurs do their jobs a lot better.',
								path: '/usage',
							},
							{
								label:
									'How CDA partnered with Coeus to help entrepreneurs do their jobs a lot better.',
								path: '/note',
							},
							{
								label: 'CDA Vnexpress 2024: Bài viết về partnership.',
								path: '/lock-ups',
							},
							{
								label:
									'How CDA partnered with Coeus to help entrepreneurs do their jobs a lot better.',
								path: '/usage',
							},
							{
								label:
									'How CDA partnered with Coeus to help entrepreneurs do their jobs a lot better.',
								path: '/note',
							},
						],
					},
				],
			],
		},
		{
			id: 6,
			__component: 'common.avigation-wrap',
			Variant: '1',
			Headline: 'Press External Link',
			List: [
				{
					Title: 'CDA Vnexpress 2024: Bài viết về partnership. 1',
					Link: '#',
				},
				{
					Title: 'How CDA partnered with Coeus to help 2',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 3',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 4',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 5',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 6',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 7',
					Link: '#',
				},
				{
					Title: 'entrepreneurs do their jobs a lot better. 8',
					Link: '#',
				},
			],
		},
	],
}
