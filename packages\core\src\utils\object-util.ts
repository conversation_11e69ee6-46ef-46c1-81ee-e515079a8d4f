// eslint-disable-next-line @typescript-eslint/naming-convention
type PlainObject = { [key: string]: unknown }

export class ObjectUtils {
	static elevateProperty<T extends PlainObject | PlainObject>(
		obj: T,
		propertyName: string
	): PlainObject | PlainObject[] {
		if (obj && typeof obj === 'object') {
			// If the current object has the specified property and it's an array, elevate the array
			if (
				Object.prototype.hasOwnProperty.call(obj, propertyName) &&
				Array.isArray(obj[propertyName])
			) {
				return obj[propertyName] as PlainObject[] // Replace the object with the array
			} else if (
				obj[propertyName] &&
				typeof obj[propertyName] === 'object' &&
				!Array.isArray(obj[propertyName])
			) {
				Object.assign(obj, obj[propertyName] as PlainObject)
				delete obj[propertyName]
			} else if (obj[propertyName] === null) {
				delete obj[propertyName]
			}

			// Recursively check for nested objects and arrays to elevate their properties
			for (const key in obj) {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					const value = obj[key]

					// If the value is an array, recursively elevate each element
					if (Array.isArray(value)) {
						;(obj as PlainObject)[key] = value.map((item) =>
							typeof item === 'object' && item !== null
								? this.elevateProperty(item as PlainObject, propertyName as string)
								: item
						)
					} else if (typeof value === 'object' && value !== null) {
						// If the value is an object, recursively apply the elevation to the object
						;(obj as PlainObject)[key] = this.elevateProperty(
							value as PlainObject,
							propertyName as string
						)
					}
				}
			}
		}

		return obj
	}
}
