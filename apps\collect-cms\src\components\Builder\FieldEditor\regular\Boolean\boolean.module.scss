@import '@/styles/config';

.toggle {
	display: inline-flex;
	height: px-to(41px, rem);
	cursor: pointer;
	border: px-to(1px, rem) solid color('grey', 15);
	border-radius: spacing(s2);
	padding: spacing(s1);
	background-color: color('white', 0);
	position: relative;

	input {
		display: none;
	}

	&__label {
		position: relative;
		width: 100%;
		padding: spacing(s2);
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		color: color('grey', 80);
		transition: 0.2s all var(--ease-transition-2);
		z-index: 1;

		@include fluid($font-size) {
			font-size: size('body', 'sm');
		}

		&.active {
			color: color('white', 0);
		}
	}

	&__thumb {
		position: absolute;
		top: spacing(s1);
		transform: translateX(calc(100% + spacing(s2)));
		height: calc(100% - spacing(s1) * 2);
		width: calc(50% - spacing(s1) * 2);
		background-color: color('red');
		border-radius: spacing(s1);
		transition: 0.2s all var(--ease-transition-2);
		z-index: 0;

		&.checked {
			transform: translateX(0);
			background-color: color('green');
		}
	}
}
