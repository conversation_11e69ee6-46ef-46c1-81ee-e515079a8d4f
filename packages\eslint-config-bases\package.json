{"name": "@collective/eslint-config-bases", "private": true, "type": "commonjs", "main": "./src/index.js", "files": ["src"], "exports": {".": "./src/index.js", "./patch/modern-module-resolution": "./src/patch/modern-module-resolution.js", "./helpers": "./src/helpers/index.js", "./mdx": "./src/bases/mdx.js", "./jest": "./src/bases/jest.js", "./playwright": {"require": "./src/bases/playwright.js"}, "./prettier-config": "./src/bases/prettier-config.js", "./prettier-plugin": "./src/bases/prettier-plugin.js", "./react": "./src/bases/react.js", "./react-query": "./src/bases/react-query.js", "./rtl": "./src/bases/rtl.js", "./regexp": "./src/bases/regexp.js", "./sonar": "./src/bases/sonar.js", "./storybook": "./src/bases/storybook.js", "./tailwind": "./src/bases/tailwind.js", "./typescript": "./src/bases/typescript.js"}, "scripts": {"clean": "rimraf ./dist ./coverage ./tsconfig.tsbuildinfo", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.mjs,.cjs,.mts,.cts --cache --cache-location ../../.cache/eslint/eslint-config-bases.eslintcache", "typecheck": "tsc --project tsconfig.json --noEmit", "fix-all-files": "eslint  --ext .ts,.tsx,.js,.jsx,.mjs,.cjs,.mts,.cts . --fix"}, "dependencies": {"@rushstack/eslint-patch": "^1.10.4", "@tanstack/eslint-plugin-query": "^5.59.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-playwright": "^1.7.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2 || 5.0.0-canary-7118f5dd7-20230705", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-sonarjs": "^1.0.4", "eslint-plugin-storybook": "^0.9.0", "eslint-plugin-testing-library": "^6.3.4"}, "peerDependencies": {"eslint": "^8.55.0", "eslint-plugin-mdx": "^2.2.0 || ^3.0.0", "eslint-plugin-tailwindcss": "^3.13.0", "prettier": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.1.6"}, "peerDependenciesMeta": {"eslint-plugin-mdx": {"optional": true}, "eslint-plugin-tailwindcss": {"optional": true}, "prettier": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}, "tailwindcss": {"optional": true}, "typescript": {"optional": true}}, "devDependencies": {"@testing-library/jest-dom": "6.6.2", "@testing-library/react": "16.0.1", "@types/node": "20.17.25", "@types/react": "18.3.19", "@types/react-dom": "18.3.1", "eslint": "8.57.1", "eslint-plugin-mdx": "3.1.5", "eslint-plugin-tailwindcss": "3.17.5", "react": "18.3.1", "react-dom": "18.3.1", "rimraf": "6.0.1", "tailwindcss": "3.4.14", "typescript": "5.8.2"}}