/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import { Checkbox, useIsomorphicLayoutEffect } from '@collective/core'
import cn from 'classnames'
import Link from 'next/link'
import type { ChangeEvent, ReactElement, ReactNode } from 'react'
import React, { useRef, useState } from 'react'
import styles from './datatable.module.scss'
import * as ACTION from './utils'
import { isMatchedAllRows } from './utils'

interface RowData<T extends Record<string, any>> {
	originalEvent: ChangeEvent<HTMLInputElement> | React.MouseEvent<HTMLTableRowElement, MouseEvent>
	type: string
	value: T[]
}

interface DataTableProps<T extends Record<string, any>> {
	selectionMode?: string
	tableStyle?: React.CSSProperties
	value?: T[]
	children?: ReactNode
	dataKey?: string
	onSelectionChange?: ({ originalEvent, type, value }: RowData<T>) => void
	onRowClick?: (e: React.MouseEvent<HTMLTableRowElement, MouseEvent>, index: number) => void
	onPaginator?: (paginator: ACTION.PaginatorClass) => void
	paginator?: ACTION.PaginatorClass
	rows?: number
	rowsPerPageOptions?: number[]
}

/**
 * Renders a DataTable component.
 *
 * @param value - The dataset to render in the table, optionally containing `rowLink` for row navigation.
 * @param tableStyle - Inline styles for the table element.
 * @param dataKey - Unique identifier key for each row in the dataset.
 * @param children - Column definitions for the table.
 * @param onSelectionChange - Callback triggered when row selection changes.
 * @param onRowClick - Callback triggered when a row is clicked.
 * @param selectionMode - Selection mode, either 'row' or 'checkbox'.
 * @param paginator - Paginator instance for managing pagination.
 * @param rows - Number of rows displayed per page.
 * @param rowsPerPageOptions - Options for rows per page selection.
 * @param onPaginator - Callback triggered when pagination state changes.
 * @returns The rendered DataTable component.
 */
export const DataTable = <T extends Record<string, any>>({
	value,
	tableStyle,
	dataKey = 'id',
	children,
	onSelectionChange,
	onRowClick,
	selectionMode,
}: DataTableProps<T>) => {
	const columns = React.Children.toArray(children) as ReactElement[]
	const [selectedRows, setSelectedRows] = useState<RowData<T>>()

	useIsomorphicLayoutEffect(() => {
		selectedRows && onSelectionChange?.(selectedRows)
	}, [selectedRows])

	return (
		<div className={styles.wrapper}>
			<table style={tableStyle}>
				<thead>
					<tr>
						{columns.map((col, index) => (
							<th style={col.props.headerStyle} key={index}>
								{col.props.selectionMode ? (
									<Checkbox
										checked={isMatchedAllRows(value ?? [], selectedRows?.value ?? [], dataKey)}
										alignCenter
										className={styles.check}
										label={col.props.header}
										onChange={(e) => {
											setSelectedRows({
												originalEvent: e,
												type: 'checkbox',
												value: ACTION.AllRowsSelection(value ?? [], selectedRows?.value ?? []),
											})
										}}
									/>
								) : (
									col.props.header
								)}
							</th>
						))}
					</tr>
				</thead>
				<tbody>
					{value?.map((row, rowIndex) => (
						<tr
							className={cn(
								selectedRows?.value.some((item) => item[dataKey] === row[dataKey]) &&
									'on__selection'
							)}
							key={rowIndex}
							onClick={(e) => {
								if (onRowClick) onRowClick?.(e, rowIndex)
								if (selectionMode === 'row')
									setSelectedRows({
										originalEvent: e,
										type: 'row',
										value: ACTION.RowSelection(selectedRows?.value ?? [], row, dataKey),
									})
							}}
							style={{ cursor: onRowClick || selectionMode === 'row' ? 'pointer' : '' }}
						>
							{columns.map((col, colIndex) => (
								<td key={colIndex}>
									{col.props.selectionMode ? (
										<Checkbox
											onClick={(e) => e.stopPropagation()}
											checked={
												selectedRows?.value.some((item) => item[dataKey] === row[dataKey]) ?? false
											}
											onChange={(e) => {
												setSelectedRows({
													originalEvent: e,
													type: 'checkbox',
													value: ACTION.RowSelection(selectedRows?.value ?? [], row, dataKey),
												})
											}}
											className={styles.check}
										/>
									) : col.props.body ? (
										col.props.body(row)
									) : row.rowLink ? (
										<Link href={row.rowLink} key={colIndex}>
											{row[col.props.field as keyof T] as string}
										</Link>
									) : (
										(row[col.props.field as keyof T] as string)
									)}
								</td>
							))}
						</tr>
					))}
				</tbody>
			</table>
		</div>
	)
}
