'use client'

import { Button, Icon } from '@collective/core'
import { useModal } from '@collective/ui-lib/contexts/ModalContext'
import cn from 'classnames'
import styles from './lockedpagealert.module.scss'

export const LockedPageAlert = () => {
	const { onOpenModal } = useModal()
	return (
		<div className={styles.locked__page}>
			<div className={styles.locked__wrapper}>
				<Icon className={styles.locked__icon} variant="padlock" />
				<h2 className={cn('aidigi__heading', styles.locked__title)}>You Need to Log In</h2>
				<p className={styles.locked__desc}>To access this content, please sign in first.</p>
				<ul className={styles.locked__option}>
					<li>
						Already have an account?{' '}
						<Button onClick={() => onOpenModal('signin')} className={styles.link}>
							Log in here.
						</Button>
					</li>
					{/* <li>New around here? Create an account and join us!</li> */}
				</ul>
			</div>
		</div>
	)
}
