/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockcontainer_wrapper__9pM15 {
  margin-bottom: var(--section-mg-btm);
}
.blockcontainer_wrapper__9pM15 .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .blockcontainer_wrapper__9pM15 .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockcontainer_wrapper__9pM15 .blockcontainer_content__CVQDR {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .blockcontainer_wrapper__9pM15 .blockcontainer_content__CVQDR {
    grid-column-start: var(--position);
    grid-column-end: span var(--total);
    grid-template-columns: repeat(var(--total), 1fr);
  }
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__FgkM7 {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
  grid-column: span var(--layout);
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ {
  color: #a8a8a8;
  display: flex;
  gap: 0.5rem;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ h4 {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__title__yzrl_ svg {
  --size: 1.125rem;
  margin-top: 0.125rem;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc i,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc em {
  font-style: italic;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc b,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc strong {
  font-weight: 700;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc a,
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockcontainer_wrapper__9pM15 .blockcontainer_block__content__pprpc del {
  text-decoration: line-through;
}
.blockcontainer_wrapper__9pM15 .block__green {
  background-color: rgba(105, 192, 105, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__green .blockcontainer_block__title__yzrl_ {
  color: #69c069;
}
.blockcontainer_wrapper__9pM15 .block__red {
  background-color: rgba(238, 29, 82, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__red .blockcontainer_block__title__yzrl_ {
  color: #ee1d52;
}
.blockcontainer_wrapper__9pM15 .block__yellow {
  background-color: rgba(195, 127, 0, 0.1);
}
.blockcontainer_wrapper__9pM15 .block__yellow .blockcontainer_block__title__yzrl_ {
  color: #c37f00;
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.searchbar_wrapper__lQzqp .searchbar_search__wrapper__zhJNv {
  display: grid;
  gap: 0.625rem;
}
.searchbar_wrapper__lQzqp .searchbar_search__bar__me_P5 input {
  border-radius: 6.25rem;
  border: 1px solid #dddde3;
  box-shadow: none;
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
}
.searchbar_wrapper__lQzqp h3 {
  font-weight: 400;
  color: #1d1d1f;
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.guidelinelink_wrapper__2aoiX {
  margin-bottom: var(--section-mg-btm);
}
.guidelinelink_wrapper__2aoiX .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .guidelinelink_wrapper__2aoiX .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.guidelinelink_wrapper__2aoiX .guidelinelink_content__dM8Jn {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .guidelinelink_wrapper__2aoiX .guidelinelink_content__dM8Jn {
    grid-column-start: var(--position);
    grid-column-end: span var(--total);
    grid-template-columns: repeat(var(--total), 1fr);
  }
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__087_f {
  display: grid;
  grid-column: span var(--width);
  color: #1d1d1f;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__087_f a {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  font-weight: 500;
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__icon__q2qEL {
  color: #6e6e73;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__label__k4pQn {
  position: relative;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__label__k4pQn::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.0625rem;
  width: 100%;
  background-color: #1d1d1f;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockhorizon_wrapper__Z8BFI {
  margin-bottom: var(--section-mg-btm);
}
.blockhorizon_wrapper__Z8BFI .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .blockhorizon_wrapper__Z8BFI .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockhorizon_wrapper__Z8BFI .unwrap__wrapper {
  gap: 3rem;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
  grid-column: span 4;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(1):nth-child(3n+1) {
  grid-column: span 12;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(2):nth-child(3n+1), .blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(1):nth-child(3n+2) {
  grid-column: span 6;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__title__N0rAr {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  font-weight: 500;
  color: #1d1d1f;
  line-height: 1.5;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM i,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM em {
  font-style: italic;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM b,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM strong {
  font-weight: 700;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM a,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM del {
  text-decoration: line-through;
}
.blockhorizon_wrapper__Z8BFI .unwrap {
  padding: 0;
  background-color: transparent;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockcontent_wrapper__8O_bU {
  margin-bottom: var(--section-mg-btm);
  margin-top: 1.5rem;
  position: relative;
}
.blockcontent_wrapper__8O_bU::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0.0625rem;
  background-color: #dddddd;
}
.blockcontent_wrapper__8O_bU .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  padding-top: 4rem;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_headline__cMJyZ {
  font-weight: 500;
  color: #1d1d1f;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_headline__cMJyZ {
    grid-column: span 6;
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_content__KH33h {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_content__KH33h {
    grid-column-start: var(--isHasHeadline);
    grid-column-end: span 6;
  }
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_content__grid__GajH2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_content__grid__GajH2 .blockcontent_block__8eue2 {
  grid-column: span 1;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__8eue2 {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__title__Ck2bf {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  font-weight: 500;
  color: #1d1d1f;
  line-height: 1.5;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ i,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ em {
  font-style: italic;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ b,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ strong {
  font-weight: 700;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ a,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ del {
  text-decoration: line-through;
}
.blockcontent_wrapper__8O_bU.nohead {
  margin-top: 0;
}
.blockcontent_wrapper__8O_bU.nohead::before {
  display: none;
}
.blockcontent_wrapper__8O_bU.nohead .aidigi__grid {
  padding-top: 0;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Color/color.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.color_wrapper__yuBaS {
  margin-bottom: var(--section-mg-btm);
}
.color_wrapper__yuBaS .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
.color_wrapper__yuBaS .color_row__afjAf {
  display: grid;
  gap: 1.25rem;
  grid-template-columns: repeat(2, 1fr);
}
@media (min-width: 75rem) {
  .color_wrapper__yuBaS .color_row__afjAf {
    grid-template-columns: repeat(var(--count), 1fr);
  }
}
.color_wrapper__yuBaS .color_bg__color__UyYaA {
  position: relative;
  height: 12.5rem;
  border-radius: 1.5625rem;
  cursor: pointer;
}
.color_wrapper__yuBaS .color_bg__color__UyYaA:hover .color_tag__OFAjQ {
  opacity: 1;
  visibility: visible;
}
.color_wrapper__yuBaS .color_description__pExio {
  width: 100%;
  margin: 0.625rem;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}
.color_wrapper__yuBaS .color_description__pExio caption {
  margin-bottom: 1rem;
}
.color_wrapper__yuBaS .color_description__pExio th {
  width: auto;
  text-transform: uppercase;
}
.color_wrapper__yuBaS .color_description__pExio * {
  text-align: left;
}
.color_wrapper__yuBaS .color_tag__OFAjQ {
  visibility: hidden;
  opacity: 0;
  -webkit-user-select: none;
          user-select: none;
  pointer-events: none;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  color: #fff;
  border-radius: 0.3125rem;
  padding: 0.3125rem 0.625rem;
  background-color: rgba(0, 0, 0, 0.25);
  transition: 0.2s var(--ease-transition-2);
  display: flex;
  gap: 0.3125rem;
  align-items: center;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Media/media.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.media_wrapper__xyR_4 {
  margin-bottom: var(--section-mg-btm);
}
.media_wrapper__xyR_4 .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  grid-template-columns: repeat(calc(var(--count) / 3), 1fr);
}
@media (min-width: 75rem) {
  .media_wrapper__xyR_4 .aidigi__grid {
    grid-template-columns: repeat(calc(var(--count) / 2), 1fr);
  }
}
@media (min-width: 90rem) {
  .media_wrapper__xyR_4 .aidigi__grid {
    grid-template-columns: repeat(calc(var(--count)), 1fr);
  }
}
.media_wrapper__xyR_4 .media_media__hEOOi {
  background-color: #f6f6f6;
  height: auto;
  min-height: clamp(12.5rem, 29.4375rem - 4.234375rem * (var(--count) - 2), 29.4375rem);
  border-radius: 1.5625rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
.media_wrapper__xyR_4 .media_inner__SNaNH {
  position: relative;
  width: 100%;
  flex: 1 1 100%;
  display: flex;
  justify-content: center;
}
.media_wrapper__xyR_4 .media_download__WLjJx {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 3.5rem;
  height: 3.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-left-radius: 1.5625rem;
  border-bottom-right-radius: 1.5625rem;
  background-color: #f0f0f0;
}
.media_wrapper__xyR_4 .media_download__WLjJx svg {
  --size: 1.125rem;
}
.media_wrapper__xyR_4 img {
  position: absolute;
  width: auto;
  height: auto;
  max-height: 100%;
  object-fit: cover;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.media_wrapper__xyR_4 .media_full__scale__S6sMj img {
  width: 100%;
  height: 100%;
  max-height: none;
}
.media_wrapper__xyR_4 .media_auto__height__7lhKV {
  min-height: unset;
}
.media_wrapper__xyR_4 .media_auto__height__7lhKV img {
  position: static;
  transform: none;
}
.media_wrapper__xyR_4 p {
  padding: 0.625rem;
  color: #646464;
  font-size: clamp(0.875rem, 1.25rem - 0.075rem * (var(--count) - 2), 1.25rem);
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Divider/divider.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.divider_wrapper__qemC4 .divider_divider__N49md {
  width: 100%;
}
.divider_wrapper__qemC4 .divider_divider__blank__KPxy4 {
  height: 3.125rem;
}
.divider_wrapper__qemC4 .divider_divider__line__sHQyg {
  border: 0.0625rem solid #dddddd;
  margin: 1.5rem 3rem 4rem;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.texthorizon_wrapper__OQFLj {
  margin-bottom: var(--section-mg-btm);
  margin-top: 1.5rem;
  position: relative;
}
.texthorizon_wrapper__OQFLj::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0.0625rem;
  background-color: #dddddd;
}
.texthorizon_wrapper__OQFLj .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  padding-top: 4rem;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_headline__QGU5y {
  font-weight: 500;
  color: #1d1d1f;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .texthorizon_headline__QGU5y {
    grid-column: span 6;
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB {
    grid-column: span 6;
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB .texthorizon_blockquote__RlgAz {
  color: #1d1d1f;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB .texthorizon_paragraph__ecVT8 {
  color: #6e6e73;
  font-size: 0.9375rem;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB i,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB em {
  font-style: italic;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB b,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB strong {
  font-weight: 700;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB a,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB del {
  text-decoration: line-through;
}
.texthorizon_wrapper__OQFLj.nohead {
  margin-top: 0;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj.nohead .texthorizon_content__qfSfB {
    grid-column-start: 7;
    grid-column-end: span 6;
  }
}
.texthorizon_wrapper__OQFLj.nohead::before {
  display: none;
}
.texthorizon_wrapper__OQFLj.nohead .aidigi__grid {
  padding-top: 0;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Header/header.module.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.header_wrapper__o2Cif {
  margin-bottom: var(--section-mg-btm);
}
.header_wrapper__o2Cif .aidigi__grid {
  display: flex;
  flex-flow: wrap;
  justify-content: space-between;
  gap: 1.25rem;
}
.header_wrapper__o2Cif .header_headline__T2YEt {
  display: grid;
  gap: 0.75rem;
}
@media (min-width: 90rem) {
  .header_wrapper__o2Cif .header_headline__T2YEt {
    flex-basis: calc(75% - 1.25rem);
  }
}
.header_wrapper__o2Cif .header_headline__T2YEt h1 {
  font-weight: 500;
}
.header_wrapper__o2Cif .header_headline__T2YEt h3 {
  font-weight: 400;
}
.header_wrapper__o2Cif .header_download__bfE6Q {
  text-align: right;
}
@media (min-width: 90rem) {
  .header_wrapper__o2Cif .header_download__bfE6Q {
    flex-basis: calc(25% - 1.25rem);
  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.navigationwrap_wrapper__CWlTa {
  margin-bottom: var(--section-mg-btm);
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi {
  display: grid;
  gap: 1.25rem;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_row__nav__N15Ox {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 75rem) {
  .navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_row__nav__N15Ox {
    grid-template-columns: repeat(12, 1fr);
  }
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W {
  background-color: #fafafa;
  padding: 2.5rem;
  border-radius: 1.5625rem;
  min-height: 15rem;
  display: grid;
  gap: 3.125rem;
  align-content: flex-start;
  grid-column: span 4;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(1):nth-child(3n+1) {
  grid-column: span 12;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(2):nth-child(3n+1), .navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(1):nth-child(3n+2) {
  grid-column: span 6;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W h2 {
  font-weight: 500;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj {
  display: grid;
  line-height: 180%;
  /* 36px */
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  color: #6e6e73;
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  text-decoration: none;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a {
  position: relative;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a:hover::after {
  width: 100%;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  height: 0.0625rem;
  width: 0;
  background-color: #6e6e73;
  transition: width 0.2s var(--ease-transition-2);
}
