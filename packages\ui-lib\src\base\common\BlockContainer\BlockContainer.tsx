import { Icon } from '@collective/core'
import cn from 'classnames'
import Markdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import styles from './blockcontainer.module.scss'

type BlockContainerProps = {
	cid?: string
	Align: string
	Size: string
	Blocks: {
		ColorVariant: string
		Icon?: string
		Headline?: string
		Paragraph: string
	}[]
}

type CommonDataProps = {
	isFirstSection?: boolean
}

type Props = BlockContainerProps & CommonDataProps

export const BlockContainer = ({ cid, Align, Size, Blocks, isFirstSection }: Props) => {
	return (
		<section id={cid} className={cn(styles.wrapper)}>
			<div className={cn('aidigi__grid')}>
				<div
					className={cn(styles.content)}
					style={
						{
							'--position': Size === 'Full' ? 0 : 7,
							'--total': Size === 'Full' ? 12 : 6,
						} as React.CSSProperties
					}
				>
					{Blocks.map((item, idx) => (
						<div
							key={idx}
							className={cn(styles.block, `block__${item.ColorVariant}`)}
							style={
								{
									'--layout':
										Size === 'Full'
											? Align === 'Horizontal'
												? 6
												: 12
											: Align === 'Horizontal'
												? 3
												: 6,
								} as React.CSSProperties
							}
						>
							{(item.Icon || item.Headline) && (
								<div className={styles.block__title}>
									{item.Icon && <Icon variant={item.Icon} />}
									{item.Headline && <h4 className={cn('aidigi__heading')}>{item.Headline}</h4>}
								</div>
							)}
							{item.Paragraph && (
								<div className={styles.block__content}>
									<Markdown rehypePlugins={[rehypeRaw]}>{item.Paragraph}</Markdown>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		</section>
	)
}
