{"name": "collect-cms", "version": "0.1.0", "sideEffects": false, "private": true, "scripts": {"dev": "next dev", "build-cloudflare": "next-on-pages", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql --cache --cache-location ../../.cache/eslint/nextjs-app.eslintcache", "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql . --fix", "clean": "rimraf ./.next ./.out ./coverage ./tsconfig.tsbuildinfo ./node_modules/.cache", "typecheck": "tsc --project ./tsconfig.json --noEmit"}, "browserslist": {"production": [">1%", "not ie 11", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "1.6.1", "@collective/core": "workspace:^", "@collective/i18n": "workspace:^", "@collective/integration-lib": "workspace:^", "@collective/tsconfig": "workspace:^", "@collective/ui-lib": "workspace:^", "@next/third-parties": "^14", "classnames": "^2.5.1", "dayjs": "^1.11.13", "framer-motion": "11.18.2", "gsap": "^3", "next": "^14", "object-hash": "^3", "react": "18.3.1", "react-colorful": "5.6.1", "react-dom": "18.3.1", "sass": "^1.86.0"}, "devDependencies": {"@ckeditor/ckeditor5-build-classic": "44.3.0", "@ckeditor/ckeditor5-react": "9.5.0", "@cloudflare/next-on-pages": "1.13.12", "@types/node": "20.17.25", "@types/object-hash": "^3", "@types/react": "18.3.19", "ckeditor5": "45.0.0", "cross-env": "7.0.3", "eslint": "8.57.1", "eslint-config-next": "^14", "npm-run-all2": "6.2.6", "prettier": "3.5.3", "rimraf": "6.0.1", "typescript": "5.8.3", "vercel": "^41.7.8"}}