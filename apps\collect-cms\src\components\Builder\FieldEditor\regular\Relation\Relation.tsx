'use client'
import { Icon, Input } from '@collective/core'
import cn from 'classnames'
import { useIsomorphicLayoutEffect } from 'framer-motion'
import { useContext, useMemo, useRef, useState } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import type { RelationDataByPage } from '@/mock/Builder'
import {
	getCmsAdminPageRelationList,
	getCmsAdminPageRelationListByQuery,
	type IRelationResultDataProps,
} from 'common/cms'
import type { FieldProps } from '../../FieldEditor'
import styles from './relation.module.scss'

export interface RelationProps<T> extends FieldProps<T> {
	value?: T
	relation: string
	onChange: (props: { field: string; value: (typeof RelationDataByPage)['results'] }) => void
}

export const Relation = <T,>(props: RelationProps<T>) => {
	const { onChange, name, relation } = props ?? {}
	const { data, configuration, contentType } = useContext(PageBuilderContext)
	const { data: uiConfig } = configuration ?? {}
	const { data: pageData } = data ?? {}
	const { data: typeData } = contentType ?? {}
	const [onSearch, setOnSearch] = useState(false)
	const [isLoading, setLoading] = useState(true)
	const [relationList, setRelationList] = useState<IRelationResultDataProps[]>([])
	const [relationValue, setRelationValue] = useState<IRelationResultDataProps[]>([])
	const holder = useRef<HTMLDivElement>(null)

	// Call relations though api by document id or relation target
	const documentId = pageData.documentId ?? ''
	const mainField: string = useMemo(() => {
		if (!uiConfig || !name) return ''
		const metadata =
			uiConfig.contentType.metadatas[name as keyof typeof uiConfig.contentType.metadatas]

		if (metadata && 'mainField' in metadata.list) {
			const field = metadata.list.mainField as string
			return field
		}

		return ''
	}, [uiConfig, name])

	const RelationAttr = useMemo(() => {
		if (!typeData) return null
		const attributes = typeData.schema.attributes
		return attributes[name as keyof typeof attributes]
	}, [typeData])

	const handleChange = (data: IRelationResultDataProps) => {
		let updatedData: typeof relationValue
		const isExisted = relationValue.some((item) => item.id === data.id)

		if (relation === 'manyToOne') {
			if (!isExisted) {
				updatedData = [data]
			} else {
				updatedData = []
			}
		} else {
			if (!isExisted) {
				updatedData = [...relationValue, data]
			} else {
				updatedData = relationValue.filter((item) => item.id !== data.id)
			}
		}

		setRelationValue(updatedData)
		onChange?.({ field: mainField, value: updatedData })
	}

	useIsomorphicLayoutEffect(() => {
		if (!name) return
		async function fetchData() {
			try {
				const [relationList, relationValue] = await Promise.all([
					getCmsAdminPageRelationListByQuery({
						uid: typeData.uid,
						documentId,
						fieldName: name as string,
					}),
					getCmsAdminPageRelationList({
						uid: typeData.uid,
						documentId,
						fieldName: name as string,
					}),
				])
				setRelationList(relationList.results)
				setRelationValue(relationValue.results)
				console.log('relationList', relationList)
				console.log('relationValue', relationValue)
			} catch (error) {
				console.error('Error fetching data:', error)
			} finally {
				setLoading(false)
			}
		}

		fetchData()
	}, [name])

	return (
		<div className={styles.wrapper} ref={holder}>
			{relationValue.length > 0 && (
				<div className={styles.list}>
					{relationValue?.map((relation, idx) => (
						<div
							data-id={relation.id}
							key={idx}
							className={styles.item}
							title={relation[mainField as keyof typeof relation] as string}
						>
							<p className="collect__body--xs">
								{mainField && relation[mainField as keyof typeof relation]}
							</p>
							<button className={styles.del} onClick={() => handleChange(relation)}>
								<Icon variant="x" type="cms" />
							</button>
						</div>
					))}
				</div>
			)}

			<Input
				className={styles.search}
				placeholder="Type to Add tags"
				onClick={() => setOnSearch(!onSearch)}
				onBlur={(e) =>
					e.relatedTarget !== holder.current &&
					!e.target.contains(e.relatedTarget) &&
					holder.current &&
					!holder.current.contains(e.relatedTarget) &&
					setOnSearch(false)
				}
			/>
			{relationList?.length > 0 && (
				<div className={cn(styles.dropdown, onSearch && styles.active)}>
					{relationList?.map((relation, idx) => (
						<button
							title={relation[mainField as keyof typeof relation] as string}
							data-id={relation.id}
							key={idx}
							className={styles.item}
							onClick={() => {
								handleChange(relation)
								setOnSearch(false)
							}}
						>
							<p className="collect__body--xs">
								{mainField && relation[mainField as keyof typeof relation]}
							</p>
						</button>
					))}
				</div>
			)}
		</div>
	)
}
