{
	"$schema": "https://json.schemastore.org/tsconfig",
	"extends": "@collective/tsconfig/base.json",
	"compilerOptions": {
		"baseUrl": "./src",
		"target": "esnext",
		"module": "esnext",
		"moduleResolution": "bundler",
		"verbatimModuleSyntax": true,
		"strict": true,
		"strictNullChecks": true,
		"incremental": true,
		"lib": ["dom", "dom.iterable", "esnext"],
		"jsx": "preserve",
		"paths": {
			"@/components/*": ["./components/*"],
			"@/app/*": ["./app/*"],
			"@/public/*": ["../public/*"],
			"@/mock/*": ["./mock/*"],
			"@/styles/*": ["./styles/*"],
			"@collective/ui-lib/*": ["../../../packages/ui-lib/src/*"],
			"@collective/ui-lib": ["../../../packages/ui-lib/src/index"],
			"@collective/integration-lib/*": ["../../../packages/integration-lib/src/*"],
			"@collective/integration-lib": ["../../../packages/integration-lib/src/index"],
			// "@collective/core/*": ["../../../packages/core/src/*"],
			// "@collective/core": ["../../../packages/core/src/index"]
			"@collective/core/*": ["@collective/core/dist/*"],
			"@collective/core": ["@collective/core/dist/index"]
		},
		"plugins": [
			{
				"name": "next"
			}
		]
	},
	"exclude": ["**/node_modules", "**/.*/*"],
	"include": [
		".eslintrc.*",
		"next-env.d.ts",
		"**/*.ts",
		"**/*.tsx",
		"**/*.mts",
		"**/*.js",
		"**/*.cjs",
		"**/*.mjs",
		"**/*.jsx",
		"**/*.json",
		".next/types/**/*.ts"
	]
}
