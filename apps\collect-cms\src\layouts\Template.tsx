'use client'

import { debounce, useIsomorphicLayoutEffect, useWindowDimensions } from '@collective/core'
// eslint-disable-next-line import/no-extraneous-dependencies
import { LayoutRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { usePathname } from 'next/navigation'
import React, { useContext, useRef } from 'react'

// Prevents instant page opening
const FrozenRouter = ({ children }: { children: React.ReactNode }) => {
	const context = useContext(LayoutRouterContext ?? {})
	const frozen = useRef(context).current

	if (!frozen) {
		return children
	}

	return <LayoutRouterContext.Provider value={frozen}>{children}</LayoutRouterContext.Provider>
}

export const Template = ({ children }: { children: React.ReactNode }) => {
	const delayScrollbar = debounce(getScrollbarWidth, 500)
	const { width, height } = useWindowDimensions()

	useIsomorphicLayoutEffect(() => {
		delayScrollbar()
		return () => {
			delayScrollbar.clear()
		}
	}, [width, height])

	return <FrozenRouter>{children}</FrozenRouter>
}

function getScrollbarWidth() {
	const htmlElm = document.documentElement

	// Creating invisible container
	const outer = document.createElement('div')
	outer.style.visibility = 'hidden'
	outer.style.overflow = 'scroll' // forcing scrollbar to appear
	document.body.appendChild(outer)

	// Creating inner element and placing it in the container
	const inner = document.createElement('div')
	outer.appendChild(inner)

	// Calculating difference between container's full width and the child width
	const scrollbarWidth = outer.offsetWidth - inner.offsetWidth
	const isScrollbar = htmlElm.scrollHeight - htmlElm.clientHeight

	// Removing temporary elements from the DOM
	outer?.parentNode?.removeChild(outer)

	htmlElm.style.setProperty('--scrollbar-gutter', `${isScrollbar > 0 ? scrollbarWidth : 0}px`)
}
