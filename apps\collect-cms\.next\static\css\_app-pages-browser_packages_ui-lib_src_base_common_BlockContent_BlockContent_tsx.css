/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockcontent_wrapper__8O_bU {
  margin-bottom: var(--section-mg-btm);
  margin-top: 1.5rem;
  position: relative;
}
.blockcontent_wrapper__8O_bU::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0.0625rem;
  background-color: #dddddd;
}
.blockcontent_wrapper__8O_bU .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  padding-top: 4rem;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_headline__cMJyZ {
  font-weight: 500;
  color: #1d1d1f;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_headline__cMJyZ {
    grid-column: span 6;
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_content__KH33h {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_content__KH33h {
    grid-column-start: var(--isHasHeadline);
    grid-column-end: span 6;
  }
}
@media (min-width: 90rem) {
  .blockcontent_wrapper__8O_bU .blockcontent_content__grid__GajH2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
.blockcontent_wrapper__8O_bU .blockcontent_content__grid__GajH2 .blockcontent_block__8eue2 {
  grid-column: span 1;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__8eue2 {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__title__Ck2bf {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  font-weight: 500;
  color: #1d1d1f;
  line-height: 1.5;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ i,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ em {
  font-style: italic;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ b,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ strong {
  font-weight: 700;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ a,
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockcontent_wrapper__8O_bU .blockcontent_block__content__CS7WJ del {
  text-decoration: line-through;
}
.blockcontent_wrapper__8O_bU.nohead {
  margin-top: 0;
}
.blockcontent_wrapper__8O_bU.nohead::before {
  display: none;
}
.blockcontent_wrapper__8O_bU.nohead .aidigi__grid {
  padding-top: 0;
}
