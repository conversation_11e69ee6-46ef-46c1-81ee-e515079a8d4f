{"name": "nextjs-app", "version": "0.1.0", "sideEffects": false, "private": true, "scripts": {"dev": "next dev", "build-cloudflare": "next-on-pages", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql --cache --cache-location ../../.cache/eslint/nextjs-app.eslintcache", "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql . --fix", "clean": "rimraf ./.next ./.out ./coverage ./tsconfig.tsbuildinfo ./node_modules/.cache", "typecheck": "tsc --project ./tsconfig.json --noEmit"}, "browserslist": {"production": [">1%", "not ie 11", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@collective/core": "workspace:^", "@collective/i18n": "workspace:^", "@collective/integration-lib": "workspace:^", "@collective/tsconfig": "workspace:^", "@collective/ui-lib": "workspace:^", "@next/third-parties": "14.2.26", "classnames": "^2.5.1", "dayjs": "^1.11.13", "framer-motion": "11.18.2", "gsap": "^3.12.5", "nanoid": "5.1.5", "next": "14.2.26", "react": "18.3.1", "react-dom": "18.3.1", "sass": "^1.86.0"}, "devDependencies": {"@cloudflare/next-on-pages": "1.13.10", "@types/node": "20.17.25", "@types/react": "18.3.19", "cross-env": "7.0.3", "eslint": "8.57.1", "eslint-config-next": "14.2.25", "npm-run-all2": "6.2.6", "prettier": "3.5.3", "rimraf": "6.0.1", "typescript": "5.8.2", "vercel": "^41.4.1"}}