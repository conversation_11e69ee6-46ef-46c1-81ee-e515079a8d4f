import { checkIsLogin } from '@collective/core'
import {
	createMetadata,
	getCmsData,
	type ICategoryProps,
	// type IComponentProps,
	// type ISEOProps,
	// type INavigationProps,
} from '@collective/integration-lib'
import { Wrapper } from '@collective/ui-lib/src/base/Wrapper'
import cn from 'classnames'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import { LockedPageAlert } from '@/components/LockedPageAlert/LockedPageAlert'
import { QuickNav } from '@/components/QuickNav/QuickNav'
// import { InitialData as ColorData } from '@/mock/Color'
// import { InitialData as LogoData } from '@/mock/Logo'
// import { InitialData as IntroData } from '@/mock/Intro'
import styles from './page.module.scss'

// export const runtime = 'edge'

export async function generateMetadata({ params }: { params: { lng: string; category: string } }) {
	try {
		const { lng, category } = params
		const CmsData = await getCmsData<ICategoryProps>({
			path: `slugify/slugs/category/${category}`,
			deep: 2,
			locale: lng,
		})
		const { seo } = CmsData.data
		return createMetadata({ seo, defaultTitle: `AI Digital Branding ${category} Page` })
	} catch (error) {
		notFound()
	}
}

export default async function Page({
	params: { lng, category },
}: Readonly<{
	params: { lng: string; category: string }
}>) {
	try {
		const CmsData = await getCmsData<ICategoryProps, 'multiple'>({
			path: `categories`,
			deep: 4,
			locale: lng,
			filter: `filters[slug][$eq]=${category}`,
		})
		if (!CmsData?.data?.[0]) {
			notFound()
		}

		const { Headline, slug, components } = CmsData.data[0]
		const filteredComponents = components.filter((component) => component.enable === true)
		// Find valid first component index (not Header, Media, Hero...)
		const firstValidComponentIndex = filteredComponents.findIndex(
			(component) =>
				component.__component !== 'common.header' &&
				component.__component !== 'common.media' &&
				component.__component !== 'homepage.hero-scene'
		)

		if (CmsData?.data[0]?.isLocked) {
			const cookieStore = cookies()
			const token = cookieStore.get('token')?.value
			if (!token) {
				// notFound() // Trả về giao diện chưa login
				return (
					<div className={styles.wrapper}>
						<LockedPageAlert />
						<QuickNav
							currentCategory={{
								Headline,
								slug,
							}}
							lng={lng}
						/>
					</div>
				)
			}
			const isLogin = await checkIsLogin(token)
			if (!isLogin) {
				// notFound() // Trả về giao diện chưa login
				return (
					<div className={styles.wrapper}>
						<LockedPageAlert />
						<QuickNav
							currentCategory={{
								Headline,
								slug,
							}}
							lng={lng}
						/>
					</div>
				)
			}
		}

		return (
			<div className={cn(styles.wrapper, 'page__content')}>
				<div className="aidigi__grid">
					{filteredComponents.map((component, index) => (
						<Wrapper
							key={index}
							commonData={{ locales: lng, isFirstSection: index === firstValidComponentIndex }}
							data={component}
						/>
					))}
				</div>
				<QuickNav
					currentCategory={{
						Headline,
						slug,
					}}
					lng={lng}
				/>
			</div>
		)
	} catch (error) {
		notFound()
	}
}
