"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Component/Component.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Component/Component.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Component: function() { return /* binding */ Component; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionItem,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionItem,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionItem,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Accordion/Accordion.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _FieldEditor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../FieldEditor */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _component_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./component.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Component/component.module.scss\");\n/* harmony import */ var _component_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_component_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar isArray = function(value) {\n    return Array.isArray(value);\n};\nvar Component = function(props) {\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var components = context.components, globals = context.globals, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, contextLayerPos = context.layerPos, setLayerPos = context.setLayerPos;\n    var fieldSizes = globals.data.fieldSizes;\n    var value = props.value, onChange = props.onChange, name = props.name, component = props.component, repeatable = props.repeatable, layerPos = props.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(isArray(value) ? value !== null && value !== void 0 ? value : [] : value !== null && value !== void 0 ? value : Object), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    var cmpData = components.data.find(function(item) {\n        return item.uid === component;\n    });\n    // Filter for object-type attributes only\n    var filteredComponents = function(obj) {\n        return Object.entries(obj || {}).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), value = _param[1];\n            return typeof value === \"object\" && value !== null;\n        });\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    (0,_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        props.value !== propsValue && setPropsValue(props.value);\n    }, [\n        props.value\n    ]);\n    if (!cmpData) return null;\n    var handleAdd = function() {\n        if (repeatable && isArray(propsValue)) {\n            // Create empty entry for repeatable array\n            var newEntry = {};\n            var newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(propsValue).concat([\n                newEntry\n            ]);\n            setPropsValue(newValue);\n            onChange({\n                field: name,\n                value: newValue\n            });\n        } else {\n            // Create empty entry for single object\n            var newEntry1 = {};\n            setPropsValue(newEntry1);\n            onChange({\n                field: name,\n                value: newEntry1\n            });\n        }\n    };\n    var handleRemove = function(idx) {\n        console.log(idx);\n        var childCmp = childComponentData;\n        if (repeatable && isArray(propsValue)) {\n            var newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(propsValue);\n            newValue.splice(idx, 1);\n            console.log(\"delete target:\", propsValue[idx], childComponentData);\n            setPropsValue(newValue);\n            onChange({\n                field: name,\n                value: newValue\n            });\n            setChildComponentData(childCmp.filter(function(item) {\n                return item.value !== propsValue[idx];\n            }));\n        } else {\n            setPropsValue(\"\");\n            onChange({\n                field: name,\n                value: null\n            });\n            childCmp.pop();\n            setChildComponentData(childCmp);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(idx);\n        var newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(propsValue);\n        newValue.push(newValue[idx]);\n        setPropsValue(newValue);\n        onChange({\n            field: name,\n            value: newValue\n        });\n    };\n    if (repeatable && isArray(propsValue)) {\n        // Handle repeatable component with multiple entries\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper), (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().multiple), isBuilderMode ? (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().builder) : \"\"),\n            children: [\n                propsValue.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isBuilderMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                            children: propsValue.map(function(mValue, idx) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__item),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__drag),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                                variant: \"more\",\n                                                type: \"cms\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 14\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 13\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"accordion__title-content\",\n                                            children: Object.values(mValue).find(function(v) {\n                                                return typeof v === \"string\";\n                                            }) || \"New entry #\".concat(idx + 1)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 13\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__action),\n                                            children: [\n                                                repeatable && isArray(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    title: \"Duplicate this entry\",\n                                                    onClick: function() {\n                                                        return handleDuplicate(idx);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                                        variant: \"duplicate\",\n                                                        type: \"cms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 16\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 15\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().remove__button),\n                                                    title: \"Remove this entry\",\n                                                    onClick: function() {\n                                                        return handleRemove(idx);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                                        variant: \"remove\",\n                                                        type: \"cms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 15\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 14\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    title: \"Edit this entry\",\n                                                    onClick: function() {\n                                                        setLayerPos(props.layerPos);\n                                                        var newEntry = {\n                                                            name: Object.values(mValue).find(function(v) {\n                                                                return typeof v === \"string\";\n                                                            }) || \"New entry #\".concat(idx + 1),\n                                                            value: mValue,\n                                                            fields: filteredComponents(cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.attributes),\n                                                            onChange: function(props) {\n                                                                if (!name) return;\n                                                                propsValue[idx][props.field] = props.value;\n                                                                onChange({\n                                                                    field: name,\n                                                                    value: propsValue\n                                                                });\n                                                            }\n                                                        };\n                                                        // Kiểm tra xem entry đã tồn tại trong childComponentData chưa\n                                                        var entryExists = childComponentData.some(function(item) {\n                                                            return item.name === newEntry.name && item.value === newEntry.value;\n                                                        });\n                                                        var entrySameLevel = propsValue.includes(newEntry.value);\n                                                        if (layerPos !== contextLayerPos || entrySameLevel && childComponentData.length < 2) {\n                                                            setChildComponentData([\n                                                                newEntry\n                                                            ]);\n                                                        } else {\n                                                            if (!entryExists) {\n                                                                var newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(childComponentData);\n                                                                newValue.push(newEntry);\n                                                                setChildComponentData(newValue);\n                                                            }\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                                        variant: \"edit\",\n                                                        type: \"cms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 15\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 14\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, idx, true, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 12\n                                }, _this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 9\n                        }, _this)\n                    }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.Accordion, {\n                        children: propsValue.map(function(mValue, idx) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.AccordionItem, {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"accordion__title-content\",\n                                            children: Object.values(mValue).find(function(v) {\n                                                return typeof v === \"string\";\n                                            }) || \"New entry #\".concat(idx + 1)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 14\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().remove__button),\n                                            title: \"Remove this entry\",\n                                            onClick: function() {\n                                                return handleRemove(idx);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                                variant: \"remove\",\n                                                type: \"cms\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 14\n                                        }, void 0)\n                                    ]\n                                }, void 0, true),\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-down\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 18\n                                }, void 0),\n                                children: filteredComponents(cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.attributes).map(function(param) {\n                                    var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n                                    var _fieldSizes_val_type;\n                                    var val = value;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FieldEditor__WEBPACK_IMPORTED_MODULE_11__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_12__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_13__._)({}, val), {\n                                        layerPos: props.layerPos,\n                                        name: \"\".concat(key, \" \").concat(isArray(mValue[key]) ? \"(\".concat(mValue[key].length, \")\") : \"\"),\n                                        size: (_fieldSizes_val_type = fieldSizes[val.type]) === null || _fieldSizes_val_type === void 0 ? void 0 : _fieldSizes_val_type[\"default\"],\n                                        value: mValue[key]\n                                    }), key, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 14\n                                    }, _this);\n                                })\n                            }, idx, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, _this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 8\n                    }, _this)\n                }, void 0, false) : null,\n                propsValue.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().add__button),\n                    onClick: handleAdd,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                            type: \"cms\",\n                            variant: \"add\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 7\n                        }, _this),\n                        \" Add an entry\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().add__button), (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().no__entry)),\n                    onClick: handleAdd,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                            type: \"cms\",\n                            variant: \"add\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 7\n                        }, _this),\n                        \" No entry yet. Click to add one.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n            lineNumber: 90,\n            columnNumber: 4\n        }, _this);\n    } else {\n        // Handle non-repeatable component (single entry)\n        return propsValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper), isBuilderMode ? (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().builder) : \"\"),\n            children: isBuilderMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__item),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__drag),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                    variant: \"more\",\n                                    type: \"cms\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 10\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 9\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"accordion__title-content\",\n                                children: Object.values(propsValue).find(function(v) {\n                                    return typeof v === \"string\";\n                                }) || \"New Entry\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 9\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__action),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        title: \"Duplicate this entry\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                            variant: \"duplicate\",\n                                            type: \"cms\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 11\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 10\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().remove__button),\n                                        title: \"Remove this entry\",\n                                        onClick: function() {\n                                            return handleRemove(0);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                            variant: \"remove\",\n                                            type: \"cms\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 11\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 10\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        title: \"Edit this entry\",\n                                        onClick: function() {\n                                            setLayerPos(props.layerPos);\n                                            var newEntry = {\n                                                name: Object.values(propsValue).find(function(v) {\n                                                    return typeof v === \"string\";\n                                                }) || \"New Entry\",\n                                                value: propsValue || {},\n                                                fields: filteredComponents(cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.attributes),\n                                                onChange: function(props) {\n                                                    if (!name) return;\n                                                    propsValue[props.field] = props.value;\n                                                    onChange({\n                                                        field: name,\n                                                        value: propsValue\n                                                    });\n                                                }\n                                            };\n                                            // Kiểm tra xem entry đã tồn tại trong childComponentData chưa\n                                            var entryExists = childComponentData.some(function(item) {\n                                                return item.name === newEntry.name && item.value === newEntry.value;\n                                            });\n                                            if (layerPos !== contextLayerPos) {\n                                                setChildComponentData([\n                                                    newEntry\n                                                ]);\n                                            } else {\n                                                if (!entryExists) {\n                                                    var newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(childComponentData);\n                                                    newValue.push(newEntry);\n                                                    setChildComponentData(newValue);\n                                                }\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                                            variant: \"edit\",\n                                            type: \"cms\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 11\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 10\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 9\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 8\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 7\n                }, _this)\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: filteredComponents(cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.attributes).map(function(param) {\n                    var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n                    var _fieldSizes_val_type;\n                    var val = value;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FieldEditor__WEBPACK_IMPORTED_MODULE_11__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_12__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_13__._)({}, val), {\n                        layerPos: layerPos,\n                        name: key,\n                        size: (_fieldSizes_val_type = fieldSizes[val.type]) === null || _fieldSizes_val_type === void 0 ? void 0 : _fieldSizes_val_type[\"default\"],\n                        value: propsValue[key] || val[\"default\"]\n                    }), key, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, _this);\n                })\n            }, void 0, false)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n            lineNumber: 229,\n            columnNumber: 4\n        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().add__button), (_component_module_scss__WEBPACK_IMPORTED_MODULE_4___default().no__entry)),\n            onClick: handleAdd,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.Icon, {\n                    type: \"cms\",\n                    variant: \"add\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 5\n                }, _this),\n                \" No entry yet. Click to add one.\"\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Component\\\\Component.tsx\",\n            lineNumber: 312,\n            columnNumber: 4\n        }, _this);\n    }\n};\n_s(Component, \"0bfNOVg4ocnO8hDhkFbiFYB+F34=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Accordion_AccordionItem_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Component;\nvar _c;\n$RefreshReg$(_c, \"Component\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Component/Component.tsx\n"));

/***/ })

});