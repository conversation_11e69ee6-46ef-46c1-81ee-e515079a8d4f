export class ObjectUtils {
    static elevateProperty(obj, propertyName) {
        if (obj && typeof obj === 'object') {
            // If the current object has the specified property and it's an array, elevate the array
            if (Object.prototype.hasOwnProperty.call(obj, propertyName) &&
                Array.isArray(obj[propertyName])) {
                return obj[propertyName]; // Replace the object with the array
            }
            else if (obj[propertyName] &&
                typeof obj[propertyName] === 'object' &&
                !Array.isArray(obj[propertyName])) {
                Object.assign(obj, obj[propertyName]);
                delete obj[propertyName];
            }
            else if (obj[propertyName] === null) {
                delete obj[propertyName];
            }
            // Recursively check for nested objects and arrays to elevate their properties
            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const value = obj[key];
                    // If the value is an array, recursively elevate each element
                    if (Array.isArray(value)) {
                        ;
                        obj[key] = value.map((item) => typeof item === 'object' && item !== null
                            ? this.elevateProperty(item, propertyName)
                            : item);
                    }
                    else if (typeof value === 'object' && value !== null) {
                        // If the value is an object, recursively apply the elevation to the object
                        ;
                        obj[key] = this.elevateProperty(value, propertyName);
                    }
                }
            }
        }
        return obj;
    }
}
