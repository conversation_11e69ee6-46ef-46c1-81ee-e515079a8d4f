import cn from 'classnames'
import Link from 'next/link'
import * as React from 'react'
import styles from './button.module.scss'

const variants = {
	primary: styles['button--primary'],
	accent: styles['button--accent'],
}

const sizes = {
	sm: styles['button--sm'],
	md: styles['button--md'],
}

const shapes = {
	fill: styles['button--fill'],
	outline: styles['button--outline'],
}

type IconProps =
	| { startIcon: React.ReactElement; endIcon?: never }
	| { endIcon: React.ReactElement; startIcon?: never }
	| { endIcon?: undefined; startIcon?: undefined }

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
	/** Variant of button */
	variant?: keyof typeof variants
	/** Size of button */
	size?: keyof typeof sizes
	/** Shape of button */
	shape?: keyof typeof shapes
	isLoading?: boolean
	isDark?: boolean
	href?: string
	scroll?: boolean
} & IconProps

/** All type of button in CCF Project */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{
			type = 'button',
			className = '',
			variant = 'primary',
			size = 'md',
			shape = 'fill',
			startIcon,
			endIcon,
			isDark,
			scroll = true,
			href,
			...props
		},
		ref
	) => {
		if (href) {
			return (
				<Link
					scroll={scroll}
					className={cn(
						styles.button,
						variants[variant],
						isDark ? styles.isDark : '',
						sizes[size],
						shapes[shape],
						className
					)}
					href={href}
				>
					{startIcon}
					<span className={styles.content}>{props.children}</span>
					{endIcon}
				</Link>
			)
		}
		return (
			<button
				ref={ref}
				type={type}
				className={cn(
					styles.button,
					variants[variant],
					isDark ? styles.isDark : '',
					sizes[size],
					shapes[shape],
					className
				)}
				{...props}
			>
				{startIcon}
				<span className={styles.content}>{props.children}</span>
				{endIcon}
			</button>
		)
	}
)

Button.displayName = 'Button'
