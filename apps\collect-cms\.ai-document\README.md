# CollectCMS Documentation

Welcome to the CollectCMS documentation folder. This directory contains comprehensive documentation for the CollectCMS application, including architecture diagrams, flow charts, and code structure explanations.

## Folder Structure

The documentation is organized into the following folders:

- **[core/](./core/)** - Core documentation about the system architecture, application flow, and codebase structure
- **[features/](./features/)** - Specialized documentation about specific features like authentication, routing, and the page builder
- **[meta/](./meta/)** - Documentation about the documentation itself

## Quick Start

- For a complete overview, start with the [index.md](./index.md) file
- To understand how the documentation is organized, see [meta/documentation-structure.md](./meta/documentation-structure.md)
- For a high-level overview of the system, check [core/architecture-overview.md](./core/architecture-overview.md)

## Documentation Contents

### Core Documentation

- [Architecture Overview](./core/architecture-overview.md) - High-level system architecture
- [Application Flow](./core/application-flow.md) - Main user flows and processes
- [Component Structure](./core/component-structure.md) - Component architecture and hierarchy
- [Data Models and API Integration](./core/data-models-and-api.md) - Data models and Strapi integration
- [Codebase Structure](./core/codebase-structure.md) - Directory structure and code organization

### Feature Documentation

- [Routing Documentation](./features/routing-documentation.md) - Routing system and middleware
- [CMS Integration](./features/cms-integration.md) - Integration with Strapi backend
- [Navigation Structure](./features/navigation-structure.md) - Navigation system and sidebar
- [Authentication Flow](./features/authentication-flow.md) - Authentication system and token management
- [Page Builder System](./features/page-builder-system.md) - Visual page builder interface

### Meta Documentation

- [Documentation Structure](./meta/documentation-structure.md) - How the documentation is organized
- [README.md](./README.md) (this file)

## Updating Documentation

When making significant changes to the codebase, please update the relevant documentation files to ensure they remain accurate and useful. Focus on:

1. Updating diagrams to reflect architectural changes
2. Modifying flow descriptions when processes change
3. Adding new components or features to the appropriate documents
4. Ensuring API integration details remain current

## Viewing Diagrams

The documentation includes Mermaid diagrams that may not render correctly in all Markdown viewers. For the best experience, view these files in an environment that supports Mermaid diagrams, such as:

- GitHub's web interface
- VS Code with a Markdown preview extension that supports Mermaid
- A dedicated Markdown viewer with Mermaid support
