/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fuzzysort";
exports.ids = ["vendor-chunks/fuzzysort"];
exports.modules = {

/***/ "(ssr)/../../node_modules/fuzzysort/fuzzysort.js":
/*!*************************************************!*\
  !*** ../../node_modules/fuzzysort/fuzzysort.js ***!
  \*************************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// https://github.com/farzher/fuzzysort v3.0.2\r\n\r\n// UMD (Universal Module Definition) for fuzzysort\r\n;((root, UMD) => {\r\n  if(true) !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (UMD),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__))\r\n  else {}\r\n})(this, _ => {\r\n  'use strict'\r\n\r\n  var single = (search, target) => {\r\n    if(!search || !target) return NULL\r\n\r\n    var preparedSearch = getPreparedSearch(search)\r\n    if(!isPrepared(target)) target = getPrepared(target)\r\n\r\n    var searchBitflags = preparedSearch.bitflags\r\n    if((searchBitflags & target._bitflags) !== searchBitflags) return NULL\r\n\r\n    return algorithm(preparedSearch, target)\r\n  }\r\n\r\n  var go = (search, targets, options) => {\r\n    if(!search) return options?.all ? all(targets, options) : noResults\r\n\r\n    var preparedSearch = getPreparedSearch(search)\r\n    var searchBitflags = preparedSearch.bitflags\r\n    var containsSpace  = preparedSearch.containsSpace\r\n\r\n    var threshold = denormalizeScore( options?.threshold || 0 )\r\n    var limit     = options?.limit || INFINITY\r\n\r\n    var resultsLen = 0; var limitedCount = 0\r\n    var targetsLen = targets.length\r\n\r\n    function push_result(result) {\r\n      if(resultsLen < limit) { q.add(result); ++resultsLen }\r\n      else {\r\n        ++limitedCount\r\n        if(result._score > q.peek()._score) q.replaceTop(result)\r\n      }\r\n    }\r\n\r\n    // This code is copy/pasted 3 times for performance reasons [options.key, options.keys, no keys]\r\n\r\n    // options.key\r\n    if(options?.key) {\r\n      var key = options.key\r\n      for(var i = 0; i < targetsLen; ++i) { var obj = targets[i]\r\n        var target = getValue(obj, key)\r\n        if(!target) continue\r\n        if(!isPrepared(target)) target = getPrepared(target)\r\n\r\n        if((searchBitflags & target._bitflags) !== searchBitflags) continue\r\n        var result = algorithm(preparedSearch, target)\r\n        if(result === NULL) continue\r\n        if(result._score < threshold) continue\r\n\r\n        result.obj = obj\r\n        push_result(result)\r\n      }\r\n\r\n    // options.keys\r\n    } else if(options?.keys) {\r\n      var keys = options.keys\r\n      var keysLen = keys.length\r\n\r\n      outer: for(var i = 0; i < targetsLen; ++i) { var obj = targets[i]\r\n\r\n        { // early out based on bitflags\r\n          var keysBitflags = 0\r\n          for (var keyI = 0; keyI < keysLen; ++keyI) {\r\n            var key = keys[keyI]\r\n            var target = getValue(obj, key)\r\n            if(!target) { tmpTargets[keyI] = noTarget; continue }\r\n            if(!isPrepared(target)) target = getPrepared(target)\r\n            tmpTargets[keyI] = target\r\n\r\n            keysBitflags |= target._bitflags\r\n          }\r\n\r\n          if((searchBitflags & keysBitflags) !== searchBitflags) continue\r\n        }\r\n\r\n        if(containsSpace) for(let i=0; i<preparedSearch.spaceSearches.length; i++) keysSpacesBestScores[i] = NEGATIVE_INFINITY\r\n\r\n        for (var keyI = 0; keyI < keysLen; ++keyI) {\r\n          target = tmpTargets[keyI]\r\n          if(target === noTarget) { tmpResults[keyI] = noTarget; continue }\r\n\r\n          tmpResults[keyI] = algorithm(preparedSearch, target, /*allowSpaces=*/false, /*allowPartialMatch=*/containsSpace)\r\n          if(tmpResults[keyI] === NULL) { tmpResults[keyI] = noTarget; continue }\r\n\r\n          // todo: this seems weird and wrong. like what if our first match wasn't good. this should just replace it instead of averaging with it\r\n          // if our second match isn't good we ignore it instead of averaging with it\r\n          if(containsSpace) for(let i=0; i<preparedSearch.spaceSearches.length; i++) {\r\n            if(allowPartialMatchScores[i] > -1000) {\r\n              if(keysSpacesBestScores[i] > NEGATIVE_INFINITY) {\r\n                var tmp = (keysSpacesBestScores[i] + allowPartialMatchScores[i]) / 4/*bonus score for having multiple matches*/\r\n                if(tmp > keysSpacesBestScores[i]) keysSpacesBestScores[i] = tmp\r\n              }\r\n            }\r\n            if(allowPartialMatchScores[i] > keysSpacesBestScores[i]) keysSpacesBestScores[i] = allowPartialMatchScores[i]\r\n          }\r\n        }\r\n\r\n        if(containsSpace) {\r\n          for(let i=0; i<preparedSearch.spaceSearches.length; i++) { if(keysSpacesBestScores[i] === NEGATIVE_INFINITY) continue outer }\r\n        } else {\r\n          var hasAtLeast1Match = false\r\n          for(let i=0; i < keysLen; i++) { if(tmpResults[i]._score !== NEGATIVE_INFINITY) { hasAtLeast1Match = true; break } }\r\n          if(!hasAtLeast1Match) continue\r\n        }\r\n\r\n        var objResults = new KeysResult(keysLen)\r\n        for(let i=0; i < keysLen; i++) { objResults[i] = tmpResults[i] }\r\n\r\n        if(containsSpace) {\r\n          var score = 0\r\n          for(let i=0; i<preparedSearch.spaceSearches.length; i++) score += keysSpacesBestScores[i]\r\n        } else {\r\n          // todo could rewrite this scoring to be more similar to when there's spaces\r\n          // if we match multiple keys give us bonus points\r\n          var score = NEGATIVE_INFINITY\r\n          for(let i=0; i<keysLen; i++) {\r\n            var result = objResults[i]\r\n            if(result._score > -1000) {\r\n              if(score > NEGATIVE_INFINITY) {\r\n                var tmp = (score + result._score) / 4/*bonus score for having multiple matches*/\r\n                if(tmp > score) score = tmp\r\n              }\r\n            }\r\n            if(result._score > score) score = result._score\r\n          }\r\n        }\r\n\r\n        objResults.obj = obj\r\n        objResults._score = score\r\n        if(options?.scoreFn) {\r\n          score = options.scoreFn(objResults)\r\n          if(!score) continue\r\n          score = denormalizeScore(score)\r\n          objResults._score = score\r\n        }\r\n\r\n        if(score < threshold) continue\r\n        push_result(objResults)\r\n      }\r\n\r\n    // no keys\r\n    } else {\r\n      for(var i = 0; i < targetsLen; ++i) { var target = targets[i]\r\n        if(!target) continue\r\n        if(!isPrepared(target)) target = getPrepared(target)\r\n\r\n        if((searchBitflags & target._bitflags) !== searchBitflags) continue\r\n        var result = algorithm(preparedSearch, target)\r\n        if(result === NULL) continue\r\n        if(result._score < threshold) continue\r\n\r\n        push_result(result)\r\n      }\r\n    }\r\n\r\n    if(resultsLen === 0) return noResults\r\n    var results = new Array(resultsLen)\r\n    for(var i = resultsLen - 1; i >= 0; --i) results[i] = q.poll()\r\n    results.total = resultsLen + limitedCount\r\n    return results\r\n  }\r\n\r\n\r\n  // this is written as 1 function instead of 2 for minification. perf seems fine ...\r\n  // except when minified. the perf is very slow\r\n  var highlight = (result, open='<b>', close='</b>') => {\r\n    var callback = typeof open === 'function' ? open : undefined\r\n\r\n    var target      = result.target\r\n    var targetLen   = target.length\r\n    var indexes     = result.indexes\r\n    var highlighted = ''\r\n    var matchI      = 0\r\n    var indexesI    = 0\r\n    var opened      = false\r\n    var parts       = []\r\n\r\n    for(var i = 0; i < targetLen; ++i) { var char = target[i]\r\n      if(indexes[indexesI] === i) {\r\n        ++indexesI\r\n        if(!opened) { opened = true\r\n          if(callback) {\r\n            parts.push(highlighted); highlighted = ''\r\n          } else {\r\n            highlighted += open\r\n          }\r\n        }\r\n\r\n        if(indexesI === indexes.length) {\r\n          if(callback) {\r\n            highlighted += char\r\n            parts.push(callback(highlighted, matchI++)); highlighted = ''\r\n            parts.push(target.substr(i+1))\r\n          } else {\r\n            highlighted += char + close + target.substr(i+1)\r\n          }\r\n          break\r\n        }\r\n      } else {\r\n        if(opened) { opened = false\r\n          if(callback) {\r\n            parts.push(callback(highlighted, matchI++)); highlighted = ''\r\n          } else {\r\n            highlighted += close\r\n          }\r\n        }\r\n      }\r\n      highlighted += char\r\n    }\r\n\r\n    return callback ? parts : highlighted\r\n  }\r\n\r\n\r\n  var prepare = (target) => {\r\n    if(typeof target === 'number') target = ''+target\r\n    else if(typeof target !== 'string') target = ''\r\n    var info = prepareLowerInfo(target)\r\n    return new_result(target, {_targetLower:info._lower, _targetLowerCodes:info.lowerCodes, _bitflags:info.bitflags})\r\n  }\r\n\r\n  var cleanup = () => { preparedCache.clear(); preparedSearchCache.clear() }\r\n\r\n\r\n  // Below this point is only internal code\r\n  // Below this point is only internal code\r\n  // Below this point is only internal code\r\n  // Below this point is only internal code\r\n\r\n\r\n  class Result {\r\n    get ['indexes']() { return this._indexes.slice(0, this._indexes.len).sort((a,b)=>a-b) }\r\n    set ['indexes'](indexes) { return this._indexes = indexes }\r\n    ['highlight'](open, close) { return highlight(this, open, close) }\r\n    get ['score']() { return normalizeScore(this._score) }\r\n    set ['score'](score) { this._score = denormalizeScore(score) }\r\n  }\r\n\r\n  class KeysResult extends Array {\r\n    get ['score']() { return normalizeScore(this._score) }\r\n    set ['score'](score) { this._score = denormalizeScore(score) }\r\n  }\r\n\r\n  var new_result = (target, options) => {\r\n    const result = new Result()\r\n    result['target']             = target\r\n    result['obj']                = options.obj                   ?? NULL\r\n    result._score                = options._score                ?? NEGATIVE_INFINITY\r\n    result._indexes              = options._indexes              ?? []\r\n    result._targetLower          = options._targetLower          ?? ''\r\n    result._targetLowerCodes     = options._targetLowerCodes     ?? NULL\r\n    result._nextBeginningIndexes = options._nextBeginningIndexes ?? NULL\r\n    result._bitflags             = options._bitflags             ?? 0\r\n    return result\r\n  }\r\n\r\n\r\n  var normalizeScore = score => {\r\n    if(score === NEGATIVE_INFINITY) return 0\r\n    if(score > 1) return score\r\n    return Math.E ** ( ((-score + 1)**.04307 - 1) * -2)\r\n  }\r\n  var denormalizeScore = normalizedScore => {\r\n    if(normalizedScore === 0) return NEGATIVE_INFINITY\r\n    if(normalizedScore > 1) return normalizedScore\r\n    return 1 - Math.pow((Math.log(normalizedScore) / -2 + 1), 1 / 0.04307)\r\n  }\r\n\r\n\r\n  var prepareSearch = (search) => {\r\n    if(typeof search === 'number') search = ''+search\r\n    else if(typeof search !== 'string') search = ''\r\n    search = search.trim()\r\n    var info = prepareLowerInfo(search)\r\n\r\n    var spaceSearches = []\r\n    if(info.containsSpace) {\r\n      var searches = search.split(/\\s+/)\r\n      searches = [...new Set(searches)] // distinct\r\n      for(var i=0; i<searches.length; i++) {\r\n        if(searches[i] === '') continue\r\n        var _info = prepareLowerInfo(searches[i])\r\n        spaceSearches.push({lowerCodes:_info.lowerCodes, _lower:searches[i].toLowerCase(), containsSpace:false})\r\n      }\r\n    }\r\n\r\n    return {lowerCodes: info.lowerCodes, _lower: info._lower, containsSpace: info.containsSpace, bitflags: info.bitflags, spaceSearches: spaceSearches}\r\n  }\r\n\r\n\r\n\r\n  var getPrepared = (target) => {\r\n    if(target.length > 999) return prepare(target) // don't cache huge targets\r\n    var targetPrepared = preparedCache.get(target)\r\n    if(targetPrepared !== undefined) return targetPrepared\r\n    targetPrepared = prepare(target)\r\n    preparedCache.set(target, targetPrepared)\r\n    return targetPrepared\r\n  }\r\n  var getPreparedSearch = (search) => {\r\n    if(search.length > 999) return prepareSearch(search) // don't cache huge searches\r\n    var searchPrepared = preparedSearchCache.get(search)\r\n    if(searchPrepared !== undefined) return searchPrepared\r\n    searchPrepared = prepareSearch(search)\r\n    preparedSearchCache.set(search, searchPrepared)\r\n    return searchPrepared\r\n  }\r\n\r\n\r\n  var all = (targets, options) => {\r\n    var results = []; results.total = targets.length // this total can be wrong if some targets are skipped\r\n\r\n    var limit = options?.limit || INFINITY\r\n\r\n    if(options?.key) {\r\n      for(var i=0;i<targets.length;i++) { var obj = targets[i]\r\n        var target = getValue(obj, options.key)\r\n        if(target == NULL) continue\r\n        if(!isPrepared(target)) target = getPrepared(target)\r\n        var result = new_result(target.target, {_score: target._score, obj: obj})\r\n        results.push(result); if(results.length >= limit) return results\r\n      }\r\n    } else if(options?.keys) {\r\n      for(var i=0;i<targets.length;i++) { var obj = targets[i]\r\n        var objResults = new KeysResult(options.keys.length)\r\n        for (var keyI = options.keys.length - 1; keyI >= 0; --keyI) {\r\n          var target = getValue(obj, options.keys[keyI])\r\n          if(!target) { objResults[keyI] = noTarget; continue }\r\n          if(!isPrepared(target)) target = getPrepared(target)\r\n          target._score = NEGATIVE_INFINITY\r\n          target._indexes.len = 0\r\n          objResults[keyI] = target\r\n        }\r\n        objResults.obj = obj\r\n        objResults._score = NEGATIVE_INFINITY\r\n        results.push(objResults); if(results.length >= limit) return results\r\n      }\r\n    } else {\r\n      for(var i=0;i<targets.length;i++) { var target = targets[i]\r\n        if(target == NULL) continue\r\n        if(!isPrepared(target)) target = getPrepared(target)\r\n        target._score = NEGATIVE_INFINITY\r\n        target._indexes.len = 0\r\n        results.push(target); if(results.length >= limit) return results\r\n      }\r\n    }\r\n\r\n    return results\r\n  }\r\n\r\n\r\n  var algorithm = (preparedSearch, prepared, allowSpaces=false, allowPartialMatch=false) => {\r\n    if(allowSpaces===false && preparedSearch.containsSpace) return algorithmSpaces(preparedSearch, prepared, allowPartialMatch)\r\n\r\n    var searchLower      = preparedSearch._lower\r\n    var searchLowerCodes = preparedSearch.lowerCodes\r\n    var searchLowerCode  = searchLowerCodes[0]\r\n    var targetLowerCodes = prepared._targetLowerCodes\r\n    var searchLen        = searchLowerCodes.length\r\n    var targetLen        = targetLowerCodes.length\r\n    var searchI          = 0 // where we at\r\n    var targetI          = 0 // where you at\r\n    var matchesSimpleLen = 0\r\n\r\n    // very basic fuzzy match; to remove non-matching targets ASAP!\r\n    // walk through target. find sequential matches.\r\n    // if all chars aren't found then exit\r\n    for(;;) {\r\n      var isMatch = searchLowerCode === targetLowerCodes[targetI]\r\n      if(isMatch) {\r\n        matchesSimple[matchesSimpleLen++] = targetI\r\n        ++searchI; if(searchI === searchLen) break\r\n        searchLowerCode = searchLowerCodes[searchI]\r\n      }\r\n      ++targetI; if(targetI >= targetLen) return NULL // Failed to find searchI\r\n    }\r\n\r\n    var searchI = 0\r\n    var successStrict = false\r\n    var matchesStrictLen = 0\r\n\r\n    var nextBeginningIndexes = prepared._nextBeginningIndexes\r\n    if(nextBeginningIndexes === NULL) nextBeginningIndexes = prepared._nextBeginningIndexes = prepareNextBeginningIndexes(prepared.target)\r\n    targetI = matchesSimple[0]===0 ? 0 : nextBeginningIndexes[matchesSimple[0]-1]\r\n\r\n    // Our target string successfully matched all characters in sequence!\r\n    // Let's try a more advanced and strict test to improve the score\r\n    // only count it as a match if it's consecutive or a beginning character!\r\n    var backtrackCount = 0\r\n    if(targetI !== targetLen) for(;;) {\r\n      if(targetI >= targetLen) {\r\n        // We failed to find a good spot for this search char, go back to the previous search char and force it forward\r\n        if(searchI <= 0) break // We failed to push chars forward for a better match\r\n\r\n        ++backtrackCount; if(backtrackCount > 200) break // exponential backtracking is taking too long, just give up and return a bad match\r\n\r\n        --searchI\r\n        var lastMatch = matchesStrict[--matchesStrictLen]\r\n        targetI = nextBeginningIndexes[lastMatch]\r\n\r\n      } else {\r\n        var isMatch = searchLowerCodes[searchI] === targetLowerCodes[targetI]\r\n        if(isMatch) {\r\n          matchesStrict[matchesStrictLen++] = targetI\r\n          ++searchI; if(searchI === searchLen) { successStrict = true; break }\r\n          ++targetI\r\n        } else {\r\n          targetI = nextBeginningIndexes[targetI]\r\n        }\r\n      }\r\n    }\r\n\r\n    // check if it's a substring match\r\n    var substringIndex = searchLen <= 1 ? -1 : prepared._targetLower.indexOf(searchLower, matchesSimple[0]) // perf: this is slow\r\n    var isSubstring = !!~substringIndex\r\n    var isSubstringBeginning = !isSubstring ? false : substringIndex===0 || prepared._nextBeginningIndexes[substringIndex-1] === substringIndex\r\n\r\n    // if it's a substring match but not at a beginning index, let's try to find a substring starting at a beginning index for a better score\r\n    if(isSubstring && !isSubstringBeginning) {\r\n      for(var i=0; i<nextBeginningIndexes.length; i=nextBeginningIndexes[i]) {\r\n        if(i <= substringIndex) continue\r\n\r\n        for(var s=0; s<searchLen; s++) if(searchLowerCodes[s] !== prepared._targetLowerCodes[i+s]) break\r\n        if(s === searchLen) { substringIndex = i; isSubstringBeginning = true; break }\r\n      }\r\n    }\r\n\r\n    // tally up the score & keep track of matches for highlighting later\r\n    // if it's a simple match, we'll switch to a substring match if a substring exists\r\n    // if it's a strict match, we'll switch to a substring match only if that's a better score\r\n\r\n    var calculateScore = matches => {\r\n      var score = 0\r\n\r\n      var extraMatchGroupCount = 0\r\n      for(var i = 1; i < searchLen; ++i) {\r\n        if(matches[i] - matches[i-1] !== 1) {score -= matches[i]; ++extraMatchGroupCount}\r\n      }\r\n      var unmatchedDistance = matches[searchLen-1] - matches[0] - (searchLen-1)\r\n\r\n      score -= (12+unmatchedDistance) * extraMatchGroupCount // penality for more groups\r\n\r\n      if(matches[0] !== 0) score -= matches[0]*matches[0]*.2 // penality for not starting near the beginning\r\n\r\n      if(!successStrict) {\r\n        score *= 1000\r\n      } else {\r\n        // successStrict on a target with too many beginning indexes loses points for being a bad target\r\n        var uniqueBeginningIndexes = 1\r\n        for(var i = nextBeginningIndexes[0]; i < targetLen; i=nextBeginningIndexes[i]) ++uniqueBeginningIndexes\r\n\r\n        if(uniqueBeginningIndexes > 24) score *= (uniqueBeginningIndexes-24)*10 // quite arbitrary numbers here ...\r\n      }\r\n\r\n      score -= (targetLen - searchLen)/2 // penality for longer targets\r\n\r\n      if(isSubstring)          score /= 1+searchLen*searchLen*1 // bonus for being a full substring\r\n      if(isSubstringBeginning) score /= 1+searchLen*searchLen*1 // bonus for substring starting on a beginningIndex\r\n\r\n      score -= (targetLen - searchLen)/2 // penality for longer targets\r\n\r\n      return score\r\n    }\r\n\r\n    if(!successStrict) {\r\n      if(isSubstring) for(var i=0; i<searchLen; ++i) matchesSimple[i] = substringIndex+i // at this point it's safe to overwrite matchehsSimple with substr matches\r\n      var matchesBest = matchesSimple\r\n      var score = calculateScore(matchesBest)\r\n    } else {\r\n      if(isSubstringBeginning) {\r\n        for(var i=0; i<searchLen; ++i) matchesSimple[i] = substringIndex+i // at this point it's safe to overwrite matchehsSimple with substr matches\r\n        var matchesBest = matchesSimple\r\n        var score = calculateScore(matchesSimple)\r\n      } else {\r\n        var matchesBest = matchesStrict\r\n        var score = calculateScore(matchesStrict)\r\n      }\r\n    }\r\n\r\n    prepared._score = score\r\n\r\n    for(var i = 0; i < searchLen; ++i) prepared._indexes[i] = matchesBest[i]\r\n    prepared._indexes.len = searchLen\r\n\r\n    const result    = new Result()\r\n    result.target   = prepared.target\r\n    result._score   = prepared._score\r\n    result._indexes = prepared._indexes\r\n    return result\r\n  }\r\n  var algorithmSpaces = (preparedSearch, target, allowPartialMatch) => {\r\n    var seen_indexes = new Set()\r\n    var score = 0\r\n    var result = NULL\r\n\r\n    var first_seen_index_last_search = 0\r\n    var searches = preparedSearch.spaceSearches\r\n    var searchesLen = searches.length\r\n    var changeslen = 0\r\n\r\n    // Return _nextBeginningIndexes back to its normal state\r\n    var resetNextBeginningIndexes = () => {\r\n      for(let i=changeslen-1; i>=0; i--) target._nextBeginningIndexes[nextBeginningIndexesChanges[i*2 + 0]] = nextBeginningIndexesChanges[i*2 + 1]\r\n    }\r\n\r\n    var hasAtLeast1Match = false\r\n    for(var i=0; i<searchesLen; ++i) {\r\n      allowPartialMatchScores[i] = NEGATIVE_INFINITY\r\n      var search = searches[i]\r\n\r\n      result = algorithm(search, target)\r\n      if(allowPartialMatch) {\r\n        if(result === NULL) continue\r\n        hasAtLeast1Match = true\r\n      } else {\r\n        if(result === NULL) {resetNextBeginningIndexes(); return NULL}\r\n      }\r\n\r\n      // if not the last search, we need to mutate _nextBeginningIndexes for the next search\r\n      var isTheLastSearch = i === searchesLen - 1\r\n      if(!isTheLastSearch) {\r\n        var indexes = result._indexes\r\n\r\n        var indexesIsConsecutiveSubstring = true\r\n        for(let i=0; i<indexes.len-1; i++) {\r\n          if(indexes[i+1] - indexes[i] !== 1) {\r\n            indexesIsConsecutiveSubstring = false; break;\r\n          }\r\n        }\r\n\r\n        if(indexesIsConsecutiveSubstring) {\r\n          var newBeginningIndex = indexes[indexes.len-1] + 1\r\n          var toReplace = target._nextBeginningIndexes[newBeginningIndex-1]\r\n          for(let i=newBeginningIndex-1; i>=0; i--) {\r\n            if(toReplace !== target._nextBeginningIndexes[i]) break\r\n            target._nextBeginningIndexes[i] = newBeginningIndex\r\n            nextBeginningIndexesChanges[changeslen*2 + 0] = i\r\n            nextBeginningIndexesChanges[changeslen*2 + 1] = toReplace\r\n            changeslen++\r\n          }\r\n        }\r\n      }\r\n\r\n      score += result._score / searchesLen\r\n      allowPartialMatchScores[i] = result._score / searchesLen\r\n\r\n      // dock points based on order otherwise \"c man\" returns Manifest.cpp instead of CheatManager.h\r\n      if(result._indexes[0] < first_seen_index_last_search) {\r\n        score -= (first_seen_index_last_search - result._indexes[0]) * 2\r\n      }\r\n      first_seen_index_last_search = result._indexes[0]\r\n\r\n      for(var j=0; j<result._indexes.len; ++j) seen_indexes.add(result._indexes[j])\r\n    }\r\n\r\n    if(allowPartialMatch && !hasAtLeast1Match) return NULL\r\n\r\n    resetNextBeginningIndexes()\r\n\r\n    // allows a search with spaces that's an exact substring to score well\r\n    var allowSpacesResult = algorithm(preparedSearch, target, /*allowSpaces=*/true)\r\n    if(allowSpacesResult !== NULL && allowSpacesResult._score > score) {\r\n      if(allowPartialMatch) {\r\n        for(var i=0; i<searchesLen; ++i) {\r\n          allowPartialMatchScores[i] = allowSpacesResult._score / searchesLen\r\n        }\r\n      }\r\n      return allowSpacesResult\r\n    }\r\n\r\n    if(allowPartialMatch) result = target\r\n    result._score = score\r\n\r\n    var i = 0\r\n    for (let index of seen_indexes) result._indexes[i++] = index\r\n    result._indexes.len = i\r\n\r\n    return result\r\n  }\r\n\r\n  // we use this instead of just .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') because that screws with japanese characters\r\n  var remove_accents = (str) => str.replace(/\\p{Script=Latin}+/gu, match => match.normalize('NFD')).replace(/[\\u0300-\\u036f]/g, '')\r\n\r\n  var prepareLowerInfo = (str) => {\r\n    str = remove_accents(str)\r\n    var strLen = str.length\r\n    var lower = str.toLowerCase()\r\n    var lowerCodes = [] // new Array(strLen)    sparse array is too slow\r\n    var bitflags = 0\r\n    var containsSpace = false // space isn't stored in bitflags because of how searching with a space works\r\n\r\n    for(var i = 0; i < strLen; ++i) {\r\n      var lowerCode = lowerCodes[i] = lower.charCodeAt(i)\r\n\r\n      if(lowerCode === 32) {\r\n        containsSpace = true\r\n        continue // it's important that we don't set any bitflags for space\r\n      }\r\n\r\n      var bit = lowerCode>=97&&lowerCode<=122 ? lowerCode-97 // alphabet\r\n              : lowerCode>=48&&lowerCode<=57  ? 26           // numbers\r\n                                                             // 3 bits available\r\n              : lowerCode<=127                ? 30           // other ascii\r\n              :                                 31           // other utf8\r\n      bitflags |= 1<<bit\r\n    }\r\n\r\n    return {lowerCodes:lowerCodes, bitflags:bitflags, containsSpace:containsSpace, _lower:lower}\r\n  }\r\n  var prepareBeginningIndexes = (target) => {\r\n    var targetLen = target.length\r\n    var beginningIndexes = []; var beginningIndexesLen = 0\r\n    var wasUpper = false\r\n    var wasAlphanum = false\r\n    for(var i = 0; i < targetLen; ++i) {\r\n      var targetCode = target.charCodeAt(i)\r\n      var isUpper = targetCode>=65&&targetCode<=90\r\n      var isAlphanum = isUpper || targetCode>=97&&targetCode<=122 || targetCode>=48&&targetCode<=57\r\n      var isBeginning = isUpper && !wasUpper || !wasAlphanum || !isAlphanum\r\n      wasUpper = isUpper\r\n      wasAlphanum = isAlphanum\r\n      if(isBeginning) beginningIndexes[beginningIndexesLen++] = i\r\n    }\r\n    return beginningIndexes\r\n  }\r\n  var prepareNextBeginningIndexes = (target) => {\r\n    target = remove_accents(target)\r\n    var targetLen = target.length\r\n    var beginningIndexes = prepareBeginningIndexes(target)\r\n    var nextBeginningIndexes = [] // new Array(targetLen)     sparse array is too slow\r\n    var lastIsBeginning = beginningIndexes[0]\r\n    var lastIsBeginningI = 0\r\n    for(var i = 0; i < targetLen; ++i) {\r\n      if(lastIsBeginning > i) {\r\n        nextBeginningIndexes[i] = lastIsBeginning\r\n      } else {\r\n        lastIsBeginning = beginningIndexes[++lastIsBeginningI]\r\n        nextBeginningIndexes[i] = lastIsBeginning===undefined ? targetLen : lastIsBeginning\r\n      }\r\n    }\r\n    return nextBeginningIndexes\r\n  }\r\n\r\n  var preparedCache       = new Map()\r\n  var preparedSearchCache = new Map()\r\n\r\n  // the theory behind these being globals is to reduce garbage collection by not making new arrays\r\n  var matchesSimple = []; var matchesStrict = []\r\n  var nextBeginningIndexesChanges = [] // allows straw berry to match strawberry well, by modifying the end of a substring to be considered a beginning index for the rest of the search\r\n  var keysSpacesBestScores = []; var allowPartialMatchScores = []\r\n  var tmpTargets = []; var tmpResults = []\r\n\r\n  // prop = 'key'                  2.5ms optimized for this case, seems to be about as fast as direct obj[prop]\r\n  // prop = 'key1.key2'            10ms\r\n  // prop = ['key1', 'key2']       27ms\r\n  // prop = obj => obj.tags.join() ??ms\r\n  var getValue = (obj, prop) => {\r\n    var tmp = obj[prop]; if(tmp !== undefined) return tmp\r\n    if(typeof prop === 'function') return prop(obj) // this should run first. but that makes string props slower\r\n    var segs = prop\r\n    if(!Array.isArray(prop)) segs = prop.split('.')\r\n    var len = segs.length\r\n    var i = -1\r\n    while (obj && (++i < len)) obj = obj[segs[i]]\r\n    return obj\r\n  }\r\n\r\n  var isPrepared = (x) => { return typeof x === 'object' && typeof x._bitflags === 'number' }\r\n  var INFINITY = Infinity; var NEGATIVE_INFINITY = -INFINITY\r\n  var noResults = []; noResults.total = 0\r\n  var NULL = null\r\n\r\n  var noTarget = prepare('')\r\n\r\n  // Hacked version of https://github.com/lemire/FastPriorityQueue.js\r\n  var fastpriorityqueue=r=>{var e=[],o=0,a={},v=r=>{for(var a=0,v=e[a],c=1;c<o;){var s=c+1;a=c,s<o&&e[s]._score<e[c]._score&&(a=s),e[a-1>>1]=e[a],c=1+(a<<1)}for(var f=a-1>>1;a>0&&v._score<e[f]._score;f=(a=f)-1>>1)e[a]=e[f];e[a]=v};return a.add=(r=>{var a=o;e[o++]=r;for(var v=a-1>>1;a>0&&r._score<e[v]._score;v=(a=v)-1>>1)e[a]=e[v];e[a]=r}),a.poll=(r=>{if(0!==o){var a=e[0];return e[0]=e[--o],v(),a}}),a.peek=(r=>{if(0!==o)return e[0]}),a.replaceTop=(r=>{e[0]=r,v()}),a}\r\n  var q = fastpriorityqueue() // reuse this\r\n\r\n  // fuzzysort is written this way for minification. all names are mangeled unless quoted\r\n  return {'single':single, 'go':go, 'prepare':prepare, 'cleanup':cleanup}\r\n}) // UMD\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fuzzysort/fuzzysort.js\n");

/***/ })

};
;