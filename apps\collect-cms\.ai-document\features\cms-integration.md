# Collect CMS Integration with Strapi

This document explains how the Collect CMS application integrates with the Strapi backend, detailing the API communication, data structures, and authentication mechanisms.

## Table of Contents

1. [Overview](#overview)
2. [Environment Configuration](#environment-configuration)
3. [API Communication](#api-communication)
4. [Content Types](#content-types)
5. [Authentication](#authentication)
6. [Media Management](#media-management)
7. [Internationalization](#internationalization)
8. [Error Handling](#error-handling)

## Overview

Collect CMS is a headless CMS frontend that communicates with a Strapi backend. The integration allows for:

- Content creation, editing, and management
- User authentication and authorization
- Media library management
- Internationalization support
- Dynamic content type handling

The application uses a combination of REST API calls and GraphQL queries to interact with the Strapi backend.

## Environment Configuration

The integration relies on environment variables to configure the connection to the Strapi backend:

```
NEXT_PUBLIC_STRAPI_HOST=https://your-strapi-instance.com
NEXT_PUBLIC_STRAPI_API_KEY=your-strapi-api-key
NEXT_PUBLIC_STRAPI_POST_API_KEY=your-strapi-post-api-key
STRAPI_COLLECT_CMS_API_KEY=your-strapi-cms-api-key
```

These variables are used throughout the application to construct API URLs and authenticate requests.

## API Communication

The application communicates with Strapi through several utility functions:

### Fetching Data

```typescript
// common/cms/index.ts
export async function getCmsData<T, K extends "single" | "multiple" = "single">({
	path,
	deep = 2,
	locale,
	filter,
}: {
	path: string
	deep?: number
	locale?: string
	filter?: string
}): Promise<K extends "single" ? { data: T } : { data: T[] }> {
	const url = new URL(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`)

	// Add query parameters
	url.searchParams.append("populate", `deep,${deep}`)
	if (locale) url.searchParams.append("locale", locale)
	if (filter) url.searchParams.append(filter.split("=")[0], filter.split("=")[1])

	// Make the request
	const res = await fetch(url.toString(), {
		headers: {
			Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
		next: { revalidate: 60 },
	})

	if (!res.ok) {
		throw new Error(`Failed to fetch data: ${res.status}`)
	}

	return res.json()
}
```

### Posting Data

```typescript
// common/cms/index.ts
export async function postJsonFormData<T>({
	fullPath,
	body,
	authToken,
}: {
	fullPath: string
	body: string
	authToken?: string
}): Promise<T> {
	const headers: HeadersInit = {
		"Content-Type": "application/json",
	}

	if (authToken) {
		headers.Authorization = `Bearer ${authToken}`
	}

	const res = await fetch(fullPath, {
		method: "POST",
		headers,
		body,
	})

	if (!res.ok) {
		throw new Error(`Failed to post data: ${res.status}`)
	}

	return res.json()
}
```

### Updating Data

```typescript
// common/cms/index.ts
export async function updateCollectionTypeData({
	path,
	id,
	data,
	authToken,
}: {
	path: string
	id: number | string
	data: any
	authToken: string
}) {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}/${id}`

	const res = await fetch(url, {
		method: "PUT",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${authToken}`,
		},
		body: JSON.stringify({ data }),
	})

	if (!res.ok) {
		throw new Error(`Failed to update data: ${res.status}`)
	}

	return res.json()
}
```

## Content Types

The application works with two main types of content from Strapi:

### Single Types

Single types represent content that has only one instance, such as a home page or global settings:

```typescript
// Example of fetching a single type
const homePageData = await getCmsData<HomePageProps, 'single'>({
  path: 'home-page',
  deep: 4,
  locale: 'en',
})

// Result structure
{
  data: {
    id: 1,
    attributes: {
      title: 'Home Page',
      content: '...',
      components: [
        {
          __component: 'common.header',
          Headline: 'Welcome',
          // ...
        },
        // More components...
      ],
      // ...
    }
  }
}
```

### Collection Types

Collection types represent content that can have multiple instances, such as articles, products, or pages:

```typescript
// Example of fetching collection types
const pagesData = await getCmsData<PageProps, "multiple">({
	path: "pages",
	deep: 4,
	locale: "en",
	filter: "filters[category][slug][$eq]=blog",
})

// Result structure
{
	data: [
		{
			id: 1,
			attributes: {
				title: "First Page",
				slug: "first-page",
				components: [
					// Components...
				],
				// ...
			},
		},
		// More pages...
	]
}
```

### Component Structure

Components within content types follow a consistent structure:

```typescript
{
  __component: 'namespace.component-name',
  // Component-specific properties...
}
```

For example:

```typescript
{
  __component: 'common.header',
  Headline: 'Welcome to our site',
  Subhead: 'Learn more about our services',
  ActionButton: {
    ButtonText: 'Get Started',
    ButtonLink: '/services'
  }
}
```

## Authentication

The application authenticates with Strapi in two ways:

### API Key Authentication

For public content, the application uses API keys:

```typescript
headers: {
  Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
}
```

### JWT Authentication

For admin operations, the application uses JWT tokens obtained through the login process:

```typescript
// Login process
const response = await postJsonFormData<{
  data: {
    token: string
    user: { firstname: string; lastname: string | null; username: string | null }
  }
}>({
  fullPath: `${process.env.NEXT_PUBLIC_STRAPI_HOST}/admin/login`,
  body: JSON.stringify({ email, password }),
})

// Using the token for authenticated requests
headers: {
  Authorization: `Bearer ${authToken}`,
}
```

The JWT token is stored in:

- Cookies for server-side authentication
- LocalStorage/SessionStorage for client-side authentication

## Media Management

The application integrates with Strapi's media library for handling images and other media:

### Media Structure

Media in Strapi is represented as:

```typescript
{
  data: {
    id: 1,
    attributes: {
      url: '/uploads/image.jpg',
      alternativeText: 'Description',
      width: 1200,
      height: 800,
      formats: {
        thumbnail: { /* ... */ },
        small: { /* ... */ },
        medium: { /* ... */ },
        large: { /* ... */ },
      }
    }
  }
}
```

### Media Components

The application includes components for displaying and managing media:

```typescript
// Example of a media component
{
  __component: 'common.media',
  Media: [
    {
      IsDownloadable: true,
      Media: {
        data: {
          attributes: {
            url: '/uploads/image.jpg',
            alternativeText: 'Description',
            width: 1200,
            height: 800,
          }
        }
      }
    }
  ]
}
```

### Media Upload

The application supports uploading media to Strapi:

```typescript
// Example of media upload
const uploadMedia = async (file: File) => {
	const formData = new FormData()
	formData.append("files", file)

	const res = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/upload`, {
		method: "POST",
		headers: {
			Authorization: `Bearer ${authToken}`,
		},
		body: formData,
	})

	if (!res.ok) {
		throw new Error("Failed to upload media")
	}

	return res.json()
}
```

## Internationalization

The application supports internationalization through Strapi's localization features:

### Locale Parameter

When fetching content, the application specifies the desired locale:

```typescript
url.searchParams.append("locale", locale)
```

### Localized Content

Content in Strapi can be localized, with different values for each supported language:

```typescript
// Fetching content in English
const enContent = await getCmsData({ path: "home-page", locale: "en" })

// Fetching the same content in French
const frContent = await getCmsData({ path: "home-page", locale: "fr" })
```

### Default Locale

If no locale is specified, Strapi returns content in the default locale:

```typescript
// Fetching content without specifying a locale
const defaultContent = await getCmsData({ path: "home-page" })
```

## Error Handling

The application includes error handling for API communication:

### Request Errors

```typescript
if (!res.ok) {
	throw new Error(`Failed to fetch data: ${res.status}`)
}
```

### Not Found Handling

```typescript
try {
	const data = await getCmsData({ path: "page", filter: `filters[slug][$eq]=${slug}` })
	if (!data.data) {
		notFound()
	}
	return data
} catch (error) {
	notFound()
}
```

### Authentication Errors

```typescript
try {
	// Authentication request
} catch (error) {
	setErrors({
		...errors,
		overall: "Login failed. Please check your credentials and try again.",
	})
}
```

This comprehensive integration with Strapi allows the Collect CMS application to provide a powerful and flexible content management experience.
