"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx] || !childCmp[idx].onChange) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        // Determine if this is a repeatable component by checking if the parent value is an array\n        // We need to check the parent's structure to understand if this is part of an array\n        var parentEntry = childCmp[idx - 1] // Previous level entry\n        ;\n        if (parentEntry && Array.isArray(parentEntry.value)) {\n            // This is a repeatable component - remove from array\n            var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n            var targetIndex = parentValue.findIndex(function(item) {\n                return item === currentValue;\n            });\n            if (targetIndex !== -1 && parentEntry.onChange) {\n                parentValue.splice(targetIndex, 1);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        } else {\n            // This is a non-repeatable component - set to null/empty\n            if (parentEntry && parentEntry.onChange) {\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: null\n                });\n            }\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx] || !childCmp[idx].onChange) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        // Determine if this is a repeatable component\n        var parentEntry = childCmp[idx - 1] // Previous level entry\n        ;\n        if (parentEntry && Array.isArray(parentEntry.value)) {\n            // This is a repeatable component - duplicate in array\n            var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n            var targetIndex = parentValue.findIndex(function(item) {\n                return item === currentValue;\n            });\n            if (targetIndex !== -1 && parentEntry.onChange) {\n                // Create a deep copy of the item to duplicate\n                var duplicatedItem = JSON.parse(JSON.stringify(currentValue));\n                parentValue.splice(targetIndex + 1, 0, duplicatedItem);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 128,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});