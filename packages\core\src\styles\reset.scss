*,
*::after,
*::before {
	box-sizing: border-box;
}

* {
	font: inherit;
	margin: 0;
	padding: 0;
	border: 0;
}

ol,
ul,
menu {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

mark {
	background-color: transparent;
	color: inherit;
}

button,
input,
textarea,
select,
.reset {
	border: 0;
	background-color: transparent;
	border-radius: 0;
	color: inherit;
	line-height: inherit;
	appearance: none;
}

textarea {
	resize: vertical;
	overflow: auto;
	vertical-align: top;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

img,
video,
svg {
	max-width: 100%;
	display: block;
}

b,
strong {
	font-weight: bold;
}
