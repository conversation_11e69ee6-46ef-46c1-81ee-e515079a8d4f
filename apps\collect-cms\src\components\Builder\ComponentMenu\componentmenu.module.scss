@import '@/styles/config';

.wrapper {
	padding-top: spacing(s6);
	display: flex;
	flex-direction: column;
	gap: spacing(s5);
	width: px-to(360px, rem);
	background-color: color('white', 0);
	box-shadow: 5px 5px 24px 0px rgba(0, 0, 0, 0.25);
	border-radius: px-to(10px, rem);
	max-height: px-to(420px, rem);
	overflow: hidden;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 8;

	.search {
		padding-left: spacing(s6);
		padding-right: spacing(s6);
		flex: auto;
		--input-bg: #{color('white', 5)};
	}

	.inner {
		overflow: auto;
		padding-left: spacing(s6);
		padding-right: spacing(s6);
		padding-bottom: spacing(s6);
	}

	.group {
		> p {
			background-color: color('white', 0);
			padding-bottom: spacing(s5);
			position: sticky;
			top: 0;
			padding-left: spacing(s6);
			margin-left: calc(-1 * spacing(s6));
			margin-right: calc(-1 * spacing(s6));
			transition: 0.2s;
			color: color('grey', 50);

			&:global(.is__sticky) {
				box-shadow: 0px spacing(s1) spacing(s6) 0px rgba(69, 69, 69, 0.15);
			}
		}

		&:not(:first-child) > p {
			padding-top: spacing(s5);
			top: calc(-1 * spacing(s5));
		}
	}

	.component {
		transition: 0.2s;
		cursor: pointer;
		display: grid;
		grid-template-columns: px-to(57px, rem) 1fr;
		align-items: center;
		gap: spacing(s4);
		padding: spacing(s3) spacing(s2);
		border-radius: spacing(s1);

		&:hover {
			background-color: color('grey', 10);
		}

		.thumbnail {
			height: px-to(40px, rem);
			background-color: color('white', 5);
			border-radius: px-to(2px, rem);
		}
	}
}
