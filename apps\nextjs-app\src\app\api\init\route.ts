import { enableMeiliSettings } from '@collective/integration-lib/search'
import { Mei<PERSON><PERSON>earch } from 'meilisearch'
import type { NextRequest } from 'next/server'

const CLOUDFLARE_HEADER = {
	'CF-Access-Client-Id': `${process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID}`,
	'CF-Access-Client-Secret': `${process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET}`,
}

const meiliClient = new MeiliSearch({
	host: `${process.env.NEXT_PUBLIC_MEILISEARCH_HOST}`,
	apiKey: `${process.env.MEILISEARCH_MASTER_KEY}`,
	requestConfig: {
		headers: {
			...(process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET ? CLOUDFLARE_HEADER : {}),
		},
	},
})

// export const runtime = 'edge'
export async function GET() {
	try {
		await enableMeiliSettings(meiliClient)
		return Response.json({ title: 'Initializing completed!', status: 'success' }, { status: 200 })
	} catch (error) {
		return Response.json({ title: 'Error when initializing!', status: 'error' }, { status: 400 })
	}
}
