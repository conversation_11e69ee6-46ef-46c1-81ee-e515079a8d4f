/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_TextHorizon_texthorizon_module_scss";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_TextHorizon_texthorizon_module_scss"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss":
/*!*********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss ***!
  \*********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"texthorizon_wrapper__OQFLj\",\n\t\"headline\": \"texthorizon_headline__QGU5y\",\n\t\"content\": \"texthorizon_content__qfSfB\",\n\t\"blockquote\": \"texthorizon_blockquote__RlgAz\",\n\t\"paragraph\": \"texthorizon_paragraph__ecVT8\"\n};\n\nmodule.exports.__checksum = \"f00ff7b5a709\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9UZXh0SG9yaXpvbi90ZXh0aG9yaXpvbi5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVGV4dEhvcml6b24vdGV4dGhvcml6b24ubW9kdWxlLnNjc3M/OTYxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwidGV4dGhvcml6b25fd3JhcHBlcl9fT1FGTGpcIixcblx0XCJoZWFkbGluZVwiOiBcInRleHRob3Jpem9uX2hlYWRsaW5lX19RR1U1eVwiLFxuXHRcImNvbnRlbnRcIjogXCJ0ZXh0aG9yaXpvbl9jb250ZW50X19xZlNmQlwiLFxuXHRcImJsb2NrcXVvdGVcIjogXCJ0ZXh0aG9yaXpvbl9ibG9ja3F1b3RlX19SbGdBelwiLFxuXHRcInBhcmFncmFwaFwiOiBcInRleHRob3Jpem9uX3BhcmFncmFwaF9fZWNWVDhcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZjAwZmY3YjVhNzA5XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss\n");

/***/ })

};
;