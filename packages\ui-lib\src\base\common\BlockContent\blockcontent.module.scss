@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	margin-top: spacing(s6);
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: px-to(1px, rem);
		background-color: color('divider');
	}

	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		padding-top: spacing(s12);
		// padding-bottom: spacing(s6);

		@include min-width('2md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}

	.headline {
		font-weight: 500;
		color: color('black', 100);

		@include min-width('2md') {
			grid-column: span 6;
		}
	}

	.content {
		@include min-width('2md') {
			grid-column-start: var(--isHasHeadline);
			grid-column-end: span 6;
		}
		display: grid;
		gap: spacing(s6);
		&__grid {
			@include min-width('2md') {
				grid-template-columns: repeat(2, 1fr);
			}
			.block {
				grid-column: span 1;
			}
		}
	}

	.block {
		padding: px-to(18px, rem) spacing(s6) spacing(s6);
		background-color: color('white', 100);
		display: flex;
		flex-direction: column;
		gap: px-to(6px, rem);
		border-radius: spacing(s4);

		&__title {
			@include fluid($font-size) {
				font-size: size('paragraph', 'lg');
			}
			font-weight: 500;
			color: color('black', 100);
			line-height: 1.5;
		}
		&__content {
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
			color: color('neutral-gray');
			line-height: 1.5;

			ul {
				padding-left: spacing(s6);
				list-style-type: disc;
			}
			i,
			em {
				font-style: italic;
			}
			b,
			strong {
				font-weight: 700;
			}
			a,
			u {
				text-decoration: underline;
				text-decoration-skip-ink: none;
			}
			del {
				text-decoration: line-through;
			}
		}
	}

	&:global(.nohead) {
		margin-top: 0;
		&::before {
			display: none;
		}
		:global(.aidigi__grid) {
			padding-top: 0;
		}
	}
}
