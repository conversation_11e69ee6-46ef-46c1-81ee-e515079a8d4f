/* eslint-disable @typescript-eslint/naming-convention */
export const replaceDynamicRoute = (route: string, slug: string, dynamicValue: string): string =>
	route.replace(`/[${slug}]`, encodeURIComponent(dynamicValue))

export const randomString = (length: number) => Math.random().toString(36).substring(length)

export const getBase64FromUrl = async (url: string) => {
	const data = await fetch(url)
	const blob = await data.blob()
	return new Promise((resolve) => {
		const reader = new FileReader()
		reader.readAsDataURL(blob)
		reader.onloadend = () => {
			const base64data = reader.result
			resolve(base64data)
		}
	})
}

export const isImage = (url: string) => /\.(jpg|jpeg|png|webp|avif|gif|svg)$/.test(url)

export const fetcher = async (url: string, options: RequestInit) => {
	const response = await fetch(url, options)
	if (!response.ok) {
		const errorData = await response.text()
		console.info(url, errorData)
		return Promise.reject(new Error(`Failed to fetch ${url} with error ${errorData}`))
	}
	const data = await response.json()
	return data
}
