name: CI

on: [push]

jobs:
  build:
    runs-on: self-hosted
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.COLLECTIVE_ACCESS_TOKEN }}
          submodules: 'true'

      - name: Cache turbo build setup
        uses: maxnowack/local-cache@v2
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Cache yarn packages
        uses: maxnowack/local-cache@v2
        with:
          path: .yarn/cache
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install package manager (from package.json)
        run: |
          corepack enable
          corepack install

      - name: Install dependencies
        run: yarn install

      - name: Lint
        run: yarn g:lint

      - name: Build
        run: yarn g:build-cloudflare

      - name: Deploy
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy ./apps/nextjs-app/.vercel/output/static --project-name=ai-digital-brand-frontend-template-smooth
