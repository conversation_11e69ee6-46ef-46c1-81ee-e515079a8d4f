@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);

	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		// padding: 0;
		// padding-top: spacing(s12);
		// padding-bottom: spacing(s6);

		@include min-width('2md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}

	.content {
		@include min-width('2md') {
			grid-column-start: var(--position);
			grid-column-end: span var(--total);
			grid-template-columns: repeat(var(--total), 1fr);
		}

		display: grid;
		gap: spacing(s6);
	}

	.block {
		padding: px-to(18px, rem) spacing(s6) spacing(s6);
		background-color: color('white', 100);
		display: flex;
		flex-direction: column;
		gap: px-to(6px, rem);
		border-radius: spacing(s4);
		grid-column: span var(--layout);

		&__title {
			color: color('light-gray', 100);
			display: flex;
			// align-items: center;
			gap: spacing(s2);
			h4 {
				@include fluid($font-size) {
					font-size: size('paragraph', 'lg');
				}
			}
			svg {
				--size: #{px-to(18px, rem)};
				margin-top: px-to(2px, rem);
			}
			// background-color: color('light-gray', 100);
		}
		&__content {
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}

			color: color('neutral-gray');
			line-height: 1.5;
			ul {
				padding-left: spacing(s6);
				list-style-type: disc;
			}
			i,
			em {
				font-style: italic;
			}
			b,
			strong {
				font-weight: 700;
			}
			a,
			u {
				text-decoration: underline;
				text-decoration-skip-ink: none;
			}
			del {
				text-decoration: line-through;
			}
		}
	}

	:global(.block__green) {
		background-color: color('green', 10);
		.block__title {
			color: color('green', 100);
		}
	}
	:global(.block__red) {
		background-color: color('red', 10);
		.block__title {
			color: color('red', 100);
		}
	}
	:global(.block__yellow) {
		background-color: color('yellow', 10);
		.block__title {
			color: color('yellow', 100);
		}
	}
}
