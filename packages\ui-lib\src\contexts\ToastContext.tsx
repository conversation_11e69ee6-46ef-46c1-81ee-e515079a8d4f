'use client'

import { useIsomorphicLayoutEffect } from '@collective/core'
import { createContext, useContext, useState, type ReactNode } from 'react'

type ToastType = 'success' | 'error' | 'info' | 'warning'
export type ToastDataType = {
	tId: string
	type: ToastType
	icon?: string
	message: string | ReactNode
}
interface ToastContextType {
	toastData?: ToastDataType[]
	onPushToast: (data: ToastDataType) => void
	onRemoveToast: (tId: string) => void
}

export const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const ToastProvider = ({ children }: { children: ReactNode }) => {
	const [toastData, setToastData] = useState<ToastDataType[]>([])

	// useIsomorphicLayoutEffect(() => {
	// 	console.log(toastData)
	// }, [toastData])

	const onPushToast = (data: ToastDataType) => setToastData((prev) => [...prev, data])

	const onRemoveToast = (tId: string) =>
		setToastData((prev) => prev.filter((toast) => toast.tId !== tId))

	return (
		<ToastContext.Provider value={{ toastData, onPushToast, onRemoveToast }}>
			{children}
		</ToastContext.Provider>
	)
}

export const useToast = () => {
	const context = useContext(ToastContext)
	if (!context) throw new Error('useToast must be used within ToastProvider')
	return context
}
