'use client'

import { debounce, Icon } from '@collective/core'
import { useState } from 'react'
import styles from './color.module.scss'

type ColorProps = {
	Colors: {
		ColorName: string
		HexColor: string
		AdditionalField: {
			FieldName: string
			FieldValue: string
		}[]
	}[]
}

export const Color = ({ Colors }: ColorProps) => (
	<section className={styles.wrapper}>
		<div className="aidigi__grid">
			<div
				className={styles.row}
				style={
					{
						'--count': Colors.length,
					} as React.CSSProperties
				}
			>
				{Colors?.map((color, idx) => {
					return (
						<div key={idx} className={styles.column}>
							<BgColor color={color.HexColor} />
							<table className={styles.description}>
								<caption>{color.ColorName}</caption>
								<tbody>
									<tr>
										<th>hex:</th>
										<td>{color.HexColor}</td>
									</tr>
									<tr>
										<th>rgb:</th>
										<td>{hexToRgb(color.HexColor)}</td>
									</tr>
									{color?.AdditionalField?.map((field, fieldIdx) => (
										<tr key={fieldIdx}>
											<th>{field.FieldName}:</th>
											<td>{field.FieldValue}</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					)
				})}
			</div>
		</div>
	</section>
)

const hexToRgb = (hex: string) => {
	// Remove the "#" if present
	hex = hex.replace(/^#/, '')

	// Parse the hex values
	let r, g, b
	if (hex.length === 3) {
		// Short format (#RGB)
		r = parseInt((hex[0] as string) + hex[0], 16)
		g = parseInt((hex[1] as string) + hex[1], 16)
		b = parseInt((hex[2] as string) + hex[2], 16)
	} else if (hex.length === 6) {
		// Full format (#RRGGBB)
		r = parseInt(hex.substring(0, 2), 16)
		g = parseInt(hex.substring(2, 4), 16)
		b = parseInt(hex.substring(4, 6), 16)
	} else {
		throw new Error('Invalid HEX color.')
	}

	return `${r}, ${g}, ${b}`
}

const BgColor = ({ color }: { color: string }) => {
	const [isCopied, setIsCopied] = useState(false)
	const lazyCopy = debounce(() => {
		setIsCopied(false)
	}, 250)

	return (
		<div
			onKeyDown={() => {
				navigator.clipboard.writeText(color)
				lazyCopy.clear()
				setIsCopied(true)
				lazyCopy()
			}}
			tabIndex={0}
			role="button"
			className={styles.bg__color}
			style={{ background: color }}
			onClick={() => {
				navigator.clipboard.writeText(color)
				lazyCopy.clear()
				setIsCopied(true)
				lazyCopy()
			}}
		>
			<div className={styles.tag}>
				{isCopied ? (
					<>
						<Icon variant="copy" /> Copied
					</>
				) : (
					color
				)}
			</div>
		</div>
	)
}
