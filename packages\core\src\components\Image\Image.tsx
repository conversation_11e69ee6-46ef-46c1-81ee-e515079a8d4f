'use client'

import cn from 'classnames'
import _ from 'lodash'
import NextImage from 'next/image'
import React from 'react'
import { createPngDataUri } from './blurhash'
import styles from './image.module.scss'

const isImage = (url: string) => /\.(jpg|jpeg|png|webp|avif|gif|svg)$/.test(url)

const imageLoader = ({ src, width, quality }: { src: string; width: number; quality: number }) =>
	`${process.env.NEXT_PUBLIC_STRAPI_HOST}${src}?w=${width}&q=${quality || 75}`

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Image = React.forwardRef((props: any, ref) => {
	const { media: media0, src: src0, isRemote, blurhash, ...rest } = props
	let media = media0 || src0

	const DEBUG_COMPONENT: string = ''
	// const DEBUG_COMPONENT: string = "Product/ProductPreview"

	if (DEBUG_COMPONENT !== '' && rest.component === DEBUG_COMPONENT) {
		console.log('Pre-check', media0, src0, media)
	}

	if (media === undefined || media.data === null || _.isEmpty(media)) {
		return <NextImage src={media} alt="No image provided" {...rest} />
	}

	let type = 'local'
	let { alt } = rest
	let src = ''

	if (media !== undefined && (media.url === undefined || media.blurhash !== undefined)) {
		if (media.blurhash !== undefined && media.blurhash !== null) {
			type = 'remote'
		}

		if (
			media.data !== undefined &&
			media.data !== null &&
			media.data.attributes !== undefined &&
			media.data.attributes !== null
		) {
			media = media.data.attributes
			type = 'remote'
		} else if (media.attributes !== undefined && media.attributes !== null) {
			media = media.attributes
			type = 'remote'
		} else if (media.data !== undefined && media.data !== null) {
			media = media.data
			type = 'local'
		}

		if (media.width !== undefined && media.height !== undefined && rest.fill === undefined) {
			rest.width = media.width
			rest.height = media.height
		}
		alt = media.alternativeText !== null ? media.alternativeText : media.name
	}

	if (DEBUG_COMPONENT !== '' && rest.component === DEBUG_COMPONENT) {
		console.log('After-check', media, 'type', type)
	}

	if (media.url !== undefined) {
		src = media.url
	} else if (media.src !== undefined) {
		src = media.src
	} else {
		src = media
	}

	if (DEBUG_COMPONENT !== '' && rest.component === DEBUG_COMPONENT) {
		console.log('src', src)
	}

	if (src.includes('_next/static')) {
		type = 'local'
	}

	if (isRemote) {
		type = 'remote'
	}

	if (!isImage(src)) {
		return (
			<div>
				<video autoPlay loop muted playsInline {...rest}>
					<track kind="captions" />
					<source src={`${process.env.NEXT_PUBLIC_STRAPI_HOST}${src}`} type="video/mp4" />
					Your browser does not support the video tag.
				</video>
			</div>
		)
	}

	if (rest.width === undefined && rest.fill === undefined) {
		rest.width = media.width || 0
	}

	if (rest.height === undefined && rest.fill === undefined) {
		rest.height = media.height || 0
	}

	const blurhashData = media.blurhash ? media.blurhash : blurhash
	if (rest.parallax) {
		return type === 'remote' ? (
			<ParallaxEffect className={rest.className}>
				<NextImage
					loader={imageLoader}
					src={src}
					alt={alt}
					placeholder={rest.placeholder || 'blur'}
					blurDataURL={createPngDataUri(blurhashData, {
						ratio: rest.width / rest.height,
					})}
					ref={ref}
					{...rest}
					className="coeus__img"
				/>
			</ParallaxEffect>
		) : (
			<ParallaxEffect className={rest.className}>
				<NextImage ref={ref} src={src} alt={alt} {...rest} className="coeus__img" />
			</ParallaxEffect>
		)
	}

	if (type === 'remote') {
		return (
			<NextImage
				loader={imageLoader}
				src={src}
				alt={alt}
				placeholder="blur"
				blurDataURL={createPngDataUri(blurhashData, {
					ratio: rest.width / rest.height,
				})}
				ref={ref}
				{...rest}
			/>
		)
	}

	return <NextImage ref={ref} src={src} alt={alt} {...rest} />
})

const ParallaxEffect = ({
	children,
	className,
}: {
	children: React.ReactNode
	className: string
}) => {
	return <div className={cn(styles.wrapper, className)}>{children}</div>
}

export { Image }
