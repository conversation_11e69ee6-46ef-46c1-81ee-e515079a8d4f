export function getScrollbarWidth() {
    const htmlElm = document.documentElement;
    // Creating invisible container
    const outer = document.createElement('div');
    outer.style.visibility = 'hidden';
    outer.style.overflow = 'scroll'; // forcing scrollbar to appear
    document.body.appendChild(outer);
    // Creating inner element and placing it in the container
    const inner = document.createElement('div');
    outer.appendChild(inner);
    // Calculating difference between container's full width and the child width
    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
    const isScrollbar = htmlElm.scrollHeight - htmlElm.clientHeight;
    // Removing temporary elements from the DOM
    outer?.parentNode?.removeChild(outer);
    htmlElm.style.setProperty('--scrollbar-gutter', `${isScrollbar > 0 ? scrollbarWidth : 0}px`);
}
