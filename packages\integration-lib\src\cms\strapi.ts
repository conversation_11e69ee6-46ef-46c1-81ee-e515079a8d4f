/* eslint-disable @typescript-eslint/naming-convention */
const cfClientID = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID
const cfClientSecret = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET
const cloudflareHeader = {
	// eslint-disable-next-line @typescript-eslint/naming-convention
	'CF-Access-Client-Id': `${cfClientID}`,
	// eslint-disable-next-line @typescript-eslint/naming-convention
	'CF-Access-Client-Secret': `${cfClientSecret}`,
}

enum MediaFormat {
	THUMBNAIL = 'thumbnail',
}

type IMediaBaseSingleProps = {
	id?: number
	alternativeText?: string | null
	blurhash?: string | null
	height?: number
	name: string
	width?: number
	url: string
	hash?: string
	ext?: string
	mime?: string
	size?: number
	updatedAt?: string
	caption?: string
	formats?: {
		[key in MediaFormat]?: {
			name: string
			hash?: string
			ext?: string
			width?: number
			height?: number
			url: string
			blurhash?: string | null
		}
	}
}

type IMediaWrapperSingleProps = {
	data: {
		id?: number
		attributes: IMediaBaseSingleProps
	} | null
}
type IMediaWrapperMultipleProps = {
	data: {
		id?: number
		attributes: IMediaBaseSingleProps
	}[]
}

type IMediaWrapperProps<Type extends 'single' | 'multiple'> = Type extends 'multiple'
	? IMediaWrapperMultipleProps
	: IMediaWrapperSingleProps
type IMediaBaseProps<Type extends 'single' | 'multiple'> = Type extends 'multiple'
	? IMediaBaseSingleProps[]
	: IMediaBaseSingleProps

export type IMediaProps<
	Mode extends 'wrapper' | 'base' = 'wrapper',
	Type extends 'single' | 'multiple' = 'single',
> = Mode extends 'base' ? IMediaBaseProps<Type> : IMediaWrapperProps<Type>

export type IBaseCMSPageProps<PageProps = Record<string, unknown>> = {
	id: number
	attributes: PageProps
}

export type IWrapperCMSPageProps<
	Type extends 'single' | 'multiple',
	PageProps = Record<string, unknown>,
> = Type extends 'multiple'
	? { data: IBaseCMSPageProps<PageProps>[] }
	: { data: IBaseCMSPageProps<PageProps> }

export type IComponentProps = {
	id?: string | number
	__component?: string
	__temp_key__?: string | number
	enable?: boolean
	[key: string]: unknown
}

export const getCmsData = async <PageProps, Type extends 'single' | 'multiple' = 'single'>({
	path,
	deep = 4,
	locale = 'en',
	draft = false,
	filter,
	revalidate = 120,
}: {
	path: string
	deep?: number
	locale?: string
	draft?: boolean
	filter?: string
	revalidate?: number
}): Promise<IWrapperCMSPageProps<Type, PageProps>> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}?pLevel=${deep}&locale=${locale}${draft ? '&publicationState=preview' : ''}${filter ? `&${filter}` : ''}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
				...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
				'Strapi-Response-Format': 'v4',
			},
			next: { revalidate },
		}
	)
	if (!response.ok) {
		const errorData = await response.text()
		console.info(path, errorData)
		return Promise.reject(new Error(`Failed to fetch ${path} with error ${errorData}`))
	}

	return response.json()
}

export const postFormCmsData = async ({ path, body }: { path: string; body: FormData }) => {
	const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`, {
		method: 'POST',
		headers: {
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_POST_API_KEY}`,
			...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
			'Strapi-Response-Format': 'v4',
		},
		body,
	})
	if (!response.ok) {
		return Promise.reject(new Error(`Failed to post ${path} with error ${await response.text()}`))
	}
	return response.json()
}

export const postJsonFormData = async <PageProps>({
	fullPath,
	body,
}: {
	fullPath: string
	body: string
}): Promise<PageProps> => {
	const cfClientID = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID
	const cfClientSecret = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET

	const response = await fetch(fullPath, {
		method: 'POST',
		headers: {
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_POST_API_KEY}`,
			// eslint-disable-next-line @typescript-eslint/naming-convention
			'Content-Type': 'application/json',
			...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
			'Strapi-Response-Format': 'v4',
		},
		body,
	})
	if (!response.ok) {
		return Promise.reject(
			new Error(`Failed to post ${fullPath} with error ${await response.text()}`)
		)
	}
	return response.json()
}

type IUINavigationProps = {
	id: number
	title: string
	menuAttached: boolean
	path: string
	type: 'INTERNAL' | 'EXTERNAL' | 'WRAPPER'
	items: IUINavigationProps[]
}

export type INavigationProps = {
	label: string
	path: string
	children?: INavigationProps[]
}
export const getNavigationData = async (
	name: string = '1',
	type: string = 'TREE'
): Promise<INavigationProps[]> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/navigation/render/${name}?type=${type}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
				...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
				'Strapi-Response-Format': 'v4',
			},
			next: { revalidate: 120 },
		}
	)
	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}
	const data = ((await response.json()) as IUINavigationProps[]).filter((item) => item.menuAttached)
	const processedData = data.map((item) => {
		return {
			label: item.title,
			path: item.path,
			children:
				item.items?.map((child: { title: string; path: string }) => {
					return {
						label: child.title,
						path: child.path,
					}
				}) || [],
		}
	})
	return processedData
}

export type IAttributesDataProps = {
	[key: string]: unknown
	Components: {
		components: string[]
		required: boolean
	}
}

export type ISchemaProps = {
	attributes: IAttributesDataProps
	collectionName: string
	description: string
	displayName: string
	kind: string
	pluginOptions: { [key: string]: unknown }
	draftAndPublish: boolean
	pluralName: string
	singularName: string
	visible: boolean
}

export type IContentBuilderTypesProps = {
	data: { apiID: string; uid: string; schema: ISchemaProps }
}

export const getContentBuilderTypes = async (type: string): Promise<IContentBuilderTypesProps> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/content-types/${type}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
			},
			next: { revalidate: 120 },
		}
	)

	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}

	return response.json()
}

export type IContentTypesProps = {
	data: { apiID: string; uid: string; schema: ISchemaProps }
}

export const getContentTypes = async (type: string): Promise<IContentTypesProps> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/content-types/${type}/configuration`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_USER_STRAPI_API_KEY}`,
			},
			next: { revalidate: 120 },
		}
	)

	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}

	return response.json()
}

export type IComponentFieldProps = {
	component: string
	displayName: string
	repeatable: boolean
	required: boolean
	type: string
	customField: string
}

export type IComponentSchemaProps = {
	attributes: { [key: string]: IComponentFieldProps | unknown }
	collectionName: string
	description: string
	displayName: string
	icon: string
}

export type IContentTypeComponentsProps = {
	data: { uid: string; category: string; apiId: string; schema: IComponentSchemaProps }[]
}

export const getListComponents = async (): Promise<IContentTypeComponentsProps> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/components`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
				...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
			},
			next: { revalidate: 120 },
		}
	)
	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}

	return response.json()
}

export type IUserDataProps = {
	firstname: string
	id: number
	lastname: string
	username: string
}

export type IResultDataProps = {
	id?: number
	createdAt?: string
	createdBy?: IUserDataProps
	createdDate?: string
	locale?: string
	updatedAt?: string
	updatedBy?: IUserDataProps
	publishedAt?: string
	Slug?: string
	Title?: string
	Components: IComponentProps[]
	[key: string]: unknown
}

export type IPaginationDataProps = {
	page: number
	pageCount: number
	pageSize: number
	total: number
}

export type ICollectionTypesDataProps = {
	results: IResultDataProps[]
	pagination: IPaginationDataProps
}

export const getCollectionTypesData = async ({
	path,
	page = 1,
	pageSize = 10,
	sort = 'updatedAt:DESC',
	filter,
}: {
	path: string
	page?: number
	pageSize?: number
	sort?: string
	filter?: string
}): Promise<ICollectionTypesDataProps> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/collection-types/${path}?page=${page}&pageSize=${pageSize}&sort=${sort}${filter ? `&${filter}` : ''}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_USER_STRAPI_API_KEY}`,
			},
			next: { revalidate: 120 },
		}
	)

	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}

	return response.json()
}

export type IContentManagerComponentProps = {
	uid: string
	isDisplayed: boolean
	apiID: string
	category?: string
	info: {
		description?: string
		displayName: string
		name?: string
		pluralName?: string
		singularName?: string
	}
}

export const postJsonCustomPathData = async <Props>(
	path: string,
	raw: string | FormData
): Promise<Props> => {
	const response = await fetch(path, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
			'Strapi-Response-Format': 'v4',
		},
		body: raw,
	})
	if (!response.ok) {
		return Promise.reject(await response.text())
	}
	return response.json()
}

export const postJsonData = async <Props>(path: string, raw: string | FormData): Promise<Props> => {
	return postJsonCustomPathData(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`, raw)
}

export const createNewChat = async () => {
	const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/chats`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
			'Strapi-Response-Format': 'v4',
		},
		body: JSON.stringify({
			data: {
				Message: [],
				uuid: '',
			},
		}),
	})

	if (!response.ok) {
		return Promise.reject(new Error(`Failed to createChat with error ${await response.text()}`))
	}
	return response.json()
}
