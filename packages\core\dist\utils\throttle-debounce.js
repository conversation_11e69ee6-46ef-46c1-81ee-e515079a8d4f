export function debounce(fn, delay) {
    let timeoutID;
    let lastArgs;
    const run = () => {
        if (lastArgs) {
            fn(...lastArgs);
            lastArgs = undefined;
        }
    };
    const debounced = (...args) => {
        clearTimeout(timeoutID);
        lastArgs = args;
        timeoutID = window.setTimeout(run, delay);
    };
    debounced.clear = () => {
        clearTimeout(timeoutID);
        run();
    };
    return debounced;
}
export function throttle(fn, cooldown) {
    let lastArgs;
    const run = () => {
        if (lastArgs) {
            fn(...lastArgs);
            lastArgs = undefined;
        }
    };
    const throttled = (...args) => {
        const isOnCooldown = !!lastArgs;
        lastArgs = args;
        if (isOnCooldown) {
            return;
        }
        window.setTimeout(run, cooldown);
    };
    return throttled;
}
