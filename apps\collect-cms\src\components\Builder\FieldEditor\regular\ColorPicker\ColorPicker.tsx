import { Icon, Input } from '@collective/core'
import cn from 'classnames'
import { usePathname } from 'next/navigation'
import { useMemo, useState, useRef } from 'react'
import { HexColorPicker } from 'react-colorful'
import type { FieldProps } from '../../FieldEditor'
import styles from './colorpicker.module.scss'

export interface ColorPickerProps<T> extends FieldProps<T> {
	value?: T
	onChange: (props: { field: string; value: string }) => void
}

export const ColorPicker = <T,>(props: ColorPickerProps<T>) => {
	const pathname = usePathname()
	const { required, value, onChange, name, placeholder } = props
	const [propsValue, setPropsValue] = useState(value ?? '#000000')
	const [isFocused, setIsFocused] = useState<boolean>(false)
	const colorPickerRef = useRef<HTMLDivElement>(null)

	const handleFocus = () => setIsFocused(true)

	const handleBlur = (e: React.FocusEvent) => {
		if (colorPickerRef.current && !colorPickerRef.current.contains(e.relatedTarget as Node)) {
			setIsFocused(false)
		}
	}

	const handleSave = (e: React.MouseEvent) => {
		e.preventDefault()
		e.stopPropagation()
		onChange?.({ field: name as string, value: propsValue as string })
		setIsFocused(false)
	}

	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])

	return (
		<div
			className={cn(styles.wrapper, isBuilderMode ? styles.builder : '')}
			onFocus={handleFocus}
			onBlur={handleBlur}
		>
			<Input
				type="text"
				className="collect__input has__border"
				required={required}
				value={propsValue as string}
				placeholder={placeholder}
				maxLength={7}
				onChange={(e) => setPropsValue(e.target.value)}
				startIcon={
					<div
						className={styles.color__dot}
						style={{ '--color': propsValue } as React.CSSProperties}
					/>
				}
				endIcon={<Icon type="cms" variant="edit" />}
			/>
			<div
				ref={colorPickerRef}
				className={cn(styles.color__picker, isFocused ? styles.active : '')}
			>
				<HexColorPicker color={propsValue as string} onChange={(color) => setPropsValue(color)} />
				<button
					className="collect__button black"
					onClick={handleSave}
					onMouseDown={(e) => e.preventDefault()}
				>
					Save
				</button>
			</div>
		</div>
	)
}
