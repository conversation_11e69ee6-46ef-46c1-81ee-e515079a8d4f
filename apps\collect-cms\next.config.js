const path = require('path')
require('dotenv').config({ path: '../nextjs-app/.env' })

/** @type {import('next').NextConfig} */
const nextConfig = {
	reactStrictMode: true,
	images: {
		deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
		imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
	},
	transpilePackages: ['@collective/i18n'],
	// redirects() {
	//   return [
	//     {
	//       source: '/',
	//       destination: '/en',
	//       permanent: true,
	//     },
	//   ];
	// },
	optimizeFonts: true,
	sassOptions: {
		includePaths: [path.join(__dirname, 'styles')],
	},
	webpack(config, options) {
		config.resolve.alias['@/styles'] = path.join(__dirname, 'styles')
		config.resolve.alias['@collective/core'] = path.join(
			__dirname,
			'../../node_modules/@collective/core/dist'
		)
		return config
	},
	async rewrites() {
		return [
			{
				source: '/plau/js/script.js',
				destination: 'https://plausible.collect.vn/js/script.js',
			},
			{
				source: '/plau/api/event',
				destination: 'https://plausible.collect.vn/api/event',
			},
			{
				source: '/sitemap.xml',
				destination: 'https://cms.example.com/api/sitemap/index.xml',
			},
			{
				source: '/xsl/:path*',
				destination: 'https://cms.example.co/api/sitemap/xsl/:path*',
			},
		]
	},
	experimental: {
		optimizePackageImports: [
			'@collective/core',
			'@collective/ui-lib',
			'@collective/integration-lib',
		],
	},
}

module.exports = nextConfig
