import { Button, Icon } from '@collective/core'
import cn from 'classnames'
import styles from './header.module.scss'

type HeaderProps = {
	cid?: string
	Headline: string
	Subhead?: string
	ActionButton: {
		ButtonText: string
		ButtonLink: string
	}
}

export const Header = ({ cid, Headline, Subhead, ActionButton }: HeaderProps) => (
	<section id={cid} className={cn(styles.wrapper)}>
		<div className={cn('aidigi__grid')} style={{ alignItems: !Subhead ? 'center' : '' }}>
			<div className={styles.headline}>
				{Headline && <h1 className={cn('aidigi__heading--h1')}>{Headline}</h1>}
				{Subhead && <h3 className={cn('aidigi__heading--h3')}>{Subhead}</h3>}
			</div>
			{ActionButton?.ButtonText && (
				<div className={styles.download}>
					<Button
						className="aidigi__button outline"
						href={ActionButton?.ButtonLink}
						startIcon={<Icon className={cn(styles.arrowdown)} variant="arrow-down" />}
					>
						<p className="vc__paragraph--md">{ActionButton?.ButtonText}</p>
					</Button>
				</div>
			)}
		</div>
	</section>
)
