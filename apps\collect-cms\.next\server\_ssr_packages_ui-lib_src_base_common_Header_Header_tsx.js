/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_Header_Header_tsx";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_Header_Header_tsx"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Header/Header.tsx":
/*!***************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/Header.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss\");\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_header_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst Header = ({ cid, Headline, Subhead, ActionButton })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            style: {\n                alignItems: !Subhead ? \"center\" : \"\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline),\n                    children: [\n                        Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h1\"),\n                            children: Headline\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 18\n                        }, undefined),\n                        Subhead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h3\"),\n                            children: Subhead\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 4\n                }, undefined),\n                ActionButton?.ButtonText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().download),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"aidigi__button outline\",\n                        href: ActionButton?.ButtonLink,\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().arrowdown)),\n                            variant: \"arrow-down\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 18\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"vc__paragraph--md\",\n                            children: ActionButton?.ButtonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Header/Header.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss":
/*!***********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/header.module.scss ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"header_wrapper__o2Cif\",\n\t\"headline\": \"header_headline__T2YEt\",\n\t\"download\": \"header_download__bfE6Q\"\n};\n\nmodule.exports.__checksum = \"ccb0339c45aa\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9IZWFkZXIvaGVhZGVyLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9oZWFkZXIubW9kdWxlLnNjc3M/YjlkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiaGVhZGVyX3dyYXBwZXJfX28yQ2lmXCIsXG5cdFwiaGVhZGxpbmVcIjogXCJoZWFkZXJfaGVhZGxpbmVfX1QyWUV0XCIsXG5cdFwiZG93bmxvYWRcIjogXCJoZWFkZXJfZG93bmxvYWRfX2JmRTZRXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImNjYjAzMzljNDVhYVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss\n");

/***/ })

};
;