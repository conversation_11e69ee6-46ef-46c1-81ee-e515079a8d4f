import {
	getCmsData,
	type ICategoryProps,
	type IComponentProps,
	type IMediaProps,
	type IPageProps,
} from '@collective/integration-lib/cms'
import OpenAI from 'openai'

const openai = new OpenAI()

type Props = {
	uuid: string
	Message: { role: string; content: string; time: string }[]
}

async function updateChat(
	uuid: string,
	messages: { role: string; content: string; time: string }[]
) {
	const chatData = await getCmsData<Props, 'multiple'>({
		path: `chats`,
		deep: 3,
		filter: `filters[uuid][$eq]=${uuid}`,
		revalidate: 0,
	})
	if (!chatData.data[0]) {
		return Promise.reject(new Error('Chat data not found'))
	}

	const { documentId } = chatData.data[0]
	const { Message: CMSMessage } = chatData.data[0]

	const newMessage = [...CMSMessage, ...messages]
	const req = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/chats/${documentId}`, {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
		body: JSON.stringify({
			data: {
				uuid: uuid,
				Message: newMessage,
			},
		}),
	})

	if (!req.ok) {
		console.error(`Error updating chat: ${req.status} ${req.statusText}`)
		return Promise.reject(await req.text())
	}
	return req.json()
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const pick = (obj: { [x: string]: any }, ...keys: any[]) =>
	Object.fromEntries(keys.filter((key) => key in obj).map((key) => [key, obj[key]]))

// This function is used to cleanup the data from CMS
// But it's currently a mess and needs to be refactored
const cleanupComponentData = (data: IComponentProps[]) => {
	return data
		.filter((component) => component.enable === true)
		.map((component) => {
			component = pick(component, 'Headline', 'Subhead', 'Paragraph', 'Category', 'Colors', 'Media')
			if (Array.isArray(component.Media)) {
				component.Media = component.Media.map((media: { Headline: string; Media: IMediaProps }) => {
					if ('Headline' in media) {
						media = pick(media, 'Headline', 'Media', 'DefaultBackground')
						if ('Media' in media) {
							media.Media = pick(media.Media, 'url', 'alternativeText', 'caption')
							media.Media.url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/uploads${media.Media.url}?w=384&q=75`
						}
					}
					return media
				})
			}
			return component
		})
}

const generatePropmtFromCMSData = (rawData: IPageProps[] | ICategoryProps[]) => {
	for (const data of rawData) {
		const components = cleanupComponentData(data.components)
		if ('Category' in data) {
			basePrompt.push({
				role: 'system',
				content: `Trong trang ${data.Category?.Headline}/${data.Headline} tại link: /${data.Category?.slug}/${data.slug} có các thông tin dạng json: ${JSON.stringify(components)}.`,
			})
		}
		if ('Pages' in data) {
			basePrompt.push({
				role: 'system',
				content: `Trong trang ${data.Headline} tại link: /${data.slug} có các thông tin dạng json: ${JSON.stringify(components)}.`,
			})
		}
	}
}

const basePrompt: { role: 'system' | 'user' | 'assistant'; content: string }[] = [
	{
		role: 'system',
		content:
			'Bạn là CōAI, một hệ thống AI của Collective Design Agency được tạo ra để giúp trả lời các câu hỏi của người dùng liên quan đến thiết kế sản phẩm của công ty cho khách hàng trong trang web này.',
	},
	{
		role: 'system',
		content:
			'Bạn là một chuyên gia trong ngành thiết kế đồ họa với con mắt thẩm mỹ và độ hiểu biết cao nhiều năm kinh nghiệm. Cố gắng trả lời các câu hỏi liên quan đến thiết kế đồ họa một cách thật chính xác và đúng với yêu cầu.',
	},
	{
		role: 'system',
		content:
			'Không trả lời các câu hỏi liên quan đến thông tin cá nhân hoặc bất kỳ thông tin nào mà bạn không muốn chia sẻ với CōAI. CōAI sẽ không lưu trữ bất kỳ thông tin cá nhân nào của bạn.',
	},
	{
		role: 'system',
		content:
			'Không trả lời các câu hỏi ngoài lề hoặc không liên quan đến thiết kế đồ họa. Cố gắng đặt câu hỏi một cách rõ ràng và cụ thể để CōAI có thể trả lời chính xác nhất có thể.',
	},
]

// export const runtime = 'edge'
export async function POST(request: Request) {
	const req = await request.json()
	if (req.text === undefined) {
		return Response.json({ error: 'No text provided' }, { status: 400 })
	}
	if (req.uuid === undefined) {
		return Response.json({ error: 'No uuid provided' }, { status: 400 })
	}

	const chatData = await getCmsData<Props, 'multiple'>({
		path: `chats`,
		deep: 3,
		filter: `filters[uuid][$eq]=${req.uuid}`,
		revalidate: 0,
	})

	if (!chatData.data[0]) {
		return Response.json({ error: 'Chat data not found' }, { status: 404 })
	}

	const { Message } = chatData.data[0]
	const question = req.text
	const messages = [{ role: 'user', content: question, time: new Date().toISOString() }]

	const [pagesDataRaw, categoriesDataRaw] = await Promise.all([
		getCmsData<IPageProps, 'multiple'>({
			path: `pages`,
			deep: 4,
		}),
		getCmsData<ICategoryProps, 'multiple'>({
			path: `categories`,
			deep: 4,
		}),
	])
	await generatePropmtFromCMSData(pagesDataRaw.data)
	await generatePropmtFromCMSData(categoriesDataRaw.data)

	// return Response.json(basePrompt, { status: 200 })

	const [completion] = await Promise.all([
		openai.chat.completions.create({
			model: 'gpt-4o-mini',
			messages: [
				...basePrompt,
				{
					role: 'system',
					content:
						'Trả lời duới dạng HTML và inline css. KHÔNG DÙNG MARKDOWN. Trong trường hợp có "DefaultBackground" tại data json, đặt css background của <img> đó với giá trị của "DefaultBackground"',
				},
				...Message.map((msg) => ({ role: msg.role as 'user' | 'assistant', content: msg.content })),
				{ role: 'user', content: `${question}` },
			],
		}),
	])

	if (completion.choices[0]?.message?.content) {
		const answer = completion.choices[0]?.message?.content
		messages.push({ role: 'assistant', content: answer, time: new Date().toISOString() })
		await updateChat(req.uuid, messages)
		return Response.json({ answer }, { status: 200 })
	}

	return Response.json({ error: 'Internal Server Error' }, { status: 500 })
}
