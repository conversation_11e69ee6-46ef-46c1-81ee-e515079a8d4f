import { decodeBlurHash } from 'fast-blurhash';
import { rgbaToDataUri } from './png';
function getScaledDimensions(aspectRatio, referenceSize) {
    let width;
    let height;
    if (aspectRatio >= 1) {
        width = referenceSize;
        height = Math.round(referenceSize / aspectRatio);
    }
    else {
        width = Math.round(referenceSize * aspectRatio);
        height = referenceSize;
    }
    return { width, height };
}
export function createPngDataUri(hash, { ratio = 1, size = 4 } = {}) {
    let hashData = hash;
    if (hashData === null || hashData === undefined) {
        hashData = 'L2MaR]?bfQ?b~qj[fQj[fQfQfQfQ';
    }
    const { width, height } = getScaledDimensions(ratio, size);
    const rgba = decodeBlurHash(hashData, width, height);
    const dataUri = rgbaToDataUri(width, height, rgba);
    return dataUri;
}
