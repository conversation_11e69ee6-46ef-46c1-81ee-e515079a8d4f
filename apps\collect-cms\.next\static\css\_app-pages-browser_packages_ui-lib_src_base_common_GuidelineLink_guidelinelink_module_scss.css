/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.guidelinelink_wrapper__2aoiX {
  margin-bottom: var(--section-mg-btm);
}
.guidelinelink_wrapper__2aoiX .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .guidelinelink_wrapper__2aoiX .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.guidelinelink_wrapper__2aoiX .guidelinelink_content__dM8Jn {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 90rem) {
  .guidelinelink_wrapper__2aoiX .guidelinelink_content__dM8Jn {
    grid-column-start: var(--position);
    grid-column-end: span var(--total);
    grid-template-columns: repeat(var(--total), 1fr);
  }
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__087_f {
  display: grid;
  grid-column: span var(--width);
  color: #1d1d1f;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__087_f a {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  font-weight: 500;
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__icon__q2qEL {
  color: #6e6e73;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__label__k4pQn {
  position: relative;
}
.guidelinelink_wrapper__2aoiX .guidelinelink_link__label__k4pQn::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.0625rem;
  width: 100%;
  background-color: #1d1d1f;
}
