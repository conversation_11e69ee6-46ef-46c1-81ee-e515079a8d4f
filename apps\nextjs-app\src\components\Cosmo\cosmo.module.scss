@use '@collective/ui-lib/styles/config' as *;

.cosmo {
	height: 100vh;
	:global(.aidigi__grid) {
		padding-bottom: spacing(s8);
		height: 100%;
		overflow: hidden;
		gap: spacing(s4);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
}

.wrapper {
	display: flex;
	flex-direction: column;
	gap: px-to(48px, rem);
	padding: px-to(96px, rem) px-to(92px, rem) 0;
	flex-shrink: 1;
	// height: auto;
	height: 100%;

	&__head {
		display: flex;
		flex-direction: column;
		gap: spacing(s2);
		flex-shrink: 0;

		h1 {
			font-size: px-to(48px, rem);
			font-weight: 500;
		}

		h3 {
			@include fluid($font-size) {
				font-size: size('heading', 'h3');
			}
			font-weight: 400;
			color: color('neutral-gray');
		}
	}
}

.recommendation {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: spacing(s3);
	height: 100%;

	&__item {
		padding: spacing(s4) spacing(s5);
		background-color: color('white', 100);
		border-radius: px-to(10px, rem);
		grid-column: span 1;
		max-height: px-to(220px, rem);
		height: 100%;
		gap: spacing(s6);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-end;
		cursor: pointer;

		&:hover {
			background-color: color('gray', 100);
		}

		* {
			color: color('neutral-gray');
			font-weight: 1.5;
		}

		p {
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
			font-weight: 500;
		}
		span {
			display: inline-block;
			padding: px-to(6px, rem) spacing(s3);
			background-color: #f0f0f0;
			border-radius: spacing(s2);
			@include fluid($font-size) {
				font-size: size('paragraph', 'sm');
			}
		}
	}
}

.dialog {
	padding-top: px-to(48px, rem);
	overflow: hidden;
	display: flex;
	flex-flow: column;
	height: 100%;

	.dialog__header {
		padding: spacing(s5) spacing(s6);
		background-color: color('black', 100);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.chat__status {
			align-items: center;
			display: flex;
			gap: px-to(15px, rem);
		}

		.avatar {
			border-radius: 50%;
			width: px-to(38px, rem);
			height: px-to(38px, rem);
			// background-color: color('white', 100);
			background-image: linear-gradient(#e0e8ff, #a5bafc);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				right: 0;
				bottom: px-to(5px, rem);
				width: spacing(s2);
				height: spacing(s2);
				border-radius: 50%;
				display: inline-block;
				background-color: #268750;
			}
		}

		p {
			@include fluid($font-size) {
				font-size: px-to(20px, rem);
			}
			font-weight: 400;
		}

		.close {
			cursor: pointer;
			border-radius: 50%;
			transition: 0.2s var(--ease-transition-2);

			&:hover {
				background-color: rgba(255, 255, 255, 0.1);
			}

			svg {
				color: #fff;
				--size: #{px-to(35px, rem)};
			}
		}
	}

	.dialog__body {
		padding: 0 px-to(92px, rem);
		display: grid;
		// grid-template-columns: 1fr;
		// grid-template-rows: auto;
		align-items: start;
		gap: px-to(48px, rem);
		// height: 100%;
		overflow: auto;
		transition: all 0.2s;

		&::-webkit-scrollbar-track {
			background-color: #f0f0f0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-image: -webkit-linear-gradient(90deg, #7a7a7a, #dddddd);
		}

		> div {
			// max-width: px-to(314px, rem);
			color: #646464;
		}

		.message {
			display: grid;
		}

		.box__chat {
			display: grid;
			gap: spacing(s1);

			.box {
				padding: spacing(s3) spacing(s4);
				border-radius: spacing(s3);
				line-height: px-to(30px, rem);
				font-weight: 400;
				display: flex;
				align-items: flex-end;
				flex-direction: column;
				gap: spacing(s2);
				@include fluid($font-size) {
					font-size: size('paragraph', 'md');
				}
				&:hover {
					.box__fn {
						opacity: 1;
					}
				}
				&__fn {
					opacity: 0;
					padding: spacing(s1);
					border-radius: spacing(s1);
					width: px-to(26px, rem);
					height: px-to(26px, rem);
					color: color('neutral-gray');
					&:hover {
						background-color: #f0f0f0;
					}
				}
				ul {
					padding-left: spacing(s6);
					list-style-type: disc;
				}
				i,
				em {
					font-style: italic;
				}
				b,
				strong {
					font-weight: 700;
				}
				a,
				u {
					text-decoration: underline;
					text-decoration-skip-ink: none;
				}
				del {
					text-decoration: line-through;
				}
			}
		}

		[data-message-role='bot'] {
			// display: grid;
			// grid-template-columns: auto 1fr;

			.box {
				place-self: flex-start;
				background-color: color('white', 100);
				color: color('black', 100);
			}
		}

		[data-message-role='user'] {
			margin-left: auto;

			.box {
				place-self: flex-end;
				background-color: #f0f0f0;
				color: color('black', 100);
				max-width: px-to(512px, rem);
			}
		}
	}

	.dialog__footer {
		padding: 0 spacing(s5) spacing(s3);
	}

	.chat__input {
		--input-radius: 9999px;
		--input-padding-x: #{spacing(s8)};
		--input-padding-y: #{spacing(s2)};
		--input-height: #{px-to(70px, rem)};
		--input-border-color: #e3e3e3;
		--input-bg: #fff;
		--icon-gap: #{px-to(10px, rem)};
		margin-bottom: px-to(10px, rem);

		button {
			cursor: pointer;
			pointer-events: all;
		}

		svg {
			color: color('black', 100);
			--size: #{px-to(30px, rem)};
		}

		input {
			color: #646464;

			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
			font-weight: 400;

			&:focus {
				--input-border-color: #{color('black', 50)};
			}
		}
	}

	.prompt__list {
		display: flex;
		flex-flow: wrap;
		gap: px-to(10px, rem);
		margin-bottom: px-to(10px, rem);

		button {
			cursor: pointer;
			padding: px-to(5px, rem) px-to(10px, rem);
			border-radius: px-to(10px, rem);
			background-color: color('dark-gray', 100);
			color: #646464;
		}
	}

	:global(.aidigi__buttons) {
		display: flex;
		flex-direction: column;
		gap: spacing(s2);
		margin-bottom: spacing(s3);
		* {
			width: 100%;
		}
	}

	:global(button.aidigi__link) {
		padding: 0;
		font-weight: 400;
		color: color('neutral-gray');
		&:hover {
			background: inherit;
			color: inherit;
			text-decoration: underline;
		}
	}

	:global(.exceed) {
		@include fluid($font-size) {
			font-size: ('paragraph', 'md');
		}
		font-weight: 500;
		color: color('neutral-gray');
		text-align: center;
		padding: 0 spacing(s6);
		margin-bottom: spacing(s2);
		line-height: 1.5;

		button {
			color: color('black', 100);
			padding: 0;
			&:hover {
				text-decoration: underline;
			}
		}
	}
}

.message {
	display: grid;
	gap: spacing(s3);
	padding: 0 px-to(92px, rem);

	&__chatbox {
		border: px-to(1px, rem) solid color('border');
		border-radius: var(--radius);
		color: color('neutral-gray');
		line-height: px-to(30px, rem);
		padding: spacing(s4) spacing(s8);
		transition: all 0.2s var(--ease-transition-2);
		display: flex;
		align-items: center;
		gap: spacing(s3);

		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}
		&.valued {
			flex-direction: column;
			align-items: flex-end;
		}
	}

	&__content {
		width: 100%;
		textarea {
			--input-radius: 0;
			--textarea-height: 30px;
			text-wrap: auto;
			resize: none;
			border: px-to(0.5px, rem) solid transparent;
			padding: 0 px-to(10px, rem) 0 0;
			font-weight: 400 !important;
			box-shadow: none;
			height: var(--textarea-height);
			line-height: px-to(30px, rem);

			&::-webkit-scrollbar-track {
				background-color: #f0f0f0;
			}

			&::-webkit-scrollbar {
				width: 6px;
			}

			&::-webkit-scrollbar-thumb {
				background-image: -webkit-linear-gradient(90deg, #7a7a7a, #dddddd);
			}

			&:focus {
				box-shadow: none;
				outline: none;
			}
		}

		&.less180 textarea {
			overflow: hidden;
		}
	}

	button.message__btn {
		padding: 0;
		&:hover {
			color: color('black', 100);
		}
		svg {
			--icon-size: #{px-to(24px, rem)};
		}
	}

	&__tips {
		color: color('neutral-gray');
		text-align: center;
		@include fluid($font-size) {
			font-size: size('paragraph', 'sm');
		}
	}
}
