import type { IMediaProps } from '@collective/integration-lib/cms';
import NextImage from 'next/image';
import React from 'react';
type NextImageProps = React.ComponentProps<typeof NextImage>;
export type ImageProps = Omit<NextImageProps, 'src'> & {
    blurhash?: string;
    isRemote?: boolean;
    component?: string;
    parallax?: boolean;
    media?: NextImageProps['src'] | IMediaProps<'single'>;
    src?: NextImageProps['src'];
};
declare const Image: React.ForwardRefExoticComponent<Omit<ImageProps, "ref"> & React.RefAttributes<HTMLImageElement>>;
export { Image };
