import type { Metadata } from 'next'
import type { IMediaProps } from '../cms'

type ISEOMetaSocialProps = {
	socialNetwork: string
	title: string
	description: string
	image: IMediaProps | null
}

export type ISEOProps = {
	metaTitle: string
	metaDescription: string
	keywords: string | null
	metaImage: IMediaProps
	metaSocial: ISEOMetaSocialProps[] | null
	metaRobots: string | null
	metaViewport: string | null
	structuredData: string | null
	canonicalURL: string | null
}

function generateTitle(defaultTitle: string, optional?: { title: string }) {
	const title = optional?.title || defaultTitle
	return title
}

function generateDescription(defaultDescription: string, optional?: { description: string }) {
	return optional?.description || defaultDescription
}

function generateImage(defaultImage?: IMediaProps, optional?: ISEOMetaSocialProps): IMediaProps {
	return (optional?.image || defaultImage || { url: '', name: '' }) as IMediaProps
}

export function createMetadata({
	seo,
	locale = 'en_US',
	defaultTitle,
	defaultDescription,
	defaultImage,
}: {
	seo: ISEOProps | null
	locale?: string
	defaultTitle: string
	defaultDescription?: string
	defaultImage?: IMediaProps
}): Metadata {
	if (seo === null || seo === undefined) {
		const ogImage: IMediaProps = generateImage(defaultImage)
		return {
			title: defaultTitle,
			description: defaultDescription,
			metadataBase: new URL(process.env.NEXT_PUBLIC_STRAPI_HOST as string),
			openGraph: {
				title: defaultTitle,
				description: defaultDescription,
				siteName: 'TBD',
				images: {
					url: ogImage
						? `${process.env.NEXT_PUBLIC_STRAPI_HOST}${ogImage.url.replace('/uploads/', '/uploads/format_jpg/')}`
						: '',
					alt: ogImage.alternativeText || ogImage.name || '',
				},
				locale: locale,
				type: 'website',
			},
		}
	}
	const {
		metaTitle,
		metaDescription,
		keywords,
		metaImage,
		metaSocial,
		metaRobots,
		metaViewport,
		canonicalURL,
	} = seo
	const metaSocialFacebook = metaSocial?.find(
		(social: { socialNetwork: string }) => social.socialNetwork === 'Facebook'
	)
	const metaSocialTwitter = metaSocial?.find(
		(social: { socialNetwork: string }) => social.socialNetwork === 'Twitter'
	)

	const title = generateTitle(metaTitle)
	const description = metaDescription

	const ogTitle = generateTitle(title, metaSocialFacebook)
	const ogDescription = generateDescription(metaDescription, metaSocialFacebook)
	let ogImage: IMediaProps
	if (defaultImage && metaImage === null) {
		ogImage = generateImage(defaultImage)
	} else {
		ogImage = generateImage(metaImage, metaSocialFacebook)
	}

	const twitterTitle = generateTitle(metaTitle, metaSocialTwitter)
	const twitterDescription = generateDescription(metaDescription, metaSocialTwitter)
	const twitterImage: IMediaProps = generateImage(metaImage, metaSocialTwitter)

	return {
		title,
		applicationName: 'TBD',
		description,
		keywords,
		metadataBase: new URL(process.env.NEXT_PUBLIC_STRAPI_HOST as string),
		alternates: {
			canonical: canonicalURL,
		},
		openGraph: {
			title: ogTitle,
			description: ogDescription,
			siteName: 'TBD',
			images: {
				url: `${process.env.NEXT_PUBLIC_STRAPI_HOST}${ogImage.url.replace('/uploads/', '/uploads/format_jpg/')}`,
				alt: ogImage.alternativeText || ogImage.name || '',
				width: ogImage.width || undefined,
				height: ogImage.height || undefined,
			},
			locale: locale,
			type: 'website',
		},
		twitter: {
			card: 'summary_large_image',
			title: twitterTitle,
			description: twitterDescription,
			images: `${process.env.NEXT_PUBLIC_STRAPI_HOST}${twitterImage.url}`,
		},
		robots: metaRobots,
		// viewport: metaViewport || "width=device-width, initial-scale=1",
	}
}
