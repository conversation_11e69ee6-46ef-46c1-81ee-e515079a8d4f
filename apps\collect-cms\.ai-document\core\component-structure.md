# CollectCMS Component Structure

This document outlines the component architecture of the CollectCMS application, explaining how different components interact and their responsibilities.

## Table of Contents

1. [Component Hierarchy](#component-hierarchy)
2. [Core Components](#core-components)
3. [Layout Components](#layout-components)
4. [Builder Components](#builder-components)
5. [Form Components](#form-components)
6. [Navigation Components](#navigation-components)
7. [Authentication Components](#authentication-components)

## Component Hierarchy

```mermaid
graph TD
    RootLayout[Root Layout] --> LngLayout[Language Layout]
    LngLayout --> DashboardLayout[Dashboard Layout]
    LngLayout --> PageBuilderLayout[Page Builder Layout]

    DashboardLayout --> AdminLayout[Admin Layout]
    AdminLayout --> AdminSidebar[Admin Sidebar]
    AdminLayout --> ContentArea[Content Area]

    ContentArea --> ContentManager[Content Manager]
    ContentArea --> SingleTypeLayout[Single Type Layout]
    ContentArea --> CollectionTypeLayout[Collection Type Layout]

    PageBuilderLayout --> BuilderSidebar[Builder Sidebar]
    PageBuilderLayout --> LayoutEditor[Layout Editor]

    LayoutEditor --> ComponentEditor[Component Editor]
    ComponentEditor --> FieldEditor[Field Editor]
```

## Core Components

### App Structure

The application is built using Next.js App Router with the following structure:

- `app/layout.tsx`: Root layout with global providers
- `app/[lng]/layout.tsx`: Language-specific layout
- `app/[lng]/(dashboard)/layout.tsx`: Dashboard group layout
- `app/[lng]/(pagebuilder)/layout.tsx`: Page builder group layout

### Context Providers

The application uses several context providers to share state:

1. **NavigationContext**: Provides navigation structure data
2. **BuilderContext**: Manages page builder state
3. **ManagerContext**: Manages content manager state
4. **GeneralSettingContext**: Provides global settings

## Layout Components

### AdminLayout

The main layout for the dashboard area:

```tsx
export const AdminLayout = (props: { children: ReactNode }) => {
	const { children } = props
	return (
		<div className={styles.wrapper}>
			<AdminSidebar />
			<main>
				<div className={styles.header}>
					<div className={styles.header_title}>Dashboard</div>
					<div className={styles.header_actions}>
						<UserMenu />
					</div>
				</div>
				<div className={styles.content}>{children}</div>
			</main>
		</div>
	)
}
```

### PageBuilderLayout

The layout for the page builder interface:

```tsx
export const PageBuilderLayout = ({
	children,
	value,
}: {
	children: ReactNode
	value: BuilderBaseProps
}) => {
	return (
		<PageBuilderProvider {...value}>
			<div className={styles.wrapper}>
				<BuilderSidebar />
				<main className={styles.content}>{children}</main>
			</div>
		</PageBuilderProvider>
	)
}
```

## Builder Components

### LayoutEditor

The main component for editing page layouts:

```tsx
export const LayoutEditor = () => {
	const context = useContext(PageBuilderContext)
	const { data, setData, components, editingId, setEditingId } = context

	// Component rendering logic

	return (
		<div className={styles.wrapper}>
			<div className={styles.header}>{/* Header content */}</div>
			<Board initial={normalizedData?.components} className={styles.body}>
				{/* Component blocks */}
			</Board>
			{menu && (
				<ComponentMenu data={uiConfig} onSelect={handleAddBlock} onClose={() => setMenu(null)} />
			)}
		</div>
	)
}
```

### ComponentEditor

For editing individual components:

```tsx
export const ComponentEditor = () => {
	const context = useContext(PageBuilderContext)
	const { data, setData, editingId, setEditingId } = context

	// Component editing logic

	return (
		<div className={styles.wrapper}>
			<div className={styles.header}>{/* Component type and controls */}</div>
			<div className={styles.body}>{/* Field editors for component properties */}</div>
		</div>
	)
}
```

### FieldEditor

Renders appropriate editors for different field types:

```tsx
export const FieldEditor = (props: FieldProps) => {
	const { type } = props

	// Select appropriate editor based on field type
	switch (type) {
		case "string":
			return <StringField {...props} />
		case "text":
			return <TextField {...props} />
		case "richtext":
			return <RichTextField {...props} />
		case "media":
			return <MediaField {...props} />
		case "component":
			return <Component {...props} />
		// Other field types
		default:
			return null
	}
}
```

## Form Components

### ContentForm

Generates forms based on content type schema:

```tsx
export const ContentForm = ({ contentType, data, onChange }: ContentFormProps) => {
	// Form generation logic

	return (
		<div className={styles.form}>
			{fields.map((field) => (
				<FormField
					key={field.name}
					name={field.name}
					type={field.type}
					value={data[field.name]}
					onChange={handleFieldChange}
					required={field.required}
					// Other props
				/>
			))}
		</div>
	)
}
```

## Navigation Components

### AdminSidebar

Renders the main navigation sidebar:

```tsx
export const AdminSidebar = () => {
	const Navigation = useContext(NavigationContext)
	const pinnedNav = Navigation.filter((nav) => nav.isPinned)
	const allNav = Navigation.filter((nav) => !nav.isPinned)

	return (
		<aside>
			<div className={styles.logo__banner}>
				<Icon variant="logo" type="cms" />
			</div>
			<div className={styles.content__manager}>
				{/* Navigation links */}
				<div className={styles.navigation}>
					<div className={styles.navigation__block}>
						<p className="collect__label">Pinned</p>
						<ul className={styles.navigation__routes}>
							{pinnedNav.map((nav) => (
								<AdminSidebarComponent key={nav.uid} {...nav} />
							))}
						</ul>
					</div>
					{/* Other navigation blocks */}
				</div>
			</div>
		</aside>
	)
}
```

## Authentication Components

### LoginPage

Handles user authentication:

```tsx
export default function LoginPage({ params: { lng } }: { params: { lng: string } }) {
	// Login form state and handlers

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault()
		// Validation

		try {
			// Authentication API call
			const response = await postJsonFormData({
				fullPath: `${process.env.NEXT_PUBLIC_STRAPI_HOST}/admin/login`,
				body: JSON.stringify({ email, password }),
			})

			// Token storage
			setCookie("adminJwt", response.data.token, rememberMe ? 30 : 1)

			// Redirect
			window.location.href = redirectPath
		} catch (error) {
			// Error handling
		}
	}

	return <form onSubmit={handleLogin}>{/* Login form fields */}</form>
}
```

### UserMenu

Displays user information and logout option:

```tsx
export const UserMenu = () => {
	const [data, setData] = useState<UserProps | null>(null)
	const [isMenuOpen, setIsMenuOpen] = useState(false)

	// Load user data from localStorage

	const handleSignOut = () => {
		localStorage.removeItem("adminJwt")
		sessionStorage.removeItem("adminJwt")
		setCookie("adminJwt", "", 0)
		localStorage.removeItem("adminUser")
		setData(null)
		window.location.href = "/login"
	}

	return <div className={styles.user_menu}>{/* User button and dropdown menu */}</div>
}
```

## Component Communication

Components communicate through:

1. **Props**: Direct parent-child communication
2. **Context**: Shared state across component trees
3. **Custom Hooks**: Encapsulated logic and state

Example of context usage:

```tsx
// In a component
const context = useContext(PageBuilderContext)
const { data, setData } = context

// Update data
const handleChange = (newData) => {
	setData({
		...data,
		...newData,
	})
}
```

## Component Styling

Components use SCSS modules for styling:

```tsx
import styles from "./component.module.scss"

export const Component = () => {
	return <div className={styles.wrapper}>{/* Component content */}</div>
}
```

This approach provides:

- Scoped styles to prevent conflicts
- Better organization of style code
- Type safety with TypeScript integration
