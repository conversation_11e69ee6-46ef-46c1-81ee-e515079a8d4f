import acceptLanguage from 'accept-language'
import type { NextRequest } from 'next/server'
import { i18nRouter } from 'next-i18n-router'
import { fallbackLng, languages } from '../settings'

acceptLanguage.languages(languages)
// eslint-disable-next-line @typescript-eslint/naming-convention
const PUBLIC_FILE = /\.(.*)$/

const i18nConfig = {
	locales: languages,
	defaultLocale: fallbackLng,
}

export function middleware(request: NextRequest) {
	if (
		request.nextUrl.pathname.startsWith('/_next') ||
		request.nextUrl.pathname.includes('/api/') ||
		PUBLIC_FILE.test(request.nextUrl.pathname)
	) {
		return
	}

	return i18nRouter(request, i18nConfig)
}

// applies this middleware only to files in the app directory
export const config = {
	matcher: ['/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js).*)'],
}
