import cn from 'classnames'
import React, { useContext } from 'react'
import styles from '../../Input/input.module.scss'
import { type OptionData, type OptionProps, SelectContext } from '../Select'
import styleSelect from '../select.module.scss'

type IconProps =
	| { startIcon: React.ReactElement; endIcon?: never }
	| { endIcon: React.ReactElement; startIcon?: never }
	| { endIcon?: undefined; startIcon?: undefined }

type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
	label: string
	placeholder: string
	required: boolean
} & IconProps

export const Multiple = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ id, className, startIcon, endIcon, label, placeholder, required }, ref) => {
		const { setIsShow, isShow } = useContext(SelectContext)
		return (
			<div
				className={cn(
					'form__wrapper',
					styles['form-control-wrapper'],
					className,
					startIcon && styles['form-control-wrapper--icon-left'],
					endIcon && styles['form-control-wrapper--icon-right'],
					isShow && 'is__triggered'
				)}
			>
				{label && (
					<span className={styles.label}>
						{label}
						{required && <span className={styles.mark}>*</span>}
					</span>
				)}
				<div className={styles.wrapper}>
					{startIcon && (
						<span className={cn(styles.icon, styles['icon--left'], 'icon--left')}>{startIcon}</span>
					)}

					<button
						id={id}
						type="button"
						className={cn(styles['form-control'])}
						onClick={() => setIsShow(!isShow)}
						ref={ref}
					>
						<span className="form-label">{placeholder}</span>
					</button>

					{endIcon && (
						<span className={cn(styles.icon, styles['icon--right'], 'icon--right')}>{endIcon}</span>
					)}
				</div>
			</div>
		)
	}
)

export function MultipleItem({
	value,
	children,
	className,
}: {
	value: string
	children?: React.ReactNode | ((data: OptionData) => React.ReactNode)
	className?: string
}) {
	const { option, setOption, onChange } = useContext(SelectContext)
	const defaultOption: OptionProps[] = !Array.isArray(option) ? [] : option

	const matchOption = (valueMatch: string) =>
		defaultOption.some((item) => item.value.toString() === valueMatch)

	const handleChange = (optionValue: string, optionLabel: string) => {
		let updateValue
		if (matchOption(optionValue)) {
			updateValue = defaultOption.filter((item) => item.value.toString() !== optionValue)
			setOption(updateValue)
		} else {
			updateValue = [
				...defaultOption,
				{
					value: optionValue,
					label: optionLabel,
				},
			]
			setOption(updateValue)
		}

		onChange?.(updateValue)
	}

	const optionData = {
		isActive: matchOption(value),
	}

	const RenderChildren = () => (typeof children === 'function' ? children(optionData) : children)

	return (
		<div
			role="button"
			tabIndex={0}
			className={cn(
				styleSelect.option,
				matchOption(value) && styleSelect.active,
				matchOption(value) && 'option__active',
				className
			)}
			onClick={() => handleChange(value, children as string)}
		>
			<RenderChildren />
		</div>
	)
}
