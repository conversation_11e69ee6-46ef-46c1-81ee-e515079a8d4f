@import '@/styles/config';

.wrapper {
	margin-left: spacing(s1);
	margin-right: spacing(s1);
	padding: spacing(s1);
	background-color: color('grey', 15);
	border-radius: spacing(s2);
	overflow: hidden;

	.inner {
		position: relative;
		overflow: hidden;
		display: flex;
		align-items: center;
		border-radius: spacing(s1);
	}

	.bg {
		position: absolute;
		top: 0;
		height: 100%;
		background-color: color('white', 0);
		border-radius: spacing(s1);
		transition: 0.2s;

		&.preview {
			left: 0;
			right: 50%;
		}

		&.edit {
			right: 0;
			left: 50%;
		}
	}

	button {
		position: relative;
		z-index: 1;
		height: 100%;
		padding: spacing(s3);
		color: color('grey', 20);
		transition: 0.4s;

		&.active {
			color: inherit;
		}

		&:hover {
			background-color: unset;
		}

		.chevron {
			position: absolute;
			bottom: spacing(s1);
			left: 50%;
			transform: translateX(-50%);
			--icon-size: #{spacing(s3)};
		}
	}
}

.device__list {
	position: fixed;
	z-index: 9;
	left: 0;
	top: 0;
	background-color: color('white', 0);
	display: grid;
	overflow: hidden;
	border-radius: 0 0 spacing(s1) spacing(s1);
	border-top: 1px solid color('grey', 15);

	button {
		height: spacing(s9);
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: 0.2s;

		&:hover,
		&.active {
			background-color: color('grey', 15);
		}
	}
}
