@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);

	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		// padding: 0;
		// padding-top: spacing(s12);
		// padding-bottom: spacing(s6);

		@include min-width('2md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}

	:global(.unwrap__wrapper) {
		gap: px-to(48px, rem);
	}

	.block {
		padding: px-to(18px, rem) spacing(s6) spacing(s6);
		background-color: color('white', 100);
		display: flex;
		flex-direction: column;
		gap: px-to(6px, rem);
		border-radius: spacing(s4);
		grid-column: span 4; // 1 row 3 col

		&:nth-last-child(1):nth-child(3n + 1) {
			grid-column: span 12; // If have 1 col extra
		}

		&:nth-last-child(2):nth-child(3n + 1),
		&:nth-last-child(1):nth-child(3n + 2) {
			grid-column: span 6; // If have 2 cols extras
		}

		&__title {
			@include fluid($font-size) {
				font-size: size('paragraph', 'lg');
			}
			font-weight: 500;
			color: color('black', 100);
			line-height: 1.5;
		}
		&__content {
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
			color: color('neutral-gray');
			line-height: 1.5;

			ul {
				padding-left: spacing(s6);
				list-style-type: disc;
			}
			i,
			em {
				font-style: italic;
			}
			b,
			strong {
				font-weight: 700;
			}
			a,
			u {
				text-decoration: underline;
				text-decoration-skip-ink: none;
			}
			del {
				text-decoration: line-through;
			}
		}
	}

	:global(.unwrap) {
		padding: 0;
		background-color: transparent;
	}
}
