{
	"$schema": "https://turbo.build/schema.json",
	"globalDependencies": ["**/.env.*local", "**/tsconfig*.json"],
	"globalPassThroughEnv": ["AWS_SECRET_KEY", "GITHUB_TOKEN"],
	"tasks": {
		"codegen": {
			// Codegen caching might also be disabled and enabled by workspace (see packages/db-main-prisma/turbo.json)
			// as generated code location might vary between toolings
			"cache": true,
			"outputs": ["src/generated/**"]
		},
		"build": {
			"outputs": ["dist/**"],
			"dependsOn": ["^build"]
		},
		"build-cloudflare": {
			"outputs": [".vercel/**"],
			"dependsOn": ["^build"]
		},
		"build-force": {
			"outputs": ["dist/**"]
		},
		"test": {},
		"test-unit": {},
		"lint": {
			"dependsOn": ["@collective/core#build"],
			"env": ["TIMING"]
		},
		"fix-all-files": {},
		"lint-styles": {},
		"typecheck": {},
		"build-storybook": {},
		"clean": {
			"cache": false
		},
		"check-dist": {
			"dependsOn": ["^build"]
		},
		"check-size": {
			"dependsOn": ["^build"]
		}
	}
}
