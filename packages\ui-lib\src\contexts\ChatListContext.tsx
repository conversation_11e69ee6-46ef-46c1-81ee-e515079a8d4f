'use client'

import { getCustomPathData } from '@collective/integration-lib/cms'
import { createContext, useState, type Dispatch, type SetStateAction } from 'react'

type ChatProps = {
	id: string
	uuid: string
	updatedAt: string
	messages: {
		content: string
		role: string
		time: string
	}[]
}
const defaultContext: {
	chatList: ChatProps[]
	setChatList: Dispatch<SetStateAction<ChatProps[]>>
	getNewChatList: () => Promise<void>
} = {
	chatList: [],
	// eslint-disable-next-line prettier/prettier, @typescript-eslint/no-empty-function
	setChatList: () => {},
	// eslint-disable-next-line prettier/prettier, @typescript-eslint/no-empty-function
	getNewChatList: async () => {},
}

export const ChatListContext = createContext(defaultContext)

export default function ChatListProvider({ children }: { children: React.ReactNode }) {
	const [chatList, setChatList] = useState<ChatProps[]>([])
	const getNewChatList = async () => {
		const response = await getCustomPathData(`/api/getChat`)
		if (response.chats.length > 0) {
			setChatList(response.chats)
		}
	}
	return (
		<ChatListContext.Provider value={{ chatList, setChatList, getNewChatList }}>
			{children}
		</ChatListContext.Provider>
	)
}
