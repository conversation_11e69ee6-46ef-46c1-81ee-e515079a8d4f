import * as jose from 'jose';
import { getCookie, setCookie } from './cookies';
export async function generateKeyPair() {
    const { publicKey, privateKey } = await jose.generateKeyPair('ES256', { extractable: true });
    return { publicKey, privateKey };
}
export async function exportJWK(keyJWK) {
    const key = await jose.exportJWK(keyJWK);
    return key;
}
const publicKeyJWK = {
    kty: 'EC',
    x: 'EY9lKsuie8a2G-9S3R6arvFOxW12DXkqihffgr2AMp4',
    y: 'BZhaUk4HBCVeJ2Lf2Uu4jhlirErcJbNSF1DMQ5CaetQ',
    crv: 'P-256',
};
export async function verifyJWT(jws) {
    const publicKey = await jose.importJWK(publicKeyJWK, 'ES256');
    try {
        const { payload, protectedHeader } = await jose.compactVerify(jws, publicKey);
        return { payload: new TextDecoder().decode(payload), protectedHeader };
    }
    catch (error) {
        return null;
    }
}
export async function checkValidJWT(jws) {
    const data = await verifyJWT(jws);
    if (data === null) {
        return null;
    }
    const payload = JSON.parse(data.payload);
    if (payload.exp < new Date().getTime()) {
        return null;
    }
    if (payload.nbf > new Date().getTime()) {
        return null;
    }
    return payload;
}
export async function checkIsLogin(token) {
    if (token === null) {
        return null;
    }
    const payload = await checkValidJWT(token);
    if (payload === null) {
        return null;
    }
    return payload;
}
export async function checkIsLoginClient() {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token') || getCookie('token');
    const isLogin = await checkIsLogin(token);
    if (isLogin === null) {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        setCookie('token', '', 0);
    }
    return isLogin;
}
