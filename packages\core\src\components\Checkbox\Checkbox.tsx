import cn from 'classnames'
import React, { useId } from 'react'
import styles from './checkbox.module.scss'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	label?: string
	type?: 'checkbox' | 'radio'
	alignCenter?: boolean
	indeterminate?: boolean
}

export const Checkbox = React.forwardRef<HTMLInputElement, InputProps>(
	(
		{
			// eslint-disable-next-line react-hooks/rules-of-hooks
			id = useId(),
			label,
			type = 'checkbox',
			className,
			alignCenter,
			indeterminate,
			...rest
		},
		ref
	) => (
		<div
			className={cn(
				styles.wrapper,
				styles[type],
				className,
				alignCenter && styles.alignCenter,
				indeterminate && 'indeterminate'
			)}
		>
			<input ref={ref} className={styles.input} type={type} id={id} hidden {...rest} />
			<label htmlFor={id}>{label}</label>
		</div>
	)
)

Checkbox.displayName = 'Checkbox'
