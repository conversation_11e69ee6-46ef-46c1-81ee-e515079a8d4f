import { type Dispatch, type SetStateAction } from 'react';
export type OptionData = {
    isActive?: boolean;
};
export type OptionProps = {
    value: string | number;
    label: React.ReactNode;
};
declare const DetectMode: {
    single: string;
    multiple: string;
};
type SelectProps = {
    mode?: keyof typeof DetectMode;
    id?: string;
    label?: string;
    dropdownClass?: string;
    className?: string;
    placeholder?: string;
    startIcon?: React.ReactElement;
    endIcon?: React.ReactElement;
    children?: React.ReactNode;
    defaultOption?: OptionProps | OptionProps[];
    onChange?: (option: OptionProps | OptionProps[]) => void;
    required?: boolean;
};
type DefaultContextProps = {
    mode: keyof typeof DetectMode;
    option: OptionProps | OptionProps[];
    setOption: Dispatch<SetStateAction<OptionProps | OptionProps[]>>;
    setIsShow: Dispatch<SetStateAction<boolean>>;
    isShow?: boolean;
    onChange?: (option: OptionProps | OptionProps[]) => void;
};
export declare const SelectContext: import("react").Context<DefaultContextProps>;
export declare function Select({ id, label, mode, placeholder, dropdownClass, className, startIcon, endIcon, children, defaultOption, onChange, required, }: SelectProps): import("react/jsx-runtime").JSX.Element;
export declare function SelectItem({ value, children, className, }: {
    value: string | number;
    children: React.ReactNode | ((data: OptionData) => React.ReactNode);
    className?: string;
}): import("react/jsx-runtime").JSX.Element;
export {};
