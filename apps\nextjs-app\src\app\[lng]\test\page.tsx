'use client'

import { Wrapper } from '@collective/ui-lib/src/base/Wrapper'
// import Image from 'next/image'
import '@collective/ui-lib/styles/test.scss'
import { QuickNav } from '@/components/QuickNav/QuickNav'
import { InitialData as MockData } from '@/mock/Logo'
import styles from '../cheatsheet/page.module.scss'

// export const runtime = 'edge'
export default function Home({
	params: { lng },
}: Readonly<{
	params: { lng: string }
}>) {
	return (
		<div className={styles.wrapper}>
			{MockData.Components.map((component, index) => (
				<Wrapper key={index} commonData={{ locales: lng }} data={component} />
			))}
			<QuickNav
				currentCategory={{
					Headline: 'Logo',
					slug: 'logo',
				}}
				// currentPage={ }
				lng={lng}
			/>
		</div>
	)
}
