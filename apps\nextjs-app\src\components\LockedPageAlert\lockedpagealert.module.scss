@use '@collective/ui-lib/styles/config' as *;

.locked {
	&__page {
		display: grid;
		min-height: calc(100vh - 200px);
		padding: px-to(48px, rem);
	}
	&__wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: spacing(s2);
		color: color('neutral-gray');
	}
	&__icon {
		--size: #{px-to(64px, rem)};
	}
	&__desc {
		font-weight: 500;
		@include fluid($font-size) {
			font-size: size('paragraph', 'lg');
		}
	}
}
button.link {
	padding: 0;
	&:hover {
		color: color('black', 100);
		text-decoration: underline;
	}
}
