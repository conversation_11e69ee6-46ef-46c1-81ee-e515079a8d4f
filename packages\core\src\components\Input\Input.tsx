import cn from 'classnames'
import React from 'react'
import styles from './input.module.scss'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	label?: string
	className?: string
	placeholder?: string
	startIcon?: React.ReactElement | null
	endIcon?: React.ReactElement | null
}
interface TextAreaProps extends React.InputHTMLAttributes<HTMLTextAreaElement> {
	label?: string
	className?: string
	placeholder?: string
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ type = 'text', label, placeholder, className, startIcon, endIcon, ...rest }, ref) => (
		<div
			className={cn(
				styles['form-control-wrapper'],
				className,
				startIcon && styles['form-control-wrapper--icon-left'],
				endIcon && styles['form-control-wrapper--icon-right']
			)}
		>
			{label && (
				<span className={styles.label}>
					{label}
					{rest.required && <span className={styles.mark}>*</span>}
				</span>
			)}
			<div className={styles.wrapper}>
				{startIcon && (
					<span className={cn(styles.icon, styles['icon--left'], 'icon--left')}>{startIcon}</span>
				)}

				<input
					ref={ref}
					className={styles['form-control']}
					type={type}
					placeholder={placeholder}
					{...rest}
				/>

				{endIcon && (
					<span className={cn(styles.icon, styles['icon--right'], 'icon--right')}>{endIcon}</span>
				)}
			</div>
		</div>
	)
)
Input.displayName = 'Input'

export const TextArea = React.forwardRef<HTMLTextAreaElement, TextAreaProps>(
	({ placeholder, label, className, maxLength, ...rest }, ref) => (
		<div className={cn(styles['form-control-wrapper'], className)}>
			{label && (
				<span className={styles.label}>
					{label}
					{rest.required && <span className={styles.mark}>*</span>}
				</span>
			)}

			<textarea ref={ref} className={styles['form-control']} placeholder={placeholder} {...rest} />
		</div>
	)
)

TextArea.displayName = 'TextArea'
