'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import React, { useRef, useState } from 'react';
import { useAnim, useIsomorphicLayoutEffect, useWindowDimensions } from '../../hooks';
import styles from './accordion.module.scss';
export const Accordion = ({ children }) => {
    return _jsx("div", { className: cn(styles.accordion, 'accordion'), children: children });
};
export const AccordionItem = ({ title, children, onActive, isActive = false, isDisabled = false, icon, }) => {
    const [active, setActive] = useState(isActive);
    const [contentHeight, setContentHeight] = useState(0);
    const contentRef = useRef(null);
    const windowDimensions = useWindowDimensions();
    const { current: blockProps } = useRef({
        config: {
            paused: true,
        },
        timeline: [
            {
                targets: '.accordion__content',
                vars: {
                    fromTo: [
                        { height: 0 },
                        {
                            height: 'auto',
                            duration: 0.5,
                            ease: 'power3.inOut',
                        },
                    ],
                },
            },
        ],
    });
    const [block, tl] = useAnim({ ...blockProps, deps: [contentHeight] });
    useIsomorphicLayoutEffect(() => {
        if (!contentRef.current)
            return;
        const innerContent = contentRef.current.firstElementChild;
        if (contentHeight !== innerContent.clientHeight)
            setContentHeight(innerContent.clientHeight);
    }, [windowDimensions]);
    useIsomorphicLayoutEffect(() => {
        active ? tl.play() : tl.reverse();
    }, [active, tl]);
    useIsomorphicLayoutEffect(() => {
        setActive(isActive);
    }, [isActive]);
    const handleActive = () => {
        if (isDisabled)
            return;
        setActive(!active);
        onActive?.();
    };
    return (_jsxs("div", { className: cn(styles.accordion__item, 'accordion__item'), ref: contentRef, children: [_jsxs("button", { className: cn(styles.trigger, 'accordion__trigger', active && 'is__active'), onClick: handleActive, children: [_jsx("h4", { className: "accordion__title", children: title }), icon ? (icon) : (_jsxs("svg", { x: "0px", y: "0px", viewBox: "0 0 14 14", className: "plus__icon svg__icon accordion__icon", children: [_jsx("rect", { className: "plus__icon-horizontal", y: "6", width: "14", height: "2", fill: "currentColor" }), _jsx("rect", { className: "plus__icon-vertical", x: "6", width: "2", height: "14", fill: "currentColor" })] }))] }), _jsx("div", { className: "accordion__content", style: { height: 0, overflow: 'hidden', willChange: 'auto' }, ref: block, children: _jsx("div", { className: "content__inner", children: children }) })] }));
};
