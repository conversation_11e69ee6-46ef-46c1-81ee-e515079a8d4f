import cn from 'classnames'
import Markdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import styles from './texthorizon.module.scss'

type TextHorizonProps = {
	cid?: string
	Headline?: string
	BlockQuote?: string
	Paragraph?: string
}

type CommonDataProps = {
	isFirstSection?: boolean
}

type Props = TextHorizonProps & CommonDataProps

export const TextHorizon = ({ cid, Headline, BlockQuote, Paragraph, isFirstSection }: Props) => (
	<section
		id={cid}
		className={cn(styles.wrapper, Headline ? '' : 'nohead', isFirstSection ? 'first__section' : '')}
	>
		<div className={cn('aidigi__grid')}>
			{Headline && <h2 className={cn(styles.headline, 'aidigi__heading')}>{Headline}</h2>}
			<div className={cn(styles.content)}>
				{BlockQuote && (
					<div className={cn(styles.blockquote, 'aidigi__paragraph--lg')}>
						<Markdown rehypePlugins={[rehypeRaw]}>{BlockQuote}</Markdown>
					</div>
				)}
				{Paragraph && (
					<div className={cn(styles.paragraph, 'aidigi__paragraph--md')}>
						<Markdown rehypePlugins={[rehypeRaw]}>{Paragraph}</Markdown>
					</div>
				)}
			</div>
		</div>
	</section>
)
