# Collect CMS Navigation Structure

This document explains the navigation system in the Collect CMS application, detailing how navigation is structured, rendered, and used throughout the application.

## Table of Contents

1. [Overview](#overview)
2. [Navigation Data Structure](#navigation-data-structure)
3. [Navigation Context](#navigation-context)
4. [Sidebar Navigation](#sidebar-navigation)
5. [Content Type Navigation](#content-type-navigation)
6. [Route Resolution](#route-resolution)
7. [Navigation State Management](#navigation-state-management)

## Overview

The Collect CMS navigation system is built around a hierarchical structure that represents the content types and groups available in the CMS. This structure is used to generate the sidebar navigation and determine the available routes for content management.

The navigation system consists of:

- A data structure defining the navigation hierarchy
- Context providers for sharing navigation data
- UI components for rendering navigation elements
- Logic for resolving routes based on navigation data

## Navigation Data Structure

The navigation data is defined in `src/mock/Navigation.ts` and follows this structure:

```typescript
export const NavigationData: INavigationProps[] = [
	{
		apiId: "group-id", // Identifier used in URLs
		uid: "group-uid", // Unique identifier for the group
		isDisplayed: true, // Whether to show in the UI
		isPinned: true / false, // Whether to pin in the sidebar
		info: {
			displayName: "Group Name", // Display name in the UI
			description: "Description", // Optional description
		},
		kind: "group", // Type of navigation item (always 'group' at top level)
		layouts: [
			// Child items within this group
			{
				name: "Content Type Name", // Display name
				apiId: "content-type-id", // Identifier used in URLs
				uid: "content-type-uid", // Unique identifier
				visible: true, // Whether to show in the UI
				kind: "singleType" / "collectionType", // Type of content
				identifierField: "slug", // Field used as identifier
			},
			// More content types...
		],
	},
	// More groups...
]
```

This structure represents:

1. **Groups**: Top-level categories that organize content types (e.g., "Pages", "Media", "Settings")
2. **Layouts**: Content types within each group, which can be either:
   - **Single Types**: Content with only one instance (e.g., "Home Page", "About Page")
   - **Collection Types**: Content with multiple instances (e.g., "Articles", "Products")

## Navigation Context

The navigation data is made available throughout the application using React Context:

```typescript
// src/contexts/NavigationContext.tsx
export const NavigationContext = createContext(defaultContext)

export default function NavigationProvider({
  data,
  children,
}: {
  data: INavigationProps[]
  children: React.ReactNode
}) {
  return <NavigationContext.Provider value={data}>{children}</NavigationContext.Provider>
}
```

This context is provided at the root layout level:

```typescript
// src/app/[lng]/layout.tsx
export default async function RootLayout({
  children,
  params: { lng },
}: Readonly<{ children: React.ReactNode; params: { lng: string } }>) {
  return (
    <html lang={lng} dir={dir(lng)}>
      <body className={inter.className}>
        <NavigationProvider data={NavigationData}>{children}</NavigationProvider>
      </body>
    </html>
  )
}
```

Components can then access the navigation data using the `useContext` hook:

```typescript
const Navigation = useContext(NavigationContext)
```

## Sidebar Navigation

The sidebar navigation is rendered by the `AdminSidebar` component:

```typescript
// src/layouts/admin/AdminSidebar.tsx
export const AdminSidebar = () => {
  const Navigation = useContext(NavigationContext)
  const pinnedNav = Navigation.filter((nav) => nav.isPinned)
  const allNav = Navigation.filter((nav) => !nav.isPinned)

  return (
    <aside>
      {/* Logo and header */}
      <div className={styles.navigation}>
        {/* Pinned navigation items */}
        <div className={styles.navigation__block}>
          <p className="collect__label">Pinned</p>
          <ul className={styles.navigation__routes}>
            {pinnedNav.map((nav, index) => (
              <AdminSidebarComponent key={index} {...nav} />
            ))}
          </ul>
        </div>

        {/* All other navigation items */}
        <div className={styles.navigation__block}>
          <p className="collect__label">Content Manager</p>
          <ul className={styles.navigation__routes}>
            {allNav.map((nav, index) => (
              <AdminSidebarComponent key={index} {...nav} />
            ))}
          </ul>
        </div>
      </div>
    </aside>
  )
}
```

Each navigation item is rendered by the `AdminSidebarComponent`:

```typescript
const AdminSidebarComponent = (nav: INavigationProps) => {
  const { apiId, uid, info, layouts } = nav
  const isShowmore =
    (layouts.length === 1 && layouts[0]?.kind === 'collectionType') || layouts.length > 1

  return (
    <li key={uid}>
      <button className={styles.pin}>
        <Icon variant="pin" type="cms" />
      </button>
      <Link href={`/content-manager/${apiId}`} className="text-w-icon">
        <Icon variant={isShowmore ? 'box' : 'file'} type="cms" /> {info.displayName}
        {isShowmore && <Icon className={styles.chevron} variant="chevron-right" type="cms" />}
      </Link>
    </li>
  )
}
```

This creates a hierarchical navigation menu with:

- Pinned items at the top for quick access
- Regular items grouped under "Content Manager"
- Visual indicators for expandable groups
- Links to the appropriate content management pages

## Content Type Navigation

When navigating to a content type, the application shows a tabbed interface for the different content types within a group. This is handled by the `content-manager/[...slug]/page.tsx` component:

```typescript
export default async function Home({
  params: { lng, slug },
}: Readonly<{ params: { lng: string; slug: string[] } }>) {
  // Find the current navigation group
  const currentNav = NavigationData.find((nav) => nav.apiId === slug[0])
  if (!currentNav) {
    notFound()
  }

  // Get the active content type
  const typeUid = slug[1]
  const activeChild = currentNav.layouts.findIndex((layout) => layout.apiId === typeUid)

  // Extract tab data from the layouts
  const { layouts } = currentNav
  const TabData: {
    apiId: string
    uid: string
    name: string
    kind: INavigationKind
  }[] = []

  for (const layout of layouts) {
    TabData.push({
      apiId: layout.apiId,
      uid: layout.uid,
      kind: layout.kind,
      name: layout.name,
    })
  }

  // Redirect if no specific content type is selected
  if (slug.length < 2) {
    redirect(`/content-manager/${slug[0]}/${currentNav.layouts?.[0]?.apiId}`)
  }

  // Find the current content type
  const currentType = currentNav.layouts.find((layout) => layout.apiId === typeUid)
  if (!currentType) {
    notFound()
  }

  // Fetch necessary data
  const currentUid = currentType.uid
  const [globalInit, components, contentType, configuration] = await Promise.all([
    getGlobalInit({ authToken }),
    getComponents(),
    getContentType(`api::${currentUid}.${currentUid}`),
    getConfiguration({ uid: currentUid, authToken }),
  ])

  // Render the content manager with tabs
  return (
    <div className="content__manager">
      {/* Header */}
      <div className="page__header">
        {/* ... */}
      </div>

      {/* Content builder layout with tabs */}
      <ContentBuilderlayout
        lng={lng}
        value={{
          components,
          globals: globalInit,
          configuration,
          data: { data: { components: [] } as unknown as IResultDataProps },
          contentType,
          locale: lng,
          slug: slug,
        }}
        data={TabData}
        activeTabIndex={activeChild}
      />
    </div>
  )
}
```

This creates a tabbed interface where:

1. The top-level navigation selects a group
2. The tabs show the content types within that group
3. The content area displays the appropriate editor for the selected content type

## Route Resolution

The application resolves routes based on the navigation structure using a multi-step process:

1. **URL Structure**: `/[lng]/content-manager/[group]/[type]/[id]?`

   - `[lng]`: Language code
   - `[group]`: Navigation group apiId
   - `[type]`: Content type apiId
   - `[id]` (optional): Specific content item identifier

2. **Route Resolution in Content Manager**:

   ```typescript
   // Find the current navigation group
   const currentNav = NavigationData.find((nav) => nav.apiId === slug[0])

   // Find the current content type
   const currentType = currentNav.layouts.find((layout) => layout.apiId === typeUid)
   ```

3. **Default Redirects**:

   ```typescript
   // Redirect to first content type if none specified
   if (slug.length < 2) {
   	redirect(`/content-manager/${slug[0]}/${currentNav.layouts?.[0]?.apiId}`)
   }
   ```

4. **Not Found Handling**:

   ```typescript
   // If navigation group not found
   if (!currentNav) {
   	notFound()
   }

   // If content type not found
   if (!currentType) {
   	notFound()
   }
   ```

This ensures that URLs are properly resolved to the correct content and invalid URLs are handled appropriately.

## Navigation State Management

The navigation system includes several state management features:

1. **Pinned Items**:

   ```typescript
   const pinnedNav = Navigation.filter((nav) => nav.isPinned)
   ```

   Items can be pinned for quick access and are displayed separately in the sidebar.

2. **Active Item Tracking**:

   ```typescript
   const activeChild = currentNav.layouts.findIndex((layout) => layout.apiId === typeUid)
   ```

   The application tracks the active content type to highlight it in the UI.

3. **Visibility Control**:

   ```typescript
   isDisplayed: true, // Whether to show in the UI
   visible: true,     // Whether to show in the UI
   ```

   Items can be hidden from the UI while still being accessible programmatically.

4. **Dynamic Navigation**:
   While the current implementation uses mock data, the structure supports fetching navigation data from an API:

   ```typescript
   // Example of how it could be implemented
   const [navigationData, setNavigationData] = useState([])

   useEffect(() => {
   	async function fetchNavigation() {
   		const data = await fetchNavigationFromAPI()
   		setNavigationData(data)
   	}
   	fetchNavigation()
   }, [])
   ```

This flexible approach allows the navigation system to adapt to different content structures and user preferences.
