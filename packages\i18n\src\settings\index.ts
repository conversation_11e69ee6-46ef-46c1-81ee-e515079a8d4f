/* eslint-disable @typescript-eslint/naming-convention */
export const fallbackLng = 'en'
export const languages = [fallbackLng]
export const defaultNS = 'common'

export function getOptions(lng = fallbackLng, ns: string | string[] = defaultNS) {
	return {
		// debug: true,
		supportedLngs: languages,
		preload: languages,
		fallbackLng,
		lng,
		fallbackNS: defaultNS,
		defaultNS: Array.isArray(ns) ? ns[0] : ns,
		ns,
	}
}
