'use client'

import { setCookie } from '@collective/core'
import { usePathname, useRouter } from 'next/navigation'
import { languages } from '../settings'

export function useLanguageChanger() {
	const path = usePathname()
	const router = useRouter()
	return (lng: string) => {
		setCookie('NEXT_LOCALE', lng, 365)
		if (languages.some((v) => path.split('/')[1] === v)) {
			// Remove the language prefix from the path
			// eg: "/en", "/vi"
			// If path is index page, redirect to the root path
			const destPath = path.length > 3 ? path.slice(3) : '/'
			router.push(`${destPath}?lng=${lng}`)
		} else {
			router.push(`${path}?lng=${lng}`)
		}
	}
}
