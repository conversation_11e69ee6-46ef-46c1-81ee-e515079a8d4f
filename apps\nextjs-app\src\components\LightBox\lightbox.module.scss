@use '@collective/ui-lib/styles/config' as *;

// Lightbox nền tối mờ
.lightboxOverlay {
	position: fixed;
	inset: 0;
	background: color('black', 80);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
}

// N<PERSON>t đóng (X)
.closeButton {
	position: absolute;
	top: spacing(s5);
	right: spacing(s5);
	--size: #{spacing(s8)};
	color: white;
	background: none;
	border: none;
	cursor: pointer;
}

// Nút prev/next
.navButton {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	--size: #{spacing(s6)};
	color: color('white', 100);
	background: none;
	border: none;
	border-radius: spacing(s2);
	padding: spacing(s1);
	cursor: pointer;
	&:hover {
		color: color('black', 100);
		background-color: color('white', 100);
	}
}

.prevButton {
	left: spacing(s5);
}

.nextButton {
	right: spacing(s5);
}

// Ảnh trong Lightbox
.lightboxWrapper {
	transform: scale(var(--zoom)) !important;
}
.lightboxImage {
	max-width: 90vw;
	max-height: 80vh;
	transition: transform 0.2s ease-in-out;
}

// Điều khiển zoom
.zoomControls {
	position: absolute;
	bottom: spacing(s5);
	display: flex;
	gap: spacing(s3);
}

.zoomButton {
	--size: #{spacing(s6)};
	color: color('white', 100);
	background: none;
	border: none;
	border-radius: spacing(s2);
	padding: spacing(s1);
	cursor: pointer;
	&:hover {
		color: color('black', 100);
		background-color: color('white', 100);
	}
}
