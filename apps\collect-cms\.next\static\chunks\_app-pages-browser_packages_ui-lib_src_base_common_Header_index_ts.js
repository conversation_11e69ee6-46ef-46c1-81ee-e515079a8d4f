/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_Header_index_ts"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/header.module.scss":
/*!***********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/header.module.scss ***!
  \***********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"header_wrapper__o2Cif\",\"headline\":\"header_headline__T2YEt\",\"download\":\"header_download__bfE6Q\"};\n    if(true) {\n      // 1747630336554\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"d5a34ef2a369\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9oZWFkZXIubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0I7QUFDbEIsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1KLGNBQWMsc0RBQXNEO0FBQ3JQLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vSGVhZGVyL2hlYWRlci5tb2R1bGUuc2Nzcz8yYmZlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJ3cmFwcGVyXCI6XCJoZWFkZXJfd3JhcHBlcl9fbzJDaWZcIixcImhlYWRsaW5lXCI6XCJoZWFkZXJfaGVhZGxpbmVfX1QyWUV0XCIsXCJkb3dubG9hZFwiOlwiaGVhZGVyX2Rvd25sb2FkX19iZkU2UVwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ3NjMwMzM2NTU0XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkQ6L0NEQS9yZXBvcy9icmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlL2FwcHMvY29sbGVjdC1jbXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gIFxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZDVhMzRlZjJhMzY5XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/header.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/Header.tsx":
/*!***************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/Header.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/header.module.scss\");\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_header_module_scss__WEBPACK_IMPORTED_MODULE_2__);\nvar _this = undefined;\n\n\n\n\nvar Header = function(param) {\n    var cid = param.cid, Headline = param.Headline, Subhead = param.Subhead, ActionButton = param.ActionButton;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            style: {\n                alignItems: !Subhead ? \"center\" : \"\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline),\n                    children: [\n                        Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h1\"),\n                            children: Headline\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 18\n                        }, _this),\n                        Subhead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h3\"),\n                            children: Subhead\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 17\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 4\n                }, _this),\n                (ActionButton === null || ActionButton === void 0 ? void 0 : ActionButton.ButtonText) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().download),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"aidigi__button outline\",\n                        href: ActionButton === null || ActionButton === void 0 ? void 0 : ActionButton.ButtonLink,\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().arrowdown)),\n                            variant: \"arrow-down\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 18\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"vc__paragraph--md\",\n                            children: ActionButton === null || ActionButton === void 0 ? void 0 : ActionButton.ButtonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 6\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, _this);\n};\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0M7QUFDcEI7QUFDYztBQVlsQyxJQUFNSSxTQUFTO1FBQUdDLFlBQUFBLEtBQUtDLGlCQUFBQSxVQUFVQyxnQkFBQUEsU0FBU0MscUJBQUFBO3lCQUNoRCw4REFBQ0M7UUFBUUMsSUFBSUw7UUFBS00sV0FBV1QsaURBQUVBLENBQUNDLG9FQUFjO2tCQUM3Qyw0RUFBQ1U7WUFBSUYsV0FBV1QsaURBQUVBLENBQUM7WUFBaUJZLE9BQU87Z0JBQUVDLFlBQVksQ0FBQ1IsVUFBVSxXQUFXO1lBQUc7OzhCQUNqRiw4REFBQ007b0JBQUlGLFdBQVdSLHFFQUFlOzt3QkFDN0JHLDBCQUFZLDhEQUFDVzs0QkFBR04sV0FBV1QsaURBQUVBLENBQUM7c0NBQXlCSTs7Ozs7O3dCQUN2REMseUJBQVcsOERBQUNXOzRCQUFHUCxXQUFXVCxpREFBRUEsQ0FBQztzQ0FBeUJLOzs7Ozs7Ozs7Ozs7Z0JBRXZEQyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNXLFVBQVUsbUJBQ3hCLDhEQUFDTjtvQkFBSUYsV0FBV1IscUVBQWU7OEJBQzlCLDRFQUFDSCxzRkFBTUE7d0JBQ05XLFdBQVU7d0JBQ1ZVLElBQUksRUFBRWIseUJBQUFBLG1DQUFBQSxhQUFjYyxVQUFVO3dCQUM5QkMseUJBQVcsOERBQUN0QixvRkFBSUE7NEJBQUNVLFdBQVdULGlEQUFFQSxDQUFDQyxzRUFBZ0I7NEJBQUdzQixTQUFROzs7Ozs7a0NBRTFELDRFQUFDQzs0QkFBRWYsV0FBVTtzQ0FBcUJILHlCQUFBQSxtQ0FBQUEsYUFBY1csVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0VBTS9EO0tBcEJZZiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9IZWFkZXIvSGVhZGVyLnRzeD9mMzQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJ1dHRvbiwgSWNvbiB9IGZyb20gJ0Bjb2xsZWN0aXZlL2NvcmUnXG5pbXBvcnQgY24gZnJvbSAnY2xhc3NuYW1lcydcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi9oZWFkZXIubW9kdWxlLnNjc3MnXG5cbnR5cGUgSGVhZGVyUHJvcHMgPSB7XG5cdGNpZD86IHN0cmluZ1xuXHRIZWFkbGluZTogc3RyaW5nXG5cdFN1YmhlYWQ/OiBzdHJpbmdcblx0QWN0aW9uQnV0dG9uOiB7XG5cdFx0QnV0dG9uVGV4dDogc3RyaW5nXG5cdFx0QnV0dG9uTGluazogc3RyaW5nXG5cdH1cbn1cblxuZXhwb3J0IGNvbnN0IEhlYWRlciA9ICh7IGNpZCwgSGVhZGxpbmUsIFN1YmhlYWQsIEFjdGlvbkJ1dHRvbiB9OiBIZWFkZXJQcm9wcykgPT4gKFxuXHQ8c2VjdGlvbiBpZD17Y2lkfSBjbGFzc05hbWU9e2NuKHN0eWxlcy53cmFwcGVyKX0+XG5cdFx0PGRpdiBjbGFzc05hbWU9e2NuKCdhaWRpZ2lfX2dyaWQnKX0gc3R5bGU9e3sgYWxpZ25JdGVtczogIVN1YmhlYWQgPyAnY2VudGVyJyA6ICcnIH19PlxuXHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5oZWFkbGluZX0+XG5cdFx0XHRcdHtIZWFkbGluZSAmJiA8aDEgY2xhc3NOYW1lPXtjbignYWlkaWdpX19oZWFkaW5nLS1oMScpfT57SGVhZGxpbmV9PC9oMT59XG5cdFx0XHRcdHtTdWJoZWFkICYmIDxoMyBjbGFzc05hbWU9e2NuKCdhaWRpZ2lfX2hlYWRpbmctLWgzJyl9PntTdWJoZWFkfTwvaDM+fVxuXHRcdFx0PC9kaXY+XG5cdFx0XHR7QWN0aW9uQnV0dG9uPy5CdXR0b25UZXh0ICYmIChcblx0XHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5kb3dubG9hZH0+XG5cdFx0XHRcdFx0PEJ1dHRvblxuXHRcdFx0XHRcdFx0Y2xhc3NOYW1lPVwiYWlkaWdpX19idXR0b24gb3V0bGluZVwiXG5cdFx0XHRcdFx0XHRocmVmPXtBY3Rpb25CdXR0b24/LkJ1dHRvbkxpbmt9XG5cdFx0XHRcdFx0XHRzdGFydEljb249ezxJY29uIGNsYXNzTmFtZT17Y24oc3R5bGVzLmFycm93ZG93bil9IHZhcmlhbnQ9XCJhcnJvdy1kb3duXCIgLz59XG5cdFx0XHRcdFx0PlxuXHRcdFx0XHRcdFx0PHAgY2xhc3NOYW1lPVwidmNfX3BhcmFncmFwaC0tbWRcIj57QWN0aW9uQnV0dG9uPy5CdXR0b25UZXh0fTwvcD5cblx0XHRcdFx0XHQ8L0J1dHRvbj5cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHQpfVxuXHRcdDwvZGl2PlxuXHQ8L3NlY3Rpb24+XG4pXG4iXSwibmFtZXMiOlsiQnV0dG9uIiwiSWNvbiIsImNuIiwic3R5bGVzIiwiSGVhZGVyIiwiY2lkIiwiSGVhZGxpbmUiLCJTdWJoZWFkIiwiQWN0aW9uQnV0dG9uIiwic2VjdGlvbiIsImlkIiwiY2xhc3NOYW1lIiwid3JhcHBlciIsImRpdiIsInN0eWxlIiwiYWxpZ25JdGVtcyIsImhlYWRsaW5lIiwiaDEiLCJoMyIsIkJ1dHRvblRleHQiLCJkb3dubG9hZCIsImhyZWYiLCJCdXR0b25MaW5rIiwic3RhcnRJY29uIiwiYXJyb3dkb3duIiwidmFyaWFudCIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/index.ts":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/index.ts ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_0__.Header; }\n/* harmony export */ });\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/Header.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9IZWFkZXIvaW5kZXgudHM/NzcyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL0hlYWRlcidcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/index.ts\n"));

/***/ })

}]);