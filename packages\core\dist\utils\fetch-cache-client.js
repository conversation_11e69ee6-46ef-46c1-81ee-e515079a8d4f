import { get, set } from 'idb-keyval';
import { fetcher } from '../common';
import { getRandomInt } from './get-random-int';
const updateCache = async (url, options, ttl) => {
    const data = await fetcher(url, options);
    // console.log('Cache update', data, Date.now())
    set(url, {
        data,
        ttl,
        createdAt: Date.now(),
    });
    return data;
};
export const cacheWrapper = async (url, options, ttl = 60, revalidateChance = 25) => {
    const cachedData = await get(url);
    if (cachedData) {
        if (Date.now() - cachedData.createdAt < cachedData.ttl * 1000) {
            if (getRandomInt(0, 100) < revalidateChance) {
                // Chance to update the cache even if the data is still valid
                // This is to prevent the cache from being stale for too long
                // and to ensure that we always have the latest data
                updateCache(url, options, ttl);
            }
            // console.info('Cache hit', url)
            return cachedData.data;
        }
        // console.info('Cache miss', cachedData, Date.now())
    }
    return updateCache(url, options, ttl);
};
