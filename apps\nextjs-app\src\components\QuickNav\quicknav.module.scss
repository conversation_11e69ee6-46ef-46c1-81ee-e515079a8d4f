@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	:global(.aidigi__grid) {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		// gap: spacing(s5);
	}

	a {
		display: flex;
		flex-direction: column;
		gap: spacing(s3);
		align-items: flex-start;
		padding: spacing(s12) 0 calc(8px + spacing(s10));
		border-top: 1px solid color('divider');

		&:last-child {
			align-items: flex-end;
			text-align: right;
		}

		&:hover h1::after {
			width: 100%;
		}
	}

	h1 {
		font-size: px-to(48px, rem);
		position: relative;
		color: color('black', 100);

		svg {
			--icon-size: #{px-to(44px, rem)};
			margin-right: spacing(s1);
			margin-bottom: spacing(s2);
		}

		&::after {
			content: '';
			position: absolute;
			left: 0;
			bottom: 0;
			height: px-to(2px, rem);
			width: 0;
			background-color: color('black', 100);
			transition: width 0.2s var(--ease-transition-2);
		}
	}

	h3 {
		font-weight: 400;
		text-transform: uppercase;
		color: color('neutral-gray');
		display: flex;
		gap: px-to(2px, rem);

		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}
	}

	.nav__list {
		line-height: 180%;
		/* 36px */
		text-decoration-line: underline;
		text-decoration-style: solid;
		text-decoration-skip-ink: none;
		text-decoration-thickness: auto;
		text-underline-offset: auto;
		text-underline-position: from-font;
		color: #686868;
		gap: spacing(s12);
	}

	.disabled {
		opacity: 0.5;
		cursor: not-allowed;
		pointer-events: none;
	}
}
