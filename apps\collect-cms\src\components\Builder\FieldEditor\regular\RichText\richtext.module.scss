@import '@/styles/config';

.wrapper {
	--ck-color-base-border: #{color('grey', 15)};
	--ck-border-radius: #{spacing(s2)};
	--ck-focus-ring: #{px-to(1px, rem) solid color('yellow', 80)};
	--ck-inner-shadow:
		#{inset 0 0 0 px-to(1px, rem) transparent, 0 0 0 px-to(2px, rem) color('yellow', 80)};
}

.loading {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: spacing(s16);
	padding: spacing(s4);
	background-color: color('grey', 5);
	border: px-to(1px, rem) solid color('grey', 15);
	border-radius: spacing(s2);
	color: color('grey', 50);
	gap: spacing(s2);

	svg {
		--icon-size: #{spacing(s5)};
		animation: spin 1s linear infinite;
	}

	span {
		@include fluid($font-size) {
			font-size: size('body', 'md');
		}
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
