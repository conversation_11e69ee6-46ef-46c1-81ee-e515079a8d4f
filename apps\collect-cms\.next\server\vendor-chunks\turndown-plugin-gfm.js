"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/turndown-plugin-gfm";
exports.ids = ["vendor-chunks/turndown-plugin-gfm"];
exports.modules = {

/***/ "(ssr)/../../node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfm: () => (/* binding */ gfm),\n/* harmony export */   highlightedCodeBlock: () => (/* binding */ highlightedCodeBlock),\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough),\n/* harmony export */   tables: () => (/* binding */ tables),\n/* harmony export */   taskListItems: () => (/* binding */ taskListItems)\n/* harmony export */ });\nvar highlightRegExp = /highlight-(?:text|source)-([a-z0-9]+)/;\n\nfunction highlightedCodeBlock (turndownService) {\n  turndownService.addRule('highlightedCodeBlock', {\n    filter: function (node) {\n      var firstChild = node.firstChild;\n      return (\n        node.nodeName === 'DIV' &&\n        highlightRegExp.test(node.className) &&\n        firstChild &&\n        firstChild.nodeName === 'PRE'\n      )\n    },\n    replacement: function (content, node, options) {\n      var className = node.className || '';\n      var language = (className.match(highlightRegExp) || [null, ''])[1];\n\n      return (\n        '\\n\\n' + options.fence + language + '\\n' +\n        node.firstChild.textContent +\n        '\\n' + options.fence + '\\n\\n'\n      )\n    }\n  });\n}\n\nfunction strikethrough (turndownService) {\n  turndownService.addRule('strikethrough', {\n    filter: ['del', 's', 'strike'],\n    replacement: function (content) {\n      return '~' + content + '~'\n    }\n  });\n}\n\nvar indexOf = Array.prototype.indexOf;\nvar every = Array.prototype.every;\nvar rules = {};\n\nrules.tableCell = {\n  filter: ['th', 'td'],\n  replacement: function (content, node) {\n    return cell(content, node)\n  }\n};\n\nrules.tableRow = {\n  filter: 'tr',\n  replacement: function (content, node) {\n    var borderCells = '';\n    var alignMap = { left: ':--', right: '--:', center: ':-:' };\n\n    if (isHeadingRow(node)) {\n      for (var i = 0; i < node.childNodes.length; i++) {\n        var border = '---';\n        var align = (\n          node.childNodes[i].getAttribute('align') || ''\n        ).toLowerCase();\n\n        if (align) border = alignMap[align] || border;\n\n        borderCells += cell(border, node.childNodes[i]);\n      }\n    }\n    return '\\n' + content + (borderCells ? '\\n' + borderCells : '')\n  }\n};\n\nrules.table = {\n  // Only convert tables with a heading row.\n  // Tables with no heading row are kept using `keep` (see below).\n  filter: function (node) {\n    return node.nodeName === 'TABLE' && isHeadingRow(node.rows[0])\n  },\n\n  replacement: function (content) {\n    // Ensure there are no blank lines\n    content = content.replace('\\n\\n', '\\n');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.tableSection = {\n  filter: ['thead', 'tbody', 'tfoot'],\n  replacement: function (content) {\n    return content\n  }\n};\n\n// A tr is a heading row if:\n// - the parent is a THEAD\n// - or if its the first child of the TABLE or the first TBODY (possibly\n//   following a blank THEAD)\n// - and every cell is a TH\nfunction isHeadingRow (tr) {\n  var parentNode = tr.parentNode;\n  return (\n    parentNode.nodeName === 'THEAD' ||\n    (\n      parentNode.firstChild === tr &&\n      (parentNode.nodeName === 'TABLE' || isFirstTbody(parentNode)) &&\n      every.call(tr.childNodes, function (n) { return n.nodeName === 'TH' })\n    )\n  )\n}\n\nfunction isFirstTbody (element) {\n  var previousSibling = element.previousSibling;\n  return (\n    element.nodeName === 'TBODY' && (\n      !previousSibling ||\n      (\n        previousSibling.nodeName === 'THEAD' &&\n        /^\\s*$/i.test(previousSibling.textContent)\n      )\n    )\n  )\n}\n\nfunction cell (content, node) {\n  var index = indexOf.call(node.parentNode.childNodes, node);\n  var prefix = ' ';\n  if (index === 0) prefix = '| ';\n  return prefix + content + ' |'\n}\n\nfunction tables (turndownService) {\n  turndownService.keep(function (node) {\n    return node.nodeName === 'TABLE' && !isHeadingRow(node.rows[0])\n  });\n  for (var key in rules) turndownService.addRule(key, rules[key]);\n}\n\nfunction taskListItems (turndownService) {\n  turndownService.addRule('taskListItems', {\n    filter: function (node) {\n      return node.type === 'checkbox' && node.parentNode.nodeName === 'LI'\n    },\n    replacement: function (content, node) {\n      return (node.checked ? '[x]' : '[ ]') + ' '\n    }\n  });\n}\n\nfunction gfm (turndownService) {\n  turndownService.use([\n    highlightedCodeBlock,\n    strikethrough,\n    tables,\n    taskListItems\n  ]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js\n");

/***/ })

};
;