import { groupBy, Input, useIsomorphicLayoutEffect, useWindowDimensions } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import { useContext, useMemo, useRef, useState } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './componentmenu.module.scss'

export const ComponentMenu = ({
	trigger,
	onClose,
}: {
	trigger: HTMLElement | null
	onClose: (component?: IComponentProps) => void
}) => {
	const { contentType, components } = useContext(PageBuilderContext)
	const { data: globalComponents } = components ?? {}
	const { data: uidConfig } = contentType ?? {}
	const [searchKey, setSearchKey] = useState('')
	const menuRef = useRef<HTMLDivElement>(null)
	const windowDimension = useWindowDimensions()

	// Get list available components
	const availComponents = useMemo(() => {
		if (!globalComponents || !uidConfig) return {}
		if (!uidConfig.schema.attributes.components) return {}
		if ('components' in uidConfig.schema.attributes.components === false) return {}

		const arrComponents = uidConfig.schema.attributes.components.components
		const filteredComponents = Object.values(globalComponents).filter(
			(component) =>
				arrComponents.includes(component.uid) &&
				component.schema.displayName.toLowerCase().includes(searchKey.toLowerCase())
		)

		const groupedComponents = groupBy(filteredComponents, 'category')

		return groupedComponents
	}, [uidConfig, globalComponents, searchKey])

	useIsomorphicLayoutEffect(() => {
		if (!menuRef.current || !trigger) return
		const menu = menuRef.current
		const element = trigger.querySelector('span') as HTMLElement
		const { left, top, height } = element.getBoundingClientRect()

		menu.style.left = `${left}px`

		if (top + menu.clientHeight > window.innerHeight) {
			menu.style.top = `${window.scrollY + top + height - menu.clientHeight}px`
		} else {
			menu.style.top = `${top + window.scrollY}px`
		}

		menu.focus()
	}, [trigger, menuRef, windowDimension])

	const handleBlurMenu = (e: React.FocusEvent) => {
		if (menuRef.current?.contains(e.relatedTarget) || e.relatedTarget?.contains(trigger)) return
		onClose()
	}

	return (
		trigger && (
			<div
				role="menu"
				className={styles.wrapper}
				ref={menuRef}
				onBlur={handleBlurMenu}
				tabIndex={0}
			>
				<Input
					onChange={(e) => setSearchKey(e.target.value)}
					placeholder="Search blocks"
					className={cn(styles.search, 'collect__input has__border')}
				/>
				<div className={styles.inner} onScroll={handleSticky}>
					{Object.entries(availComponents).map((group, idx) => (
						<div key={idx} className={cn(styles.group, 'component__group')}>
							<p className="collect__body--sm">{formatString(group[0])}</p>
							<div>
								{group[1]?.map((component, index) => (
									<div
										className={styles.component}
										key={index}
										role="button"
										tabIndex={0}
										onClick={() => onClose(component)}
										onKeyDown={() => onClose(component)}
									>
										<div className={styles.thumbnail} />
										<div className={styles.info}>
											<p className="collect__body--lg">{component.schema.displayName}</p>
											<p className="collect__body--xs">{component.schema.description}</p>
										</div>
									</div>
								))}
							</div>
						</div>
					))}
				</div>
			</div>
		)
	)
}

const formatString = (string: string) => {
	const fieldName = ((string.split('.')[1] as string) ?? string)
		.toLowerCase()
		.replace(/(^|[\s\-_])([a-z])/g, (_, p1, letter) => ' ' + letter.toUpperCase())

	return fieldName
}

const handleSticky = (e: React.UIEvent<HTMLDivElement>) => {
	const container = e.target as HTMLElement
	const sticky = container.querySelectorAll('.component__group')
	sticky.forEach((element) => {
		const stickyRect = element.getBoundingClientRect()
		const containerRect = container.getBoundingClientRect()

		element
			.querySelector('.collect__body--sm')
			?.classList.toggle('is__sticky', stickyRect.top < containerRect.top)
	})
}
