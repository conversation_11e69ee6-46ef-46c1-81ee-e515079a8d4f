@import '@/styles/config';

.wrapper {
	display: grid;
	gap: spacing(s5);
	height: 100%;
	align-content: flex-start;
	padding-top: spacing(s5);

	.search {
		display: grid;
		gap: spacing(s5);

		padding-left: spacing(s5);
		padding-right: spacing(s5);
	}

	.components {
		overflow: auto;

		:global(.accordion__trigger) {
			padding: spacing(s4) spacing(s5);
			background-color: color('white', 5);
			position: sticky;
			inset: 0;
			border-top: 1px solid color('grey', 20);
			transition: 0.2s;

			svg {
				transition: inherit;
			}

			&:global(.is__active) {
				svg {
					transform: rotate(180deg);
				}
			}

			&:global(.is__sticky) {
				box-shadow: 0px spacing(s1) spacing(s6) 0px rgba(69, 69, 69, 0.15);
			}
		}

		:global(.accordion__item) {
			padding-top: 0;
			padding-bottom: 0;
		}

		div:global(.content__inner) {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			padding: 0 spacing(s5) spacing(s5);
			gap: spacing(s3);
		}
	}

	.component {
		display: grid;
		gap: spacing(s1);
		.thumbnail {
			border-radius: spacing(s1);
			background-color: color('white', 0);
			height: px-to(86px, rem);
		}
		cursor: grab;

		&:active {
			cursor: grabbing;
		}
	}
}

.tooltip {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 9;
	background-color: color('white', 5);
	border-radius: px-to(10px, rem);
	padding: spacing(s5);
	display: grid;
	gap: px-to(10px, rem);
	width: px-to(320px, rem);

	h4 {
		display: flex;
		gap: px-to(10px, rem);
		font-weight: 600;
	}

	.tag {
		font-size: px-to(10px, rem);
		color: color('white', 0);
		padding: px-to(5px, rem) px-to(10px, rem);
		border-radius: spacing(s5);
		background-color: color('grey', 80);
	}

	.thumbnail {
		width: 100%;
		height: px-to(147px, rem);
		background: color('white', 0);
		border-radius: spacing(s3);
	}
}
