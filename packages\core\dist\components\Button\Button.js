import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import Link from 'next/link';
import * as React from 'react';
import styles from './button.module.scss';
const variants = {
    primary: styles['button--primary'],
    accent: styles['button--accent'],
};
const sizes = {
    sm: styles['button--sm'],
    md: styles['button--md'],
};
const shapes = {
    fill: styles['button--fill'],
    outline: styles['button--outline'],
};
/** All type of button in CCF Project */
export const Button = React.forwardRef(({ type = 'button', className = '', variant = 'primary', size = 'md', shape = 'fill', startIcon, endIcon, isDark, scroll = true, href, ...props }, ref) => {
    if (href) {
        return (_jsxs(Link, { scroll: scroll, className: cn(styles.button, variants[variant], isDark ? styles.isDark : '', sizes[size], shapes[shape], className), href: href, children: [startIcon, _jsx("span", { className: styles.content, children: props.children }), endIcon] }));
    }
    return (_jsxs("button", { ref: ref, type: type, className: cn(styles.button, variants[variant], isDark ? styles.isDark : '', sizes[size], shapes[shape], className), ...props, children: [startIcon, _jsx("span", { className: styles.content, children: props.children }), endIcon] }));
});
Button.displayName = 'Button';
