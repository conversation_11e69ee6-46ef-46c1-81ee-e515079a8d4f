/**
 * Custom config base for projects using typescript / javascript.
 *
 */

module.exports = {
	env: {
		es6: true,
		node: true,
	},
	parser: '@typescript-eslint/parser',
	parserOptions: {
		ecmaFeatures: {
			jsx: true,
			globalReturn: false,
		},
		ecmaVersion: 2020,
		project: ['tsconfig.json'],
		sourceType: 'module',
	},
	settings: {
		'import/parsers': {
			'@typescript-eslint/parser': ['.ts', '.tsx', '.mts'],
		},
		'import/resolver': {
			typescript: {},
		},
	},
	extends: [
		'eslint:recommended',
		'plugin:@typescript-eslint/recommended',
		'plugin:react-hooks/recommended',
		'plugin:import/recommended',
		'plugin:import/typescript',

		// 'plugin:react-hooks/recommended',
		// 'eslint:recommended',
		// 'plugin:import/recommended',
		// 'plugin:import/typescript',
		// 'plugin:@typescript-eslint/recommended-type-checked',
		// 'plugin:@typescript-eslint/stylistic-type-checked',
	],
	rules: {
		'spaced-comment': [
			'error',
			'always',
			{
				line: {
					markers: ['/'],
					exceptions: ['-', '+'],
				},
				block: {
					markers: ['!'],
					exceptions: ['*'],
					balanced: true,
				},
			},
		],
		'linebreak-style': ['error', 'unix'],
		'no-empty-function': 'off',
		'import/default': 'off',
		// Caution this rule is slow https://github.com/import-js/eslint-plugin-import/blob/main/docs/rules/namespace.md
		'import/namespace': 'off', // ['error'] If you want the extra check (typechecking will spot most issues already)
		// https://github.com/import-js/eslint-plugin-import/blob/main/docs/rules/no-duplicates.md
		'import/no-duplicates': ['error', { considerQueryString: true }],
		'import/no-named-as-default-member': 'off',
		'import/no-named-as-default': 'off',
		'import/order': [
			'error',
			{
				groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index', 'object'],
				alphabetize: { order: 'asc', caseInsensitive: true },
			},
		],
		'@typescript-eslint/ban-tslint-comment': ['error'],
		'@typescript-eslint/ban-ts-comment': [
			'error',
			{
				'ts-expect-error': 'allow-with-description',
				minimumDescriptionLength: 10,
				'ts-ignore': true,
				'ts-nocheck': true,
				'ts-check': false,
			},
		],
		'@typescript-eslint/no-explicit-any': ['error', { ignoreRestArgs: false }],
		'@typescript-eslint/no-empty-function': ['error', { allow: ['private-constructors'] }],
		'@typescript-eslint/no-unused-vars': [
			'warn',
			{ argsIgnorePattern: '^_', ignoreRestSiblings: true },
		],
		'@typescript-eslint/consistent-type-exports': 'error',
		'@typescript-eslint/consistent-type-imports': ['error', { prefer: 'type-imports' }],
		'@typescript-eslint/no-misused-promises': [
			'error',
			{
				checksVoidReturn: {
					arguments: false,
					attributes: false,
				},
			},
		],
		'@typescript-eslint/naming-convention': [
			'error',
			{
				selector: 'default',
				format: ['camelCase'],
				leadingUnderscore: 'forbid',
				trailingUnderscore: 'forbid',
			},
			{
				selector: 'import',
				format: ['camelCase', 'PascalCase'],
			},
			{
				selector: 'variable',
				format: ['camelCase'],
				leadingUnderscore: 'allow',
			},
			// require all global constants to be camelCase or UPPER_CASE
			// all other variables and functions still need to be camelCase
			{
				selector: 'variable',
				modifiers: ['exported', 'global', 'const'],
				types: ['boolean', 'string', 'number', 'array'],
				format: ['UPPER_CASE'],
			},
			{
				selector: ['function'],
				format: ['camelCase'],
			},
			{
				selector: 'parameter',
				format: ['camelCase'],
				leadingUnderscore: 'allow',
			},
			// enum members must be in PascalCase. Without this config, enumMember would inherit UPPER_CASE from public static const property
			{
				selector: ['enum', 'enumMember'],
				format: ['PascalCase'],
			},
			{
				selector: 'class',
				format: ['PascalCase'],
			},
			{
				selector: 'classProperty',
				format: ['camelCase'],
				leadingUnderscore: 'allow',
			},
			{
				selector: 'objectLiteralProperty',
				format: [
					'camelCase',
					// Some external libraries use snake_case for params
					'snake_case',
					// Env variables are generally uppercase
					'UPPER_CASE',
					// DB / Graphql might use PascalCase for relationships
					'PascalCase',
				],
				leadingUnderscore: 'allowSingleOrDouble',
				trailingUnderscore: 'allowSingleOrDouble',
			},
			{
				selector: ['typeAlias', 'interface'],
				format: ['PascalCase'],
				prefix: ['I'],
			},
			{
				selector: ['typeProperty'],
				format: ['camelCase'],
				// For graphql __typename
				leadingUnderscore: 'allowDouble',
			},
			{
				selector: ['typeParameter'],
				format: ['PascalCase'],
			},
			// enforce UPPER_CASE for all public static readonly(!) properties
			{
				selector: 'classProperty',
				modifiers: ['public', 'static'],
				format: ['UPPER_CASE'],
			},
			// allow leading underscores for unused parameters, because `tsc --noUnusedParameters` will not flag underscore prefixed parameters
			// all other rules (trailingUnderscore: forbid, format: camelCase) still apply
			{
				selector: 'parameter',
				modifiers: ['unused'],
				format: ['camelCase'],
				leadingUnderscore: 'allow',
			},
		],
	},
	overrides: [
		{
			files: ['*.d.ts'],
			rules: {
				'@typescript-eslint/no-import-type-side-effects': 'off',
				'@typescript-eslint/no-explicit-any': 'off',
			},
		},
		{
			files: ['*.mjs'],
			extends: ['plugin:@typescript-eslint/disable-type-checked'],
			parserOptions: {
				ecmaVersion: 'latest',
				sourceType: 'module',
			},
			rules: {
				'@typescript-eslint/explicit-module-boundary-types': 'off',
				'@typescript-eslint/consistent-type-exports': 'off',
				'@typescript-eslint/consistent-type-imports': 'off',
				'@typescript-eslint/no-unsafe-call': 'off',
				'@typescript-eslint/no-unsafe-member-access': 'off',
				'@typescript-eslint/no-unsafe-return': 'off',
			},
		},
		{
			// javascript commonjs
			files: ['*.js', '*.cjs'],
			extends: ['plugin:@typescript-eslint/disable-type-checked'],
			parser: 'espree',
			parserOptions: {
				ecmaVersion: '2020',
			},
			rules: {
				'@typescript-eslint/ban-ts-comment': 'off',
				'@typescript-eslint/no-explicit-any': 'off',
				'@typescript-eslint/no-var-requires': 'off',
				'@typescript-eslint/explicit-module-boundary-types': 'off',
				'@typescript-eslint/consistent-type-exports': 'off',
				'@typescript-eslint/consistent-type-imports': 'off',
			},
		},
	],
}
