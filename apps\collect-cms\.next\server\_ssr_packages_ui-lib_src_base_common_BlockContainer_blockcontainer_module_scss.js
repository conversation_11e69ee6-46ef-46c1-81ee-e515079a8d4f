/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockContainer_blockcontainer_module_scss";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockContainer_blockcontainer_module_scss"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontainer_wrapper__9pM15\",\n\t\"content\": \"blockcontainer_content__CVQDR\",\n\t\"block\": \"blockcontainer_block__FgkM7\",\n\t\"block__title\": \"blockcontainer_block__title__yzrl_\",\n\t\"block__content\": \"blockcontainer_block__content__pprpc\"\n};\n\nmodule.exports.__checksum = \"73f097d7ba0e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRhaW5lci9ibG9ja2NvbnRhaW5lci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250YWluZXIvYmxvY2tjb250YWluZXIubW9kdWxlLnNjc3M/NDVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiYmxvY2tjb250YWluZXJfd3JhcHBlcl9fOXBNMTVcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250YWluZXJfY29udGVudF9fQ1ZRRFJcIixcblx0XCJibG9ja1wiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19GZ2tNN1wiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX190aXRsZV9feXpybF9cIixcblx0XCJibG9ja19fY29udGVudFwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19jb250ZW50X19wcHJwY1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI3M2YwOTdkN2JhMGVcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss\n");

/***/ })

};
;