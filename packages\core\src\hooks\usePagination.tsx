import { useMemo } from 'react'

export const DOTS = 'DOTS'

type UsePaginationProps = {
	totalCount: number
	pageSize: number
	siblingCount?: number
	currentPage: number
}

const range = (start: number, end: number) => {
	return Array.from({ length: end - start + 1 }, (_, idx) => idx + start)
}

export const usePagination = ({
	totalCount,
	pageSize,
	siblingCount = 1,
	currentPage,
}: UsePaginationProps) => {
	const paginationRange = useMemo(() => {
		const totalPageCount = Math.ceil(totalCount / pageSize)
		const totalDisplayedPages = siblingCount + 5

		if (totalDisplayedPages >= totalPageCount) {
			return range(1, totalPageCount)
		}

		const leftSiblingIndex = Math.max(currentPage - siblingCount, 1)
		const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPageCount)

		const shouldShowLeftDots = leftSiblingIndex > 2
		const shouldShowRightDots = rightSiblingIndex < totalPageCount - 2

		const firstPageIndex = 1
		const lastPageIndex = totalPageCount

		// If only right dots are needed
		if (!shouldShowLeftDots && shouldShowRightDots) {
			const leftRange = range(1, rightSiblingIndex)
			return [...leftRange, DOTS, lastPageIndex]
		}

		// If only left dots are needed
		if (shouldShowLeftDots && !shouldShowRightDots) {
			const rightRange = range(leftSiblingIndex, totalPageCount)
			return [firstPageIndex, DOTS, ...rightRange]
		}

		// If both left and right dots are needed
		if (shouldShowLeftDots && shouldShowRightDots) {
			const middleRange = range(leftSiblingIndex, rightSiblingIndex)
			return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex]
		}

		return []
	}, [totalCount, pageSize, siblingCount, currentPage])

	return paginationRange
}
