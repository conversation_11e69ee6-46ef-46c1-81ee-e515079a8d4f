'use client'

import { createContext, useContext, useState } from 'react'

type ModalType = 'signin' | 'confirm' // expand here
interface ModalContextType {
	onOpenModal: (type: ModalType, data?: object) => void
	onCloseModal: () => void
	modalData: { type: ModalType; data?: object } | null
}

export const ModalContext = createContext<ModalContextType | undefined>(undefined)

export const ModalProvider = ({ children }: { children: React.ReactNode }) => {
	const [modalData, setModalData] = useState<{ type: ModalType; data?: object } | null>(null)

	const onOpenModal = (type: ModalType, data?: object) =>
		setModalData({
			type: type,
			data: data,
		})
	const onCloseModal = () => setModalData(null)

	return (
		<ModalContext.Provider value={{ modalData, onOpenModal, onCloseModal }}>
			{children}
		</ModalContext.Provider>
	)
}

export const useModal = () => {
	const context = useContext(ModalContext)
	if (!context) throw new Error('useModal must be used within ModalProvider')
	return context
}
