@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		// padding: 0;
	}

	.row {
		display: grid;
		gap: spacing(s5);
		grid-template-columns: repeat(2, 1fr);

		@include min-width('md') {
			grid-template-columns: repeat(var(--count), 1fr);
		}
	}

	.bg__color {
		position: relative;
		height: px-to(200px, rem);
		border-radius: px-to(25px, rem);
		cursor: pointer;

		&:hover {
			.tag {
				opacity: 1;
				visibility: visible;
			}
		}
	}

	.description {
		width: 100%;
		margin: px-to(10px, rem);
		color: color('black', 60);
		font-size: px-to(14px, rem);

		caption {
			margin-bottom: spacing(s4);
		}

		th {
			width: auto;
			text-transform: uppercase;
		}

		* {
			text-align: left;
		}
	}

	.tag {
		visibility: hidden;
		opacity: 0;
		user-select: none;
		pointer-events: none;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		font-size: px-to(14px, rem);
		font-weight: 600;
		line-height: 1.2;
		color: #fff;
		border-radius: px-to(5px, rem);
		padding: px-to(5px, rem) px-to(10px, rem);
		background-color: rgba(0, 0, 0, 0.25);
		transition: 0.2s var(--ease-transition-2);
		display: flex;
		gap: px-to(5px, rem);
		align-items: center;
	}
}
