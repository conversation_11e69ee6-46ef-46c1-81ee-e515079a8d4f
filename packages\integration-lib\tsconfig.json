{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@collective/tsconfig/base.json", "compilerOptions": {"baseUrl": "./src", "rootDir": "./src", "outDir": "./dist", "target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "jsx": "react-jsx", "noEmit": false, "incremental": true, "composite": true}, "exclude": ["**/node_modules", "**/.*/*", "dist", "build"], "include": ["./src"]}