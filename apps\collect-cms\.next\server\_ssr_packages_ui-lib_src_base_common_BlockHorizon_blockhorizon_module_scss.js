/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockHorizon_blockhorizon_module_scss";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockHorizon_blockhorizon_module_scss"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockhorizon_wrapper__Z8BFI\",\n\t\"block\": \"blockhorizon_block__NfrMu\",\n\t\"block__title\": \"blockhorizon_block__title__N0rAr\",\n\t\"block__content\": \"blockhorizon_block__content__9MijM\"\n};\n\nmodule.exports.__checksum = \"e0b99015f311\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0hvcml6b24vYmxvY2tob3Jpem9uLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tIb3Jpem9uL2Jsb2NraG9yaXpvbi5tb2R1bGUuc2Nzcz9jZTE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2hvcml6b25fd3JhcHBlcl9fWjhCRklcIixcblx0XCJibG9ja1wiOiBcImJsb2NraG9yaXpvbl9ibG9ja19fTmZyTXVcIixcblx0XCJibG9ja19fdGl0bGVcIjogXCJibG9ja2hvcml6b25fYmxvY2tfX3RpdGxlX19OMHJBclwiLFxuXHRcImJsb2NrX19jb250ZW50XCI6IFwiYmxvY2tob3Jpem9uX2Jsb2NrX19jb250ZW50X185TWlqTVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlMGI5OTAxNWYzMTFcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss\n");

/***/ })

};
;