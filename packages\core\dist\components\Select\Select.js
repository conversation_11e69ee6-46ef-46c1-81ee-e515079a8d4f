'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import { Children, createContext, useContext, useMemo, useRef, useState, } from 'react';
import { createPortal } from 'react-dom';
import { useIsomorphicLayoutEffect, useWindowDimensions } from '../../hooks';
import styleSelect from './select.module.scss';
import * as SelectModule from './SelectMode';
const DetectMode = {
    single: 'Single',
    multiple: 'Multiple',
};
const defaultContext = {
    option: {
        value: '',
        label: '',
    },
    setOption: () => {
        return '';
    },
    setIsShow: () => {
        return '';
    },
    mode: 'single',
    isShow: false,
    onChange: () => {
        return '';
    },
};
export const SelectContext = createContext(defaultContext);
export function Select({ id, label, mode = 'single', placeholder, dropdownClass, className, startIcon, endIcon, children, defaultOption, onChange, required, }) {
    const [isShow, setIsShow] = useState(false);
    const [wrapperPos, setWrapperPos] = useState({
        x: 'left',
        y: 'top',
    });
    const defaultDataOption = mode === 'single'
        ? {
            value: '',
            label: placeholder || 'Select',
        }
        : [];
    const [option, setOption] = useState(defaultDataOption);
    useIsomorphicLayoutEffect(() => {
        if (!defaultOption)
            return setOption(defaultDataOption);
        return setOption(defaultOption);
    }, [defaultOption]);
    const drawerRef = useRef(null);
    const triggerRef = useRef(null);
    const windowDimension = useWindowDimensions();
    const handleBlurInput = () => {
        const { current: element } = triggerRef;
        element?.blur();
        setIsShow(false);
    };
    const handleOnClick = (event) => {
        const { target } = event;
        if (target === triggerRef.current || drawerRef?.current?.contains(target))
            return;
        handleBlurInput();
    };
    const placeWrapper = (wrapper, element) => {
        const { top, left, height, width, right } = element;
        console.log(element, left);
        const elmentWrapper = wrapper;
        if (left + wrapper.clientWidth > window.innerWidth) {
            elmentWrapper.style.right = `${window.innerWidth - right}px`;
            setWrapperPos({ ...wrapperPos, x: 'right' });
        }
        else {
            elmentWrapper.style.left = `${left}px`;
        }
        if (top + wrapper.clientHeight > window.innerHeight) {
            elmentWrapper.style.top = `${window.scrollY + top - wrapper.clientHeight}px`;
            setWrapperPos({ ...wrapperPos, y: 'bottom' });
        }
        else {
            elmentWrapper.style.top = `${window.scrollY + top + height}px`;
            setWrapperPos({ ...wrapperPos, y: 'top' });
        }
        elmentWrapper.style.width = `${wrapper.clientWidth < width ? width : wrapper.clientWidth}px`;
    };
    useIsomorphicLayoutEffect(() => {
        window.addEventListener('scroll', handleBlurInput);
        window.addEventListener('mousedown', handleOnClick);
        return () => {
            window.removeEventListener('scroll', handleBlurInput);
            window.removeEventListener('mousedown', handleOnClick);
        };
    }, []);
    useIsomorphicLayoutEffect(() => {
        handleBlurInput();
    }, [windowDimension]);
    useIsomorphicLayoutEffect(() => {
        const { current: element } = triggerRef;
        const elementPosition = element?.getBoundingClientRect();
        const { current: drawerPortal } = drawerRef;
        if (!drawerPortal)
            return;
        if (isShow) {
            placeWrapper(drawerPortal, elementPosition);
        }
        else {
            drawerPortal.removeAttribute('style');
            setWrapperPos({ ...wrapperPos, x: 'top', y: 'left' });
        }
    }, [isShow]);
    const PortalElement = (_jsx("div", { ref: drawerRef, id: id && `${id}-wrapper`, className: cn('select__dropdown', styleSelect.wrapper, styleSelect[`drawer__${wrapperPos.x}`], styleSelect[`drawer__${wrapperPos.y}`], dropdownClass), children: _jsx("div", { className: "dropdown__inner", children: children }) }));
    /* eslint-disable  @typescript-eslint/no-explicit-any */
    const SelectElements = SelectModule;
    const SelectMode = SelectElements[DetectMode[mode]];
    const props = {
        label,
        className,
        startIcon,
        endIcon,
        placeholder,
        required,
        totalItem: Children.count(children),
    };
    const ValueContextMemo = useMemo(() => ({
        option,
        setOption,
        setIsShow,
        isShow,
        mode,
        onChange,
    }), [option, mode, isShow, onChange]);
    return (_jsxs(SelectContext.Provider, { value: ValueContextMemo, children: [_jsx(SelectMode, { ...props, ref: triggerRef }), isShow && createPortal(PortalElement, document.body)] }));
}
export function SelectItem({ value, children, className, }) {
    const { mode, ...rest } = useContext(SelectContext);
    /* eslint-disable  @typescript-eslint/no-explicit-any */
    const SelectElements = SelectModule;
    const SelectMode = SelectElements[`${DetectMode[mode]}Item`];
    return (_jsx(SelectMode, { className: className, value: value.toString(), ...rest, children: children }));
}
