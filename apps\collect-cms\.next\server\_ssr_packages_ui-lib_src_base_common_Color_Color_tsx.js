/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_Color_Color_tsx";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_Color_Color_tsx"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Color/Color.tsx":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/Color.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(ssr)/../../packages/core/dist/utils/throttle-debounce.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./color.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss\");\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_color_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Color auto */ \n\n\n\nconst Color = ({ Colors })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().row),\n                style: {\n                    \"--count\": Colors.length\n                },\n                children: Colors?.map((color, idx)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().column),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BgColor, {\n                                color: color.HexColor\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().description),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n                                        children: color.ColorName\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 9\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"hex:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 11\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: color.HexColor\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 11\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 10\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"rgb:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 11\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: hexToRgb(color.HexColor)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 11\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 10\n                                            }, undefined),\n                                            color?.AdditionalField?.map((field, fieldIdx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: [\n                                                                field.FieldName,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 12\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: field.FieldValue\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 12\n                                                        }, undefined)\n                                                    ]\n                                                }, fieldIdx, true, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 11\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 9\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 7\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                lineNumber: 21,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 20,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 19,\n        columnNumber: 2\n    }, undefined);\nconst hexToRgb = (hex)=>{\n    // Remove the \"#\" if present\n    hex = hex.replace(/^#/, \"\");\n    // Parse the hex values\n    let r, g, b;\n    if (hex.length === 3) {\n        // Short format (#RGB)\n        r = parseInt(hex[0] + hex[0], 16);\n        g = parseInt(hex[1] + hex[1], 16);\n        b = parseInt(hex[2] + hex[2], 16);\n    } else if (hex.length === 6) {\n        // Full format (#RRGGBB)\n        r = parseInt(hex.substring(0, 2), 16);\n        g = parseInt(hex.substring(2, 4), 16);\n        b = parseInt(hex.substring(4, 6), 16);\n    } else {\n        throw new Error(\"Invalid HEX color.\");\n    }\n    return `${r}, ${g}, ${b}`;\n};\nconst BgColor = ({ color })=>{\n    const [isCopied, setIsCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const lazyCopy = (0,_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_3__.debounce)(()=>{\n        setIsCopied(false);\n    }, 250);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onKeyDown: ()=>{\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        tabIndex: 0,\n        role: \"button\",\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().bg__color),\n        style: {\n            background: color\n        },\n        onClick: ()=>{\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().tag),\n            children: isCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                        variant: \"copy\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 7\n                    }, undefined),\n                    \" Copied\"\n                ]\n            }, void 0, true) : color\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 108,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 90,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Color/Color.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/color.module.scss ***!
  \*********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"color_wrapper__yuBaS\",\n\t\"row\": \"color_row__afjAf\",\n\t\"bg__color\": \"color_bg__color__UyYaA\",\n\t\"tag\": \"color_tag__OFAjQ\",\n\t\"description\": \"color_description__pExio\"\n};\n\nmodule.exports.__checksum = \"efb6142de78d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9Db2xvci9jb2xvci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQ29sb3IvY29sb3IubW9kdWxlLnNjc3M/MDlkZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiY29sb3Jfd3JhcHBlcl9feXVCYVNcIixcblx0XCJyb3dcIjogXCJjb2xvcl9yb3dfX2FmakFmXCIsXG5cdFwiYmdfX2NvbG9yXCI6IFwiY29sb3JfYmdfX2NvbG9yX19VeVlhQVwiLFxuXHRcInRhZ1wiOiBcImNvbG9yX3RhZ19fT0ZBalFcIixcblx0XCJkZXNjcmlwdGlvblwiOiBcImNvbG9yX2Rlc2NyaXB0aW9uX19wRXhpb1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlZmI2MTQyZGU3OGRcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss\n");

/***/ })

};
;