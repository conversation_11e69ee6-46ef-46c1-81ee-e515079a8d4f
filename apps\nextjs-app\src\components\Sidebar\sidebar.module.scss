@use '@collective/ui-lib/styles/config' as *;

.brand__logo {
	padding: spacing(s5);

	a {
		display: inline-block;
	}

	img {
		width: auto;
		height: auto;
		max-height: px-to(64px, rem);
	}

	margin-bottom: spacing(s4);
}

.switch {
	border-radius: spacing(s2);
	margin: 0 spacing(s4);
	overflow: hidden;
	background-color: color('gray', 100);
	flex-shrink: 0;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	align-items: center;
	position: relative;

	:global(.switch__bar) {
		left: 0;
		top: 0;
		width: 50%;
		background-color: color('black', 100);
		position: absolute;
		z-index: 1;
		height: 100%;
	}

	a {
		display: flex;
		gap: px-to(10px, rem);
		align-items: center;
		justify-content: center;
		padding: spacing(s3) spacing(s4);
		position: relative;
		z-index: 1;
		color: color('neutral-gray');
		transition: 0.2s var(--ease-transition-2);
		transition-delay: 0.25s;

		&.active {
			color: color('white', 100);
		}
	}
}

.routes {
	overflow: hidden;
	position: relative;
	display: grid;
	gap: px-to(32px, rem);
	// padding-left: spacing(s4);
	// padding-right: spacing(s4);

	.redirect {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.bottom__mask {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: px-to(80px, rem);
		z-index: 1;
		background-image: linear-gradient(180deg, #ffffff00, #fafafa);
	}
}

.newchat {
	margin: px-to(-36px, rem) spacing(s4) 0;
	padding: spacing(s4) 0;
	display: grid;
	border-bottom: px-to(1px, rem) solid color('divider');
	button {
		text-align: center;
		font-weight: 500;
		// padding: spacing(s3) spacing(s5);
		color: color('neutral-gray');
		&:hover {
			background-color: color('gray', 100);
			color: color('black', 100);
		}

		span {
			display: flex;
			align-items: center;
			gap: px-to(10px, rem);
		}

		svg {
			--icon-size: #{px-to(18px, rem)};
		}
	}
}

.navigation {
	overflow: auto;
	padding-left: spacing(s2);
	padding-right: spacing(s2);
	padding-bottom: spacing(s13);
	&.trimleft {
		padding: 0 0 spacing(s13);
	}

	&::-webkit-scrollbar-track {
		background-color: #f0f0f0;
	}

	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-thumb {
		background-image: -webkit-linear-gradient(90deg, #7a7a7a, #dddddd);
	}

	:global(.accordion) {
		display: grid;
		gap: px-to(10px, rem);
		margin-bottom: 0;
	}

	:global(.accordion__item) {
		background-color: color('gray', 100);
		padding: spacing(s4);
		border-radius: px-to(10px, rem);
	}

	:global(.accordion__title) {
		@include fluid($font-size) {
			font-size: size('paragraph', 'lg');
		}

		line-height: 1.2;
		flex: 1;
		text-align: left;
		font-weight: 500;

		a {
			display: flex;
			align-items: center;
			gap: px-to(10px, rem);

			span {
				position: relative;
			}
		}
	}

	.lock {
		--size: #{px-to(18px, rem)};
	}

	.arrow {
		--size: #{spacing(s3)};
		transition: rotate 250ms;
		color: color('light-gray', 100);
	}

	:global(.accordion__trigger):global(.is__active) {
		.arrow {
			rotate: 90deg;
			color: color('neutral-gray');
		}
	}

	:global(.accordion__content) {
		margin-left: calc(-1 * spacing(s4));
		margin-right: calc(-1 * spacing(s4));

		:global(.content__inner) {
			padding-top: spacing(s4) !important;
		}
	}

	.sub__route {
		display: grid;
		gap: spacing(s1);

		a {
			display: flex;
			gap: px-to(10px, rem);
			padding: spacing(s2) spacing(s4);
			color: color('neutral-gray');
			font-weight: 600;
			transition: 0.2s var(--ease-transition-2);
			font-size: px-to(15px, rem);

			&:hover,
			&.active {
				color: color('black', 100);
				background-color: color('dark-gray', 100);
			}
		}
	}
}

.history {
	display: flex;
	flex-direction: column;
	gap: spacing(s6);
	&__notfound {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
		gap: spacing(s1);
		padding: spacing(s8);
		line-height: 1.5;
		color: color('light-gray', 100);
		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}
		svg {
			--icon-size: #{px-to(48px, rem)};
		}
	}
	&__group {
		display: flex;
		flex-direction: column;
	}
	&__label {
		padding: spacing(s2) spacing(s4);
		color: color('neutral-gray');
		font-weight: 500;

		@include fluid($font-size) {
			font-size: size('paragraph', md);
		}
	}
	&__item {
		padding: px-to(10px, rem) spacing(s4);
		color: color('black', 100);
		letter-spacing: 0.005em;
		position: relative;

		@include fluid($font-size) {
			font-size: size('paragraph', md);
		}

		&:hover {
			font-weight: 500;
			letter-spacing: 0;
			background-color: color('gray', 100);
			.more {
				display: flex;
			}
		}
		a {
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
			display: block;
		}
	}
	.more {
		position: absolute;
		display: none;
		justify-content: flex-end;
		align-items: center;
		padding-right: spacing(s2);
		width: 20%;
		height: 100%;
		right: 0;
		top: 0;
		background-image: linear-gradient(90deg, color('gray', 0) 0%, color('gray', 100) 50%);
		z-index: 9;
		button.more__trigger {
			width: px-to(18px, rem);
			height: px-to(18px, rem);
			--icon-size: #{px-to(18px, rem)};
			color: color('neutral-gray');
			padding: 0;
		}
		// &__menu {
		// 	// display: none;
		// 	position: relative;
		// 	width: 0;
		// 	&__wrapper {
		// 		position: absolute;
		// 		left: 0;
		// 		top: 100%;
		// 		background-color: color('white', 100);
		// 		border: px-to(1px, rem) solid color('border');
		// 		border-radius: spacing(s4);
		// 		padding: spacing(s1);
		// 		width: fit-content;
		// 	}
		// }
	}
}

// .ai__chat {
// 	padding: spacing(s5);
// 	margin-top: auto;

// 	button {
// 		background: linear-gradient(90deg, #190b3f 0%, #411da5 100%);
// 		padding: spacing(s6);
// 		color: #fff;
// 		cursor: pointer;
// 		width: 100%;
// 		border-radius: spacing(s2);
// 		display: flex;
// 		align-items: center;
// 		justify-content: space-between;

// 		> span {
// 			display: flex;
// 			align-items: center;
// 			gap: spacing(s2);
// 			:global(.svg__icon) {
// 				--size: #{spacing(s9)};
// 			}
// 		}
// 	}
// }

.account {
	padding-left: spacing(s4);
	padding-right: spacing(s4);
	padding-bottom: spacing(s6);
	margin-top: auto;

	button {
		width: 100%;
		border-color: color('light-gray', 100) !important;
	}

	:global(button.account__button) {
		font-size: size('button');
	}
}

.user {
	display: flex;
	gap: spacing(s4);
	&__avatar {
		object-fit: cover;
		width: px-to(50px, rem);
		height: px-to(50px, rem);
		border-radius: px-to(10px, rem);
		background-color: color('light-gray', 100);
		flex-shrink: 0;
	}
	&__info {
		display: grid;
		gap: spacing(s1);
		width: 100%;
	}
	&__username {
		color: color('black', 100);
		line-height: spacing(s6);
		font-weight: 500;
		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}
	}
	&__email {
		color: color('neutral-gray');
		line-height: spacing(s5);
		font-size: px-to(14px, rem);
	}
	button.user__signout {
		color: color('red', 100);
		padding: 0;
		flex-shrink: 0;
		height: fit-content;
		width: fit-content;
		svg {
			--size: #{px-to(18px, rem)};
		}
	}
}
