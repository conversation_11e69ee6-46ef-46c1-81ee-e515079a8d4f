{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@collective/tsconfig/base.json", "compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "jsx": "react-jsx", "baseUrl": "../", "rootDir": "../", "outDir": "dist", "emitDecoratorMetadata": true, "experimentalDecorators": true, "noEmit": false, "declaration": true, "declarationDir": "dist", "composite": true, "sourceMap": true, "incremental": true, "paths": {"@collective/i18n": ["../i18n/src"], "@collective/core": ["../core/src"], "@collective/integration-lib/*": ["./integration-lib/src/*"], "@collective/integration-lib": ["./integration-lib/src/index"], "@/styles": ["./styles/*"]}}, "exclude": ["**/node_modules", "**/.*/", "dist", "build"], "include": ["./src", "../core/src", "../i18n/src/", "../integration-lib/src/", "../../apps/nextjs-app/src/components/Breadcrumbs", "../../apps/nextjs-app/src/components/LightBox"]}