{"name": "@collective/integration-lib", "version": "1.0.0", "private": true, "sideEffects": false, "source": "src/index.ts", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "exports": {".": "./src/index.ts", "./cms": "./src/cms/index.ts", "./search": "./src/search/index.ts", "./seo": "./src/seo/index.ts"}, "scripts": {"build": "echo \"Unrequired and disabled when using tsonfig paths aliases, run 'build-force' to test a build.\"", "build-force": "tsc --build tsconfig.build.json", "build-storybook": "storybook build --output-dir build/storybook", "clean": "rimraf ./dist ./build ./tsconfig.tsbuildinfo ./tsconfig.build.tsbuildinfo ./node_modules/.cache", "dev": "microbundle watch --tsconfig ./tsconfig.build.json", "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs . --fix", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs --cache --cache-location ../../.cache/eslint/integration-lib.eslintcache", "serve-storybook": "sirv build/storybook --cors --port 8888", "storybook": "storybook dev -p 6006", "typecheck": "tsc --project ./tsconfig.json --noEmit"}, "devDependencies": {"@collective/core": "workspace:^", "@collective/eslint-config-bases": "workspace:^", "@collective/i18n": "workspace:^", "@collective/tsconfig": "workspace:^", "@types/node": "20.17.25", "cross-env": "7.0.3", "eslint": "8.57.1", "microbundle": "0.15.1", "next": "14.2.26", "npm-run-all2": "6.2.6", "prettier": "3.5.3", "rimraf": "6.0.1", "sirv": "3.0.0", "sirv-cli": "3.0.0", "typescript": "5.8.2"}, "dependencies": {"meilisearch": "0.49.0"}}