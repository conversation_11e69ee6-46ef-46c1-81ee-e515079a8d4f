'use client'

import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import _ from 'lodash'
import type { StaticImport, StaticRequire } from 'next/dist/shared/lib/get-img-props'
import NextImage, { type ImageLoaderProps } from 'next/image'
import React from 'react'
import { createPngDataUri } from './blurhash'
import styles from './image.module.scss'

const isImage = (url: string) => /\.(jpg|jpeg|png|webp|avif|gif|svg)$/.test(url)

const imageLoader = ({ src, width, quality }: ImageLoaderProps) => {
	return `${process.env.NEXT_PUBLIC_STRAPI_HOST}${src}?w=${width}&q=${quality || 75}`
}

type NextImageProps = React.ComponentProps<typeof NextImage>

export type ImageProps = Omit<NextImageProps, 'src'> & {
	blurhash?: string
	isRemote?: boolean
	component?: string
	parallax?: boolean
	media?: NextImageProps['src'] | IMediaProps<'single'>
	src?: NextImageProps['src']
}

// Type Guard for checking if `media` is a StaticImport
function isStaticImport(value: unknown): value is StaticImport {
	if (typeof value === 'object' && value !== null) {
		// Check for StaticImageData directly
		if (
			'src' in value &&
			'height' in value &&
			'width' in value &&
			'blurWidth' in value &&
			'blurHeight' in value
		) {
			return true
		}
		// Check for StaticRequire (has `default` property)
		if ('default' in value && typeof (value as StaticRequire).default === 'object') {
			const defaultVal = (value as StaticRequire).default
			return 'src' in defaultVal && 'height' in defaultVal && 'width' in defaultVal
		}
	}
	return false
}

function returnLocalImage({
	ref,
	media,
	isParallax,
	props,
}: {
	ref: React.ForwardedRef<HTMLImageElement>
	media: string | StaticImport
	isParallax?: boolean
	props: ImageProps
}) {
	const { ...rest } = props
	if (isParallax) {
		return (
			<ParallaxEffect className={rest.className}>
				<NextImage
					ref={ref}
					src={media}
					{...rest}
					width={rest.width || 1}
					height={rest.height || 1}
					className="coeus__img"
				/>
			</ParallaxEffect>
		)
	}
	return (
		<NextImage ref={ref} src={media} width={rest.width || 1} height={rest.height || 1} {...rest} />
	)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Image = React.forwardRef<HTMLImageElement, ImageProps>((props: ImageProps, ref) => {
	const { media: media0, src: src0, isRemote, blurhash, ...rest } = props
	const media = media0 || src0

	const DEBUG_COMPONENT: string = ''
	// const DEBUG_COMPONENT: string = "Product/ProductPreview"

	if (DEBUG_COMPONENT !== '' && rest.component === DEBUG_COMPONENT) {
		console.log('Pre-check', media0, src0, media)
	}

	if (media === undefined || _.isEmpty(media)) {
		return <NextImage src={''} {...rest} alt="No image provided" />
	}

	if (typeof media === 'string' || isStaticImport(media) || media.blurhash === undefined) {
		if (typeof media === 'string' || isStaticImport(media)) {
			return returnLocalImage({
				ref,
				media,
				isParallax: rest.parallax,
				props: { ...rest },
			})
		}
		return returnLocalImage({
			ref,
			media: media.url,
			isParallax: rest.parallax,
			props: {
				...rest,
				width: media.width,
				height: media.height,
				alt: media.alternativeText || media.name,
			},
		})
	}

	let type = 'local'
	let { alt } = rest
	let width = rest.width || media.width || 0
	if (typeof width === 'string') {
		width = parseInt(width)
	}
	let height = rest.height || media.height || 0
	if (typeof height === 'string') {
		height = parseInt(height)
	}

	if (media !== undefined && (media.url === undefined || media.blurhash !== undefined)) {
		if (media.blurhash !== undefined && media.blurhash !== null) {
			type = 'remote'
		}
		type = 'remote'
		alt = media.alternativeText || media.name
	}
	const src = media.url

	if (DEBUG_COMPONENT !== '' && rest.component === DEBUG_COMPONENT) {
		console.log('After-check', media, 'type', type)
		console.log('src', src)
	}

	if (isRemote) {
		type = 'remote'
	}

	if (!isImage(src)) {
		return (
			<div>
				<video autoPlay loop muted playsInline>
					<track kind="captions" />
					<source src={`${process.env.NEXT_PUBLIC_STRAPI_HOST}${src}`} type="video/mp4" />
					Your browser does not support the video tag.
				</video>
			</div>
		)
	}

	if (width === undefined && rest.fill === undefined) {
		width = media.width || 1
	}

	if (height === undefined && rest.fill === undefined) {
		height = media.height || 1
	}

	const blurhashData = media.blurhash || blurhash
	if (rest.parallax) {
		return (
			<ParallaxEffect className={rest.className}>
				<NextImage
					loader={imageLoader}
					src={src}
					placeholder={rest.placeholder || 'blur'}
					blurDataURL={createPngDataUri(blurhashData, {
						ratio: width / height,
					})}
					ref={ref}
					{...rest}
					width={width}
					height={height}
					alt={alt}
					className="coeus__img"
				/>
			</ParallaxEffect>
		)
	}

	return (
		<NextImage
			loader={imageLoader}
			src={src}
			placeholder="blur"
			blurDataURL={createPngDataUri(blurhashData, {
				ratio: width / height,
			})}
			ref={ref}
			{...rest}
			width={width}
			height={height}
			alt={alt}
		/>
	)
})

const ParallaxEffect = ({
	children,
	className,
}: {
	children: React.ReactNode
	className?: string
}) => {
	return <div className={cn(styles.wrapper, className)}>{children}</div>
}

export { Image }
