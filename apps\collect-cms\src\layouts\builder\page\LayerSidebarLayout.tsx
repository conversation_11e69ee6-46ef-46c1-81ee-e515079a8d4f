import { Icon, useIsomorphicLayoutEffect } from '@collective/core'
import cn from 'classnames'
import { useContext, useState, useRef, useEffect } from 'react'
import { FieldEditor } from '@/components/Builder'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './pagebuilderlayout.module.scss'

// Custom hook để phát hiện click bên ngoài một phần tử
const useClickOutside = (callback: () => void) => {
	const ref = useRef<HTMLDivElement>(null)

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (ref.current && !ref.current.contains(event.target as Node)) {
				callback()
			}
		}

		document.addEventListener('mousedown', handleClickOutside)
		return () => {
			document.removeEventListener('mousedown', handleClickOutside)
		}
	}, [callback])

	return ref
}

export const LayerSidebarLayout = () => {
	const context = useContext(PageBuilderContext)
	const { childComponentData: childCmp, setChildComponentData, layerPos } = context
	const [curChildIndex, setCurChildIndex] = useState(0)
	const [isPrevEntriesOpen, setIsPrevEntriesOpen] = useState(false)

	// Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài
	const prevEntriesRef = useClickOutside(() => {
		if (isPrevEntriesOpen) {
			setIsPrevEntriesOpen(false)
		}
	})

	const handleBack = (idx: number | undefined) => {
		const newVal = [...childCmp]
		console.log(idx)
		if (idx !== undefined) {
			if (curChildIndex === idx) return
			setCurChildIndex(idx)
			newVal.splice(idx + 1, newVal.length - (idx + 1))
		} else {
			setCurChildIndex(curChildIndex - 1)
			newVal.pop()
		}
		setChildComponentData(newVal)
	}

	const handleRemove = (idx: number) => {
		console.log('Remove idx:', idx)

		if (!childCmp[idx] || !childCmp[idx].onChange) return

		const currentEntry = childCmp[idx]

		// Check if we need to find the parent component to determine if it's repeatable
		// Look for the parent entry that contains this value
		let parentEntry = null
		let isRepeatable = false
		let targetIndex = -1

		// Search through previous entries to find the parent
		for (let i = idx - 1; i >= 0; i--) {
			const entry = childCmp[i]
			if (Array.isArray(entry.value)) {
				// Check if current value exists in this array
				targetIndex = entry.value.findIndex((item) => item === currentEntry.value)
				if (targetIndex !== -1) {
					parentEntry = entry
					isRepeatable = true
					break
				}
			} else if (entry.value === currentEntry.value) {
				// This is a non-repeatable component
				parentEntry = entry
				isRepeatable = false
				break
			}
		}

		if (parentEntry && parentEntry.onChange) {
			if (isRepeatable && targetIndex !== -1) {
				// Remove from array
				const parentValue = [...(parentEntry.value as unknown[])]
				parentValue.splice(targetIndex, 1)
				parentEntry.onChange({ field: 'value', value: parentValue })
			} else {
				// Set to null for non-repeatable
				parentEntry.onChange({ field: 'value', value: null })
			}
		}

		// Update childComponentData - remove current and subsequent entries
		const newChildData = [...childCmp]
		newChildData.splice(idx, newChildData.length - idx)
		setChildComponentData(newChildData)

		// Update current index to previous level
		if (idx > 0) {
			setCurChildIndex(idx - 1)
		}
	}

	const handleDuplicate = (idx: number) => {
		console.log('Duplicate idx:', idx)

		if (!childCmp[idx] || !childCmp[idx].onChange) return

		const currentEntry = childCmp[idx]
		const currentValue = currentEntry.value

		// Determine if this is a repeatable component
		const parentEntry = childCmp[idx - 1] // Previous level entry

		if (parentEntry && Array.isArray(parentEntry.value)) {
			// This is a repeatable component - duplicate in array
			const parentValue = [...parentEntry.value]
			const targetIndex = parentValue.findIndex((item) => item === currentValue)

			if (targetIndex !== -1 && parentEntry.onChange) {
				// Create a deep copy of the item to duplicate
				const duplicatedItem = JSON.parse(JSON.stringify(currentValue))
				parentValue.splice(targetIndex + 1, 0, duplicatedItem)
				parentEntry.onChange({ field: 'value', value: parentValue })
			}
		}
		// Note: Non-repeatable components cannot be duplicated
	}

	useIsomorphicLayoutEffect(() => {
		if (!childCmp || childCmp.length === 0) return
		setCurChildIndex(childCmp.length - 1)
	}, [childCmp])

	return (
		<>
			{childCmp && childCmp.length > 0 && childCmp[curChildIndex] && (
				<div
					className={cn(
						styles.sidebar,
						styles.sidebar__layer,
						layerPos !== '' ? styles[layerPos] : ''
					)}
				>
					<div className={styles.component__title}>
						<button onClick={() => handleBack(undefined)}>
							<Icon type="cms" variant="back" />
						</button>
						<h6 className="collect__heading collect__heading--h6">
							{childCmp.length > 1 && (
								<>
									<div className={styles.preventries} ref={prevEntriesRef}>
										<button
											onClick={() => setIsPrevEntriesOpen(!isPrevEntriesOpen)}
											title="Show previous entries"
											className={styles.preventries__trigger}
										>
											<span>...</span>
										</button>
										{isPrevEntriesOpen && (
											<div className={styles.preventries__list}>
												{childCmp.map((item, idx) =>
													idx === (0 || curChildIndex) ? null : (
														<button
															key={idx}
															className={styles.preventries__item}
															title={`Back to ${item.name}`}
															onClick={() => {
																handleBack(idx)
																setIsPrevEntriesOpen(false)
															}}
														>
															<span key={idx}>{item.name}</span>
														</button>
													)
												)}
											</div>
										)}
									</div>
									/
								</>
							)}
							<button title={curChildIndex === 0 ? '' : `Back to ${childCmp[curChildIndex].name}`}>
								<span>{childCmp[curChildIndex].name}</span>
							</button>
						</h6>
						<div className={styles.component__action}>
							<button title="Duplicate this entry" onClick={() => handleDuplicate(curChildIndex)}>
								<Icon variant="duplicate" type="cms" />
							</button>
							<button
								className={styles.remove__button}
								title="Remove this entry"
								onClick={() => handleRemove(curChildIndex)}
							>
								<Icon variant="remove" type="cms" />
							</button>
						</div>
					</div>
					<div className={styles.editor__components}>
						{childCmp[curChildIndex].fields.map(([key, fValue]) => {
							const fval = fValue as {
								type: string
							}
							return (
								<FieldEditor
									key={key}
									{...fval}
									layerPos={layerPos}
									name={key}
									size={12}
									value={(childCmp[curChildIndex]?.value as Record<string, unknown>)?.[key]}
									onChange={(props) => {
										if (!childCmp[curChildIndex]?.onChange) return
										console.log(props, key, fval, childCmp[curChildIndex].value)
										childCmp[curChildIndex].onChange(props)
									}}
								/>
							)
						})}
					</div>
				</div>
			)}
		</>
	)
}
