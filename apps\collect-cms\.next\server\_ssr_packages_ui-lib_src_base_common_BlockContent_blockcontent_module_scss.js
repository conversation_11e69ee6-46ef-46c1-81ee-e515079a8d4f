/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockContent_blockcontent_module_scss";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockContent_blockcontent_module_scss"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontent_wrapper__8O_bU\",\n\t\"headline\": \"blockcontent_headline__cMJyZ\",\n\t\"content\": \"blockcontent_content__KH33h\",\n\t\"content__grid\": \"blockcontent_content__grid__GajH2\",\n\t\"block\": \"blockcontent_block__8eue2\",\n\t\"block__title\": \"blockcontent_block__title__Ck2bf\",\n\t\"block__content\": \"blockcontent_block__content__CS7WJ\"\n};\n\nmodule.exports.__checksum = \"7e7468d3bd30\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRlbnQvYmxvY2tjb250ZW50Lm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250ZW50L2Jsb2NrY29udGVudC5tb2R1bGUuc2Nzcz8yZGM3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2NvbnRlbnRfd3JhcHBlcl9fOE9fYlVcIixcblx0XCJoZWFkbGluZVwiOiBcImJsb2NrY29udGVudF9oZWFkbGluZV9fY01KeVpcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250ZW50X2NvbnRlbnRfX0tIMzNoXCIsXG5cdFwiY29udGVudF9fZ3JpZFwiOiBcImJsb2NrY29udGVudF9jb250ZW50X19ncmlkX19HYWpIMlwiLFxuXHRcImJsb2NrXCI6IFwiYmxvY2tjb250ZW50X2Jsb2NrX184ZXVlMlwiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGVudF9ibG9ja19fdGl0bGVfX0NrMmJmXCIsXG5cdFwiYmxvY2tfX2NvbnRlbnRcIjogXCJibG9ja2NvbnRlbnRfYmxvY2tfX2NvbnRlbnRfX0NTN1dKXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjdlNzQ2OGQzYmQzMFwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss\n");

/***/ })

};
;