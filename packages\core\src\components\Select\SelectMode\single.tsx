import cn from 'classnames'
import React, { useContext } from 'react'
import styles from '../../Input/input.module.scss'
import { type OptionData, type OptionProps, SelectContext } from '../Select'
import styleSelect from '../select.module.scss'

type IconProps =
	| { startIcon: React.ReactElement; endIcon?: never }
	| { endIcon: React.ReactElement; startIcon?: never }
	| { endIcon?: undefined; startIcon?: undefined }

type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
	label: string
	required: boolean
} & IconProps

export type SingleItem = {
	value: string
	children: React.ReactNode | ((data: OptionData) => React.ReactNode)
	className?: string
}

export const Single = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ id, className, startIcon, endIcon, label, required }, ref) => {
		const { option, setIsShow, isShow } = useContext(SelectContext)

		const defaultOption = option as OptionProps
		return (
			<div
				className={cn(
					'form__wrapper',
					styles['form-control-wrapper'],
					className,
					startIcon && styles['form-control-wrapper--icon-left'],
					endIcon && styles['form-control-wrapper--icon-right'],
					isShow && 'is__triggered'
				)}
			>
				{label && (
					<span className={styles.label}>
						{label}
						{required && <span className={styles.mark}>*</span>}
					</span>
				)}
				<div className={styles.wrapper}>
					{startIcon && (
						<span className={cn(styles.icon, styles['icon--left'], 'icon--left')}>{startIcon}</span>
					)}

					<button
						id={id}
						type="button"
						className={cn(styles['form-control'])}
						onClick={() => setIsShow(!isShow)}
						ref={ref}
					>
						<span className="form-label">{defaultOption.label}</span>
					</button>

					{endIcon && (
						<span className={cn(styles.icon, styles['icon--right'], 'icon--right')}>{endIcon}</span>
					)}
				</div>
			</div>
		)
	}
)

export function SingleItem({ value, children, className }: SingleItem) {
	const { option, setOption, setIsShow, onChange } = useContext(SelectContext)
	const defaultOption = option as OptionProps

	const handleChange = (optionValue: string, optionLabel: string) => {
		setIsShow(false)
		if (defaultOption.value.toString() === optionValue) return
		const currentOption = {
			value: optionValue,
			label: optionLabel,
		}
		setOption(currentOption)
		onChange?.(currentOption)
	}

	const optionData = {
		isActive: defaultOption.value.toString() === value,
	}

	const RenderChildren = () => (typeof children === 'function' ? children(optionData) : children)

	return (
		<div
			role="button"
			tabIndex={0}
			className={cn(
				styleSelect.option,
				defaultOption.value.toString() === value && styleSelect.active,
				defaultOption.value.toString() === value && 'option__active',
				className
			)}
			onClick={() => handleChange(value, children as string)}
		>
			<RenderChildren />
		</div>
	)
}
