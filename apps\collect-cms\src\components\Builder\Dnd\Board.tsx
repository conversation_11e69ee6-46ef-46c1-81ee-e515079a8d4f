import React, { useState, useRef } from 'react'
import { Column } from './Column'

export const Board = <T,>({
	initial,
	children,
	className,
}: {
	initial: T[]
	children: React.ReactNode
	className?: string
}) => {
	const [data, setData] = useState(initial ?? [])
	const columns = React.Children.toArray(children) as React.ReactElement[]
	const scrollableRef = useRef<HTMLDivElement | null>(null)

	return (
		<div ref={scrollableRef} className={className}>
			{initial?.map((col, colIdx) => (
				<Column key={colIdx}>
					{columns?.map((column, idx) =>
						React.cloneElement(column, { column: col, index: colIdx, key: idx })
					)}
				</Column>
			))}
		</div>
	)
}
