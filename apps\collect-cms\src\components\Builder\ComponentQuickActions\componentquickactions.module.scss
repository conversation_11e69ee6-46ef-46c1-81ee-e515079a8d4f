@import '@/styles/config';

.wrapper {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	left: calc(-1 * spacing(s4));
	display: grid;
	gap: spacing(s2);
	background-color: color('white', 0);
	border-radius: px-to(4px, rem);
	box-shadow: 0 px-to(2px, rem) px-to(8px, rem) rgba(0, 0, 0, 0.1);
	z-index: 10;
	opacity: 0;
	transition: opacity 0.2s ease;

	&.is__visible {
		opacity: 1;
	}
}

.action {
	display: flex;
	align-items: center;
	justify-content: center;
	width: px-to(28px, rem);
	height: px-to(28px, rem);
	border-radius: px-to(4px, rem);
	background-color: transparent;
	cursor: pointer;
	transition: background-color 0.2s ease;

	&:hover {
		background-color: color('grey', 10);
	}

	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	svg {
		--icon-size: #{px-to(16px, rem)};
		color: color('grey', 60);
	}
}

.more_container {
	position: relative;
}

.more_menu {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	left: calc(100% + spacing(s1));
	background-color: color('white', 0);
	border-radius: px-to(4px, rem);
	box-shadow: 0 px-to(2px, rem) px-to(8px, rem) rgba(0, 0, 0, 0.1);
	min-width: px-to(120px, rem);
	z-index: 20;
}

.menu_item {
	display: flex;
	align-items: center;
	gap: spacing(s2);
	width: 100%;
	padding: spacing(s2) spacing(s3);
	text-align: left;
	background-color: transparent;
	cursor: pointer;
	transition: background-color 0.2s ease;

	&:hover {
		background-color: color('grey', 10);
	}

	svg {
		--icon-size: #{px-to(16px, rem)};
		color: color('grey', 60);
	}

	span {
		font-size: px-to(14px, rem);
		color: color('grey', 90);
	}
}
