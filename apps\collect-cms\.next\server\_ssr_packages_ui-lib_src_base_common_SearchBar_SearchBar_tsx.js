/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_SearchBar_SearchBar_tsx";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_SearchBar_SearchBar_tsx"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./searchbar.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\");\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst SearchBar = ({ cid, Headline })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading--h3\"),\n                        children: Headline\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__bar)),\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__icon)),\n                            variant: \"search-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 17\n                        }, void 0),\n                        placeholder: \"Logo usages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                lineNumber: 13,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n        lineNumber: 11,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss":
/*!*****************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"searchbar_wrapper__lQzqp\",\n\t\"search__wrapper\": \"searchbar_search__wrapper__zhJNv\",\n\t\"search__bar\": \"searchbar_search__bar__me_P5\"\n};\n\nmodule.exports.__checksum = \"c5c9e546ee9e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9TZWFyY2hCYXIvc2VhcmNoYmFyLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1NlYXJjaEJhci9zZWFyY2hiYXIubW9kdWxlLnNjc3M/MGNlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwic2VhcmNoYmFyX3dyYXBwZXJfX2xRenFwXCIsXG5cdFwic2VhcmNoX193cmFwcGVyXCI6IFwic2VhcmNoYmFyX3NlYXJjaF9fd3JhcHBlcl9femhKTnZcIixcblx0XCJzZWFyY2hfX2JhclwiOiBcInNlYXJjaGJhcl9zZWFyY2hfX2Jhcl9fbWVfUDVcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiYzVjOWU1NDZlZTllXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\n");

/***/ })

};
;