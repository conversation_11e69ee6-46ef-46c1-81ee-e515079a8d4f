'use client'

import type { IComponentProps } from '@collective/integration-lib/cms'
import dynamic from 'next/dynamic'
import type { ComponentType } from 'react'

type WrapperData = {
	commonData?: object
	data: IComponentProps
}

export const Wrapper = ({ commonData, data }: WrapperData) => {
	const moduleName = data.__component && data.__component.split('.')[0]
	const componentName = data.__component && data.__component.split('.')[1]

	if (!moduleName || !componentName) return null

	const fieldName: string = componentName
		.toLowerCase()
		.replace(/(^|[\s\-_])([a-z])/g, (_, p1, letter) => letter.toUpperCase())
		.replace(/[^a-z0-9]/gi, '')

	const Component: ComponentType<IComponentProps> = dynamic(() =>
		import(`./${moduleName}`)
			.then((module) => {
				// Ensure that the named export exists
				if (module && module[fieldName]) {
					return module[fieldName]
				}
				throw new Error(`Component ${data.__component} failed to import/load`)
			})
			.catch((error) => {
				console.error(error)
				// Return a fallback component in case of failure
				// eslint-disable-next-line react/display-name
				return () => (
					<p>
						Component <b>{data.__component}</b> failed to import/load
					</p>
				)
			})
	)

	if (!Component) return null

	return <Component {...commonData} {...data} />
}

export const CmsWrapper = ({ module }: { module: string }) => {
	const moduleName = module && module.split('.')[0]
	const componentName = module && module.split('.')[1]

	if (!moduleName || !componentName) return ''

	const fieldName: string = componentName
		.toLowerCase()
		.replace(/(^|[\s\-_])([a-z])/g, (_, p1, letter) => letter.toUpperCase())
		.replace(/[^a-z0-9]/gi, '')

	const Component: ComponentType<IComponentProps> = dynamic(() =>
		import(`@collective/ui-lib/base/${moduleName}`)
			.then((mod) => {
				// Ensure that the named export exists
				if (mod && mod[fieldName]) {
					return mod[fieldName]
				}
				throw new Error(`Component ${module} failed to import/load`)
			})
			.catch((error) => {
				console.error(error)
				// Return a fallback component in case of failure
				// eslint-disable-next-line react/display-name
				return () => (
					<p>
						Component <b>{module}</b> failed to import/load
					</p>
				)
			})
	)

	return Component
}
