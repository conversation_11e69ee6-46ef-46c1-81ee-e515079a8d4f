'use client'
// Custom Hook
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { useRef, type DependencyList, type MutableRefObject } from 'react'
import { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'

export interface AnimVars {
	config?: GSAPTimelineVars
	timeline: {
		targets: gsap.TweenTarget
		vars: { [key: string]: gsap.TweenVars | gsap.TweenVars[] }
		config?: GSAPTimelineVars
		position?: gsap.Position
		matchMedia?: string
	}[]
	deps?: DependencyList
}

type ReturnVars = [ref: MutableRefObject<null>, tl: GSAPTimeline]

export const useAnim = ({ config, timeline, deps }: AnimVars): ReturnVars => {
	// create ref using useState and then we use pass our setRef function to the ref of object
	// we want to animate
	const ref = useRef(null)

	// memoise the inital timeline in a ref so it doesnt get recreated each render.
	const tl = useRef<GSAPTimeline>(gsap.timeline({ paused: true }))

	useIsomorphicLayoutEffect(() => {
		gsap.registerPlugin(ScrollTrigger)
		tl.current = gsap.timeline(config)
	}, [config])

	useIsomorphicLayoutEffect(
		() => {
			if (!ref.current) return
			const context = gsap.context(() => {
				const selector = gsap.utils.selector(ref)

				const mm = gsap.matchMedia()

				timeline.forEach(({ targets, vars, position, config, matchMedia = '' }) => {
					mm.add(matchMedia, () => {
						let element
						// Check if ref itself matches the target
						if (ref.current && (ref.current as HTMLElement).matches(targets as string)) {
							element = [ref.current]
						} else {
							element = selector(targets as string)
						}

						if (!element || !element.length) return

						if (config) {
							const newTl = gsap.timeline(config)
							Object.keys(vars).forEach((value) => {
								const timelineVars = vars[value] as gsap.TweenVars
								if (value === 'fromTo') {
									newTl.fromTo(element, timelineVars[0], timelineVars[1], position)
								} else {
									newTl[value](element, timelineVars, position)
								}
							})
						} else {
							Object.keys(vars).forEach((value) => {
								const timelineVars = vars[value] as gsap.TweenVars
								if (value === 'fromTo') {
									tl.current.fromTo(element, timelineVars[0], timelineVars[1], position)
								} else {
									tl.current[value](element, timelineVars, position)
								}
							})
						}
					})
				})
			})

			return () => {
				context.revert()
			}
		},
		deps ? [...deps, ref] : [ref]
	)

	return [ref, tl.current]
}
