/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockHorizon_index_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockHorizon_index_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx":
/*!***************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockHorizon: () => (/* binding */ BlockHorizon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockhorizon.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss\");\n/* harmony import */ var _blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst BlockHorizon = ({ cid, isWrapContent, Blocks })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\", isWrapContent ? \"\" : \"unwrap__wrapper\"),\n            children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(isWrapContent ? \"\" : \"unwrap\", (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block)),\n                    children: [\n                        item.Title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\", (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title)),\n                            children: item.Title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 7\n                        }, undefined),\n                        item.Content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: item.Content\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 8\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, idx, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 5\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/index.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockHorizon: () => (/* reexport safe */ _BlockHorizon__WEBPACK_IMPORTED_MODULE_0__.BlockHorizon)\n/* harmony export */ });\n/* harmony import */ var _BlockHorizon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0hvcml6b24vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0Jsb2NrSG9yaXpvbi9pbmRleC50cz9jYzY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vQmxvY2tIb3Jpem9uJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockhorizon_wrapper__Z8BFI\",\n\t\"block\": \"blockhorizon_block__NfrMu\",\n\t\"block__title\": \"blockhorizon_block__title__N0rAr\",\n\t\"block__content\": \"blockhorizon_block__content__9MijM\"\n};\n\nmodule.exports.__checksum = \"e0b99015f311\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0hvcml6b24vYmxvY2tob3Jpem9uLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tIb3Jpem9uL2Jsb2NraG9yaXpvbi5tb2R1bGUuc2Nzcz9jZTE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2hvcml6b25fd3JhcHBlcl9fWjhCRklcIixcblx0XCJibG9ja1wiOiBcImJsb2NraG9yaXpvbl9ibG9ja19fTmZyTXVcIixcblx0XCJibG9ja19fdGl0bGVcIjogXCJibG9ja2hvcml6b25fYmxvY2tfX3RpdGxlX19OMHJBclwiLFxuXHRcImJsb2NrX19jb250ZW50XCI6IFwiYmxvY2tob3Jpem9uX2Jsb2NrX19jb250ZW50X185TWlqTVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlMGI5OTAxNWYzMTFcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss\n");

/***/ })

};
;