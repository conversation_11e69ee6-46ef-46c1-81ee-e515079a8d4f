import { Accordion, AccordionItem, groupBy, Icon, Input } from '@collective/core'
import cn from 'classnames'
import { useContext, useMemo, useState, useCallback } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './component.module.scss'
import { ComponentDisplay } from './ComponentDisplay'

export const ComponentList = () => {
	const { contentType, components } = useContext(PageBuilderContext)
	const { data: globalComponents } = components ?? {}
	const { data: uidConfig } = contentType ?? {}
	const [searchKey, setSearchKey] = useState('')
	const [hoveredData, setHoveredData] = useState<{
		ref: (EventTarget & Element) | null
		data: (typeof globalComponents)[0] | null
	}>({
		ref: null,
		data: null,
	})

	// Get list available components
	const availComponents = useMemo(() => {
		if (!globalComponents || !uidConfig) return {}
		if (!uidConfig.schema.attributes.components) return {}
		if ('components' in uidConfig.schema.attributes.components === false) return {}

		const arrComponents = uidConfig.schema.attributes.components.components
		const filteredComponents = Object.values(globalComponents).filter(
			(component) =>
				arrComponents.includes(component.uid) &&
				component.schema.displayName.toLowerCase().includes(searchKey.toLowerCase())
		)

		const groupedComponents = groupBy(filteredComponents, 'category')

		return groupedComponents
	}, [uidConfig, globalComponents, searchKey])

	const handleDisplay = useCallback(
		(e?: React.MouseEvent | React.FocusEvent | null, data?: (typeof globalComponents)[0]) => {
			setHoveredData({
				ref: e?.currentTarget ?? null,
				data: data ?? null,
			})
		},
		[]
	)

	return (
		<div className={styles.wrapper}>
			<div className={styles.search}>
				<h4 className="collect__heading--h5">Components</h4>
				<Input
					onChange={(e) => setSearchKey(e.target.value)}
					startIcon={<Icon variant="search" type="cms" />}
					placeholder="Search..."
					className="collect__input "
				/>
			</div>
			<div className={cn(styles.components)} onScroll={handleSticky}>
				<Accordion>
					{Object.entries(availComponents).map((group, idx) => (
						<AccordionItem
							title={<span className="collect__body--md">{formatString(group[0])}</span>}
							key={idx}
							isActive={true}
							icon={<Icon variant="chevron-down" type="cms" />}
						>
							{group[1]?.map((component, index) => (
								<div
									className={styles.component}
									key={index}
									role="button"
									tabIndex={0}
									onFocus={(e) => handleDisplay(e, component)}
									onMouseOver={(e) => handleDisplay(e, component)}
									onMouseLeave={handleDisplay}
									onBlur={handleDisplay}
								>
									<p className="collect__body--xs">{component.schema.displayName}</p>
									<div className={styles.thumbnail} />
								</div>
							))}
						</AccordionItem>
					))}
				</Accordion>
			</div>
			{hoveredData.data && <ComponentDisplay data={hoveredData} />}
		</div>
	)
}

const formatString = (string: string) => {
	const fieldName = ((string.split('.')[1] as string) ?? string)
		.toLowerCase()
		.replace(/(^|[\s\-_])([a-z])/g, (_, p1, letter) => ' ' + letter.toUpperCase())

	return fieldName
}

const handleSticky = (e: React.UIEvent<HTMLDivElement>) => {
	const container = e.target as HTMLElement
	const sticky = container.querySelectorAll('.accordion__item')

	sticky.forEach((element) => {
		const stickyRect = element.getBoundingClientRect()
		const containerRect = container.getBoundingClientRect()

		element
			.querySelector('.accordion__trigger')
			?.classList.toggle('is__sticky', stickyRect.top < containerRect.top)
	})
}
