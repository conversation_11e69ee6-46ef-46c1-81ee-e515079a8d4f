/* eslint-disable @typescript-eslint/no-explicit-any */
import { createInstance, type i18n, type TFunction } from 'i18next'
import resourcesToBackend from 'i18next-resources-to-backend'
import { initReactI18next } from 'react-i18next/initReactI18next'
import { defaultNS, getOptions } from '../settings'

const initI18next = async (lng: string, ns: string | string[]) => {
	// on server side we create a new instance for each render, because during compilation everything seems to be executed in parallel
	const i18nInstance = createInstance()
	await i18nInstance
		.use(initReactI18next)
		.use(
			resourcesToBackend(
				(language: string, namespace: string) =>
					import(`../../public/locales/${language}/${namespace}.json`)
			)
		)
		.init(getOptions(lng, ns))
	return i18nInstance
}

export async function useServerTranslation(
	lng: string,
	ns: string | string[] = defaultNS,
	options: any = {}
): Promise<{
	t: TFunction<string, any>
	i18n: i18n
}> {
	const i18nextInstance = await initI18next(lng, ns)
	return {
		t: i18nextInstance.getFixedT(lng, Array.isArray(ns) ? ns[0] : ns, options.keyPrefix),
		i18n: i18nextInstance,
	}
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export type TFuncType = Awaited<ReturnType<typeof useServerTranslation>>['t']
