/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.blockhorizon_wrapper__Z8BFI {
  margin-bottom: var(--section-mg-btm);
}
.blockhorizon_wrapper__Z8BFI .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 90rem) {
  .blockhorizon_wrapper__Z8BFI .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.blockhorizon_wrapper__Z8BFI .unwrap__wrapper {
  gap: 3rem;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu {
  padding: 1.125rem 1.5rem 1.5rem;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  border-radius: 1rem;
  grid-column: span 4;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(1):nth-child(3n+1) {
  grid-column: span 12;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(2):nth-child(3n+1), .blockhorizon_wrapper__Z8BFI .blockhorizon_block__NfrMu:nth-last-child(1):nth-child(3n+2) {
  grid-column: span 6;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__title__N0rAr {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  font-weight: 500;
  color: #1d1d1f;
  line-height: 1.5;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  color: #6e6e73;
  line-height: 1.5;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM i,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM em {
  font-style: italic;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM b,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM strong {
  font-weight: 700;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM a,
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.blockhorizon_wrapper__Z8BFI .blockhorizon_block__content__9MijM del {
  text-decoration: line-through;
}
.blockhorizon_wrapper__Z8BFI .unwrap {
  padding: 0;
  background-color: transparent;
}
