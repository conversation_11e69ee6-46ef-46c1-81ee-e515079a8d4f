@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);

	.nav__wrapper {
		display: grid;
		gap: spacing(s5);

		.row__nav {
			display: grid;
			gap: spacing(s5);

			@include min-width('md') {
				// grid-template-columns: repeat(calc(var(--count)), 1fr);
				grid-template-columns: repeat(12, 1fr);
			}
		}

		.col__nav {
			background-color: color('white', 100);
			padding: px-to(40px, rem);
			border-radius: px-to(25px, rem);
			min-height: px-to(240px, rem);
			display: grid;
			gap: px-to(50px, rem);
			align-content: flex-start;
			grid-column: span 4; // 1 row 3 col

			&:nth-last-child(1):nth-child(3n + 1) {
				grid-column: span 12; // If have 1 col extra
			}

			&:nth-last-child(2):nth-child(3n + 1),
			&:nth-last-child(1):nth-child(3n + 2) {
				grid-column: span 6; // If have 2 cols extras
			}

			h2 {
				font-weight: 500;
			}
		}
	}

	.nav__list {
		display: grid;
		line-height: 180%;
		/* 36px */
		text-decoration-line: underline;
		text-decoration-style: solid;
		text-decoration-skip-ink: none;
		text-decoration-thickness: auto;
		text-underline-offset: auto;
		text-underline-position: from-font;
		color: color('neutral-gray');

		@include fluid($font-size) {
			font-size: size('paragraph', 'lg');
		}

		text-decoration: none;

		a {
			position: relative;

			&:hover::after {
				width: 100%;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: -2px;
				left: 0;
				height: px-to(1px, rem);
				width: 0;
				background-color: color('neutral-gray');
				transition: width 0.2s var(--ease-transition-2);
			}
		}
	}
}
