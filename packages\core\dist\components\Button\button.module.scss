@use '@/styles/config' as *; // ← customize the framework

.button {
	--button-text-color: #{currentColor};
	--button-bg-color: transparent;
	--icon-gap: #{spacing('s4')};
	--button-padding-x: #{spacing('s6')};
	--button-padding-y: #{spacing('s4')};
	--button-radius: #{spacing('s2')};

	justify-content: center;
	padding: var(--button-padding-y) var(--button-padding-x);
	border-radius: var(--button-radius);
	transition: 0.2s;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	display: inline-flex;
	align-items: center;
	color: var(--button-text-color);
	background-color: var(--button-bg-color);
	gap: var(--icon-gap);
	text-decoration: none;

	&.button--sm {
		font-size: px-to(12px, rem);
	}

	&.button--md {
		font-size: px-to(16px, rem);
	}

	&:active {
		transform: scale(0.95);
	}

	span {
		position: relative;
		z-index: 1;
		text-align: center;
	}

	&:disabled {
		pointer-events: none;
		cursor: not-allowed;
		opacity: 0.3;
	}
}
