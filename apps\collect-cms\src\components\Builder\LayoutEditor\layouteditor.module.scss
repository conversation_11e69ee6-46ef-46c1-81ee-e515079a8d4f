@import '@/styles/config';

.wrapper {
	padding-top: px-to(160px, rem);
	padding-bottom: px-to(160px, rem);
	display: grid;
	gap: spacing(s11);
	align-content: flex-start;

	.headline {
		width: 100%;
		font-size: px-to(64px, rem);
		font-weight: 700;
		line-height: 1.2;
		outline: 0;
		resize: none;
		overflow-y: hidden;
	}

	.header {
		display: grid;
		gap: spacing(s11);
		place-items: flex-start;

		button {
			cursor: pointer;
			color: color('grey', 40);

			&.add__image {
				--icon-gap: #{px-to(10px, rem)};
				svg {
					--icon-size: #{px-to(18px, rem)};
				}
			}

			&.add__block {
				svg {
					--icon-size: #{px-to(25px, rem)};
				}
			}
		}
	}
	.body {
		display: grid;

		button.add__block {
			display: flex;
			align-items: center;
			margin-top: spacing(s2);
			margin-bottom: spacing(s2);
			color: color('grey', 40);
			cursor: pointer;
			opacity: 0;

			&:hover,
			&:global(.active) {
				opacity: 1;
			}

			svg {
				--icon-size: #{px-to(25px, rem)};
			}

			.line {
				flex: 1;
				background-color: color('grey', 30);
				height: px-to(4px, rem);
			}
		}
	}

	.component__block {
		padding: spacing(s5);
		border: px-to(1px, rem) solid transparent;
		cursor: pointer;
		position: relative;

		&:hover {
			border-color: color('yellow', 70);
		}

		// * {
		// 	user-select: none;
		// 	pointer-events: none;
		// }
	}
}
