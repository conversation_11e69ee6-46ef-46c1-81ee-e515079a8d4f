@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	margin-top: spacing(s6);
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: px-to(1px, rem);
		background-color: color('divider');
	}

	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		padding-top: spacing(s12);

		@include min-width('2md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}

	.headline {
		font-weight: 500;
		color: color('black', 100);

		@include min-width('2md') {
			grid-column: span 6;
		}
	}

	.content {
		@include min-width('2md') {
			grid-column: span 6;
		}

		display: flex;
		flex-direction: column;
		gap: spacing(s4);

		.blockquote {
			color: color('black', 100);
		}

		.paragraph {
			color: color('neutral-gray');
			font-size: px-to(15px, rem);
		}

		ul {
			padding-left: spacing(s6);
			list-style-type: disc;
		}
		i,
		em {
			font-style: italic;
		}
		b,
		strong {
			font-weight: 700;
		}
		a,
		u {
			text-decoration: underline;
			text-decoration-skip-ink: none;
		}
		del {
			text-decoration: line-through;
		}
	}

	&:global(.nohead) {
		margin-top: 0;
		.content {
			@include min-width('2md') {
				grid-column-start: 7;
				grid-column-end: span 6;
			}
		}
		&::before {
			display: none;
		}
		:global(.aidigi__grid) {
			padding-top: 0;
		}
	}
}
