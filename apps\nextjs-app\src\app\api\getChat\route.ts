import { checkValidJWT, type IUserJWTProps } from '@collective/core'
import { getCmsData } from '@collective/integration-lib/cms'
import { cookies } from 'next/headers'

// export const runtime = 'edge'

type Props = {
	uuid: string
	updatedAt: string
	Message: { role: string; content: string; time: string }[]
	user: { id: number }
}

/**
 * Fetches chat data from Strapi based on UUID
 * @param uuid The unique identifier for the chat
 * @returns Promise with chat data
 */
export const getChatData = async (uuid: string) => {
	const response = await getCmsData<Props, 'multiple'>({
		path: `chats`,
		deep: 3,
		filter: `filters[uuid][$eq]=${uuid}`,
		revalidate: 0,
	})
	if (!response.data[0]) {
		return Promise.reject(new Error('Chat data not found'))
	}

	return response.data[0]
}

export async function GET(request: Request) {
	const { searchParams } = new URL(request.url)
	const uuid = searchParams.get('uuid')

	const cookieStore = cookies()
	const BearerToken = cookieStore.get('token')?.value
	if (!BearerToken) {
		return Response.json({ error: 'Invalid token' }, { status: 401 })
	}
	const check = await checkValidJWT<IUserJWTProps>(BearerToken)
	if (!check) {
		return Response.json({ error: 'Invalid token' }, { status: 401 })
	}

	// Nếu có uuid, lấy chat cụ thể
	if (!uuid) {
		const allChats = await getCmsData<Props, 'multiple'>({
			path: `chats`,
			deep: 3,
			filter: `filters[user][id][$eq]=${check.id}`,
			revalidate: 0,
		})

		const chats = allChats.data.map((chat) => ({
			id: chat.documentId,
			uuid: chat.uuid,
			messages: chat.Message.map((msg) => ({
				role: msg.role,
				content: msg.content,
				time: msg.time,
			})),
			updatedAt: chat.updatedAt,
		}))

		return Response.json({ chats }, { status: 200 })
	} else {
		const chatData = await getCmsData<Props, 'multiple'>({
			path: `chats`,
			deep: 3,
			filter: `filters[uuid][$eq]=${uuid}&filters[user][id][$eq]=${check.id}`,
			revalidate: 0,
		})

		if (!chatData.data[0]) {
			return Response.json({ error: 'Chat data not found' }, { status: 404 })
		}

		const id = chatData.data[0].documentId
		const messages = chatData.data[0].Message.map((msg) => ({
			role: msg.role,
			content: msg.content,
			time: msg.time,
		}))

		return Response.json({ id, messages }, { status: 200 })
	}
}
