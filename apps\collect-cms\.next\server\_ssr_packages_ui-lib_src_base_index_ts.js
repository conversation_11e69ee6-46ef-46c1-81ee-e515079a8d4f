/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_index_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_index_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* binding */ BlockContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockcontainer.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss\");\n/* harmony import */ var _blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\n\nconst BlockContainer = ({ cid, Align, Size, Blocks, isFirstSection })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content)),\n                style: {\n                    \"--position\": Size === \"Full\" ? 0 : 7,\n                    \"--total\": Size === \"Full\" ? 12 : 6\n                },\n                children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block), `block__${item.ColorVariant}`),\n                        style: {\n                            \"--layout\": Size === \"Full\" ? Align === \"Horizontal\" ? 6 : 12 : Align === \"Horizontal\" ? 3 : 6\n                        },\n                        children: [\n                            (item.Icon || item.Headline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title),\n                                children: [\n                                    item.Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                        variant: item.Icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 24\n                                    }, undefined),\n                                    item.Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\"),\n                                        children: item.Headline\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 28\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 9\n                            }, undefined),\n                            item.Paragraph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                    rehypePlugins: [\n                                        rehype_raw__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                    ],\n                                    children: item.Paragraph\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 10\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 7\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n            lineNumber: 28,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/index.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* reexport safe */ _BlockContainer__WEBPACK_IMPORTED_MODULE_0__.BlockContainer)\n/* harmony export */ });\n/* harmony import */ var _BlockContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContainer */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRhaW5lci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250YWluZXIvaW5kZXgudHM/NTg3OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL0Jsb2NrQ29udGFpbmVyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx":
/*!***************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContent: () => (/* binding */ BlockContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockcontent.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss\");\n/* harmony import */ var _blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst BlockContent = ({ cid, Headline, Variant, Blocks, isFirstSection })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper), Headline ? \"\" : \"nohead\", isFirstSection ? \"first__section\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: [\n                Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading\"),\n                    children: Headline\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 17\n                }, undefined),\n                Blocks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content), Variant === \"grid\" ? (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content__grid) : \"\"),\n                    style: {\n                        \"--isHasHeadline\": Headline ? 0 : 7\n                    },\n                    children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block)),\n                            children: [\n                                item.Title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\", (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title)),\n                                    children: item.Title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 9\n                                }, undefined),\n                                item.Content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_blockcontent_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                        rehypePlugins: [\n                                            rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                        ],\n                                        children: item.Content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 10\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 7\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n            lineNumber: 27,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContent\\\\BlockContent.tsx\",\n        lineNumber: 23,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/index.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/index.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContent: () => (/* reexport safe */ _BlockContent__WEBPACK_IMPORTED_MODULE_0__.BlockContent)\n/* harmony export */ });\n/* harmony import */ var _BlockContent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContent */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContent/BlockContent.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRlbnQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0Jsb2NrQ29udGVudC9pbmRleC50cz80M2JiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vQmxvY2tDb250ZW50J1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx":
/*!***************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockHorizon: () => (/* binding */ BlockHorizon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockhorizon.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss\");\n/* harmony import */ var _blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst BlockHorizon = ({ cid, isWrapContent, Blocks })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\", isWrapContent ? \"\" : \"unwrap__wrapper\"),\n            children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(isWrapContent ? \"\" : \"unwrap\", (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block)),\n                    children: [\n                        item.Title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\", (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title)),\n                            children: item.Title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 7\n                        }, undefined),\n                        item.Content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_blockhorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: item.Content\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 8\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, idx, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 5\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockHorizon\\\\BlockHorizon.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/index.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockHorizon: () => (/* reexport safe */ _BlockHorizon__WEBPACK_IMPORTED_MODULE_0__.BlockHorizon)\n/* harmony export */ });\n/* harmony import */ var _BlockHorizon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/BlockHorizon.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0hvcml6b24vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0Jsb2NrSG9yaXpvbi9pbmRleC50cz9jYzY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vQmxvY2tIb3Jpem9uJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Color/Color.tsx":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/Color.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(ssr)/../../packages/core/dist/utils/throttle-debounce.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./color.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss\");\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_color_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Color auto */ \n\n\n\nconst Color = ({ Colors })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().row),\n                style: {\n                    \"--count\": Colors.length\n                },\n                children: Colors?.map((color, idx)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().column),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BgColor, {\n                                color: color.HexColor\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().description),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n                                        children: color.ColorName\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 9\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"hex:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 11\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: color.HexColor\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 11\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 10\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"rgb:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 11\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: hexToRgb(color.HexColor)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 11\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 10\n                                            }, undefined),\n                                            color?.AdditionalField?.map((field, fieldIdx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: [\n                                                                field.FieldName,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 12\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: field.FieldValue\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 12\n                                                        }, undefined)\n                                                    ]\n                                                }, fieldIdx, true, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 11\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 9\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 7\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                lineNumber: 21,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 20,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 19,\n        columnNumber: 2\n    }, undefined);\nconst hexToRgb = (hex)=>{\n    // Remove the \"#\" if present\n    hex = hex.replace(/^#/, \"\");\n    // Parse the hex values\n    let r, g, b;\n    if (hex.length === 3) {\n        // Short format (#RGB)\n        r = parseInt(hex[0] + hex[0], 16);\n        g = parseInt(hex[1] + hex[1], 16);\n        b = parseInt(hex[2] + hex[2], 16);\n    } else if (hex.length === 6) {\n        // Full format (#RRGGBB)\n        r = parseInt(hex.substring(0, 2), 16);\n        g = parseInt(hex.substring(2, 4), 16);\n        b = parseInt(hex.substring(4, 6), 16);\n    } else {\n        throw new Error(\"Invalid HEX color.\");\n    }\n    return `${r}, ${g}, ${b}`;\n};\nconst BgColor = ({ color })=>{\n    const [isCopied, setIsCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const lazyCopy = (0,_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_3__.debounce)(()=>{\n        setIsCopied(false);\n    }, 250);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onKeyDown: ()=>{\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        tabIndex: 0,\n        role: \"button\",\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().bg__color),\n        style: {\n            background: color\n        },\n        onClick: ()=>{\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().tag),\n            children: isCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                        variant: \"copy\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 7\n                    }, undefined),\n                    \" Copied\"\n                ]\n            }, void 0, true) : color\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 108,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 90,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Color/Color.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Color/index.ts":
/*!************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/index.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* reexport safe */ _Color__WEBPACK_IMPORTED_MODULE_0__.Color)\n/* harmony export */ });\n/* harmony import */ var _Color__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Color */ \"(ssr)/../../packages/ui-lib/src/base/common/Color/Color.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9Db2xvci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQ29sb3IvaW5kZXgudHM/OGRmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL0NvbG9yJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Color/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Divider/Divider.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Divider/Divider.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Divider: () => (/* binding */ Divider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _divider_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./divider.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Divider/divider.module.scss\");\n/* harmony import */ var _divider_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_divider_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Divider = ({ cid, Variant = \"blank\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_divider_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper), \"ai__layout ai__grid\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"divider\", (_divider_module_scss__WEBPACK_IMPORTED_MODULE_2___default())[`divider__${Variant}`])\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Divider\\\\Divider.tsx\",\n            lineNumber: 11,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Divider\\\\Divider.tsx\",\n        lineNumber: 10,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9EaXZpZGVyL0RpdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTJCO0FBQ2U7QUFPbkMsTUFBTUUsVUFBVSxDQUFDLEVBQUVDLEdBQUcsRUFBRUMsVUFBVSxPQUFPLEVBQWdCLGlCQUMvRCw4REFBQ0M7UUFBUUMsSUFBSUg7UUFBS0ksV0FBV1AsaURBQUVBLENBQUNDLHFFQUFjLEVBQUU7a0JBQy9DLDRFQUFDUTtZQUFJRixXQUFXUCxpREFBRUEsQ0FBQyxXQUFXQyw2REFBTSxDQUFDLENBQUMsU0FBUyxFQUFFRyxRQUFRLENBQUMsQ0FBQzs7Ozs7Ozs7OztrQkFFNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0RpdmlkZXIvRGl2aWRlci50c3g/YjM2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY24gZnJvbSAnY2xhc3NuYW1lcydcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi9kaXZpZGVyLm1vZHVsZS5zY3NzJ1xuXG50eXBlIERpdmlkZXJQcm9wcyA9IHtcblx0Y2lkPzogc3RyaW5nXG5cdFZhcmlhbnQ6IHN0cmluZyAvLyAnQmxhbmsnIHwgJ0xpbmUnXG59XG5cbmV4cG9ydCBjb25zdCBEaXZpZGVyID0gKHsgY2lkLCBWYXJpYW50ID0gJ2JsYW5rJyB9OiBEaXZpZGVyUHJvcHMpID0+IChcblx0PHNlY3Rpb24gaWQ9e2NpZH0gY2xhc3NOYW1lPXtjbihzdHlsZXMud3JhcHBlciwgJ2FpX19sYXlvdXQgYWlfX2dyaWQnKX0+XG5cdFx0PGRpdiBjbGFzc05hbWU9e2NuKCdkaXZpZGVyJywgc3R5bGVzW2BkaXZpZGVyX18ke1ZhcmlhbnR9YF0pfT48L2Rpdj5cblx0PC9zZWN0aW9uPlxuKVxuIl0sIm5hbWVzIjpbImNuIiwic3R5bGVzIiwiRGl2aWRlciIsImNpZCIsIlZhcmlhbnQiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJ3cmFwcGVyIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Divider/Divider.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Divider/index.ts":
/*!**************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Divider/index.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Divider: () => (/* reexport safe */ _Divider__WEBPACK_IMPORTED_MODULE_0__.Divider)\n/* harmony export */ });\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Divider */ \"(ssr)/../../packages/ui-lib/src/base/common/Divider/Divider.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9EaXZpZGVyL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9EaXZpZGVyL2luZGV4LnRzPzE3NDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9EaXZpZGVyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Divider/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx":
/*!*****************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GuidelineLink: () => (/* binding */ GuidelineLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./guidelinelink.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss\");\n/* harmony import */ var _guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\nconst GuidelineLink = ({ cid, Variant, Links })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().content)),\n                style: {\n                    \"--position\": Variant == \"fill\" ? 0 : 7,\n                    \"--total\": Variant == \"fill\" ? 12 : 6\n                },\n                children: Links?.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link)),\n                        style: {\n                            \"--width\": Variant == \"fill\" ? 6 : 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.Link,\n                            target: \"_blank\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                    variant: \"book-open\",\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link__icon))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 8\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_guidelinelink_module_scss__WEBPACK_IMPORTED_MODULE_3___default().link__label)),\n                                    children: item.Label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 8\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 7\n                        }, undefined)\n                    }, idx, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 6\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n                lineNumber: 18,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\GuidelineLink\\\\GuidelineLink.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/index.ts":
/*!********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/index.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GuidelineLink: () => (/* reexport safe */ _GuidelineLink__WEBPACK_IMPORTED_MODULE_0__.GuidelineLink)\n/* harmony export */ });\n/* harmony import */ var _GuidelineLink__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GuidelineLink */ \"(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/GuidelineLink.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9HdWlkZWxpbmVMaW5rL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9HdWlkZWxpbmVMaW5rL2luZGV4LnRzP2QyODkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9HdWlkZWxpbmVMaW5rJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Header/Header.tsx":
/*!***************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/Header.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss\");\n/* harmony import */ var _header_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_header_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst Header = ({ cid, Headline, Subhead, ActionButton })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            style: {\n                alignItems: !Subhead ? \"center\" : \"\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline),\n                    children: [\n                        Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h1\"),\n                            children: Headline\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 18\n                        }, undefined),\n                        Subhead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading--h3\"),\n                            children: Subhead\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 4\n                }, undefined),\n                ActionButton?.ButtonText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().download),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"aidigi__button outline\",\n                        href: ActionButton?.ButtonLink,\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_header_module_scss__WEBPACK_IMPORTED_MODULE_2___default().arrowdown)),\n                            variant: \"arrow-down\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 18\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"vc__paragraph--md\",\n                            children: ActionButton?.ButtonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n            lineNumber: 17,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Header\\\\Header.tsx\",\n        lineNumber: 16,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Header/Header.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Header/index.ts":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/index.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_0__.Header)\n/* harmony export */ });\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Header */ \"(ssr)/../../packages/ui-lib/src/base/common/Header/Header.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9IZWFkZXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9pbmRleC50cz83NzI0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vSGVhZGVyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Header/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Media/Media.tsx":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/Media.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: () => (/* binding */ Media)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Image!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Image!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./media.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\nconst Media = ({ Media, IsFixedHeight = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            style: {\n                \"--count\": Media.map((item)=>item).length\n            },\n            children: Media?.map((item, index)=>{\n                const media = item?.Media;\n                const mediaUrl = `${\"https://ai-digital-brand-cms-smooth.gocollectives.com\"}${media?.url}?original=true&download=true`;\n                const { DefaultBackground } = item;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().media), item.IsFullScale && (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().full__scale), !IsFixedHeight && (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().auto__height)),\n                            style: DefaultBackground ? {\n                                backgroundColor: item.DefaultBackground\n                            } : {},\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().inner),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        media: item.Media,\n                                        placeholder: \"empty\",\n                                        alt: item.Media.caption || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 10\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 9\n                                }, undefined),\n                                item?.IsDownloadable && media?.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    target: \"_blank\",\n                                    href: mediaUrl,\n                                    download: true,\n                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_3___default().download),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Image_collective_core__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                        variant: \"arrow-down\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 11\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 10\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 8\n                        }, undefined),\n                        media?.caption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"aidigi__paragraph--md\",\n                            children: media?.caption\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 27\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 7\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n            lineNumber: 20,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Media\\\\Media.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Media/Media.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Media/index.ts":
/*!************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/index.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: () => (/* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.Media)\n/* harmony export */ });\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Media */ \"(ssr)/../../packages/ui-lib/src/base/common/Media/Media.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9NZWRpYS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vTWVkaWEvaW5kZXgudHM/MjkwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL01lZGlhJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Media/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationWrap: () => (/* binding */ NavigationWrap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./navigationwrap.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n// import { useIsomorphicLayoutEffect } from '@collective/core'\n// import type { INavigationWrapProps } from '@collective/integration-lib/cms'\n// import { searchNavigation } from '@collective/integration-lib/search/meilisearch'\n\n\n// import type { Hits } from 'meilisearch'\n\n// import { useState } from 'react'\n\nconst NavigationWrap = ({ cid, List: List0 })=>{\n    // Strapi V5's bug that shows both draft and published data\n    const List = List0.filter((item)=>item.publishedAt !== null);\n    // const [navigation, setNavigation] = useState<Hits<INavigationProps>>([\n    // \t{\n    // \t\tid: -99,\n    // \t\tHeadline: '',\n    // \t\tslug: '',\n    // \t\tPages: [\n    // \t\t\t{\n    // \t\t\t\tHeadline: '',\n    // \t\t\t\tslug: '',\n    // \t\t\t},\n    // \t\t],\n    // \t},\n    // ])\n    // const searchNavigationWrap = async () => {\n    // \tconst data = await searchNavigation('', {\n    // \t\thitsPerPage: List.data.attributes.map((item) => item).length,\n    // \t\tpage: 1,\n    // \t})\n    // \tsetNavigation(data.hits)\n    // }\n    // useIsomorphicLayoutEffect(() => {\n    // \tsearchNavigationWrap()\n    // }, [])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__wrapper),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().row__nav),\n                    children: List?.map((item, idx)=>{\n                        const { Headline, slug, Pages } = item;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().col__nav),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"aidigi__heading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: `/${slug}`,\n                                        children: Headline\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 11\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 10\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__list),\n                                    style: {\n                                        gridTemplateColumns: // List divide 2 columns for (n - 1) % 3 === 0 and last item\n                                        idx % 3 === 0 && idx === List.map((item)=>item).length - 1 ? `repeat(2, 1fr)` : \"\"\n                                    },\n                                    children: Pages?.map((child, count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: `/${child.slug}/${child.slug}`,\n                                                children: child.Headline\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        }, count, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 12\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 10\n                                }, undefined)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 6\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                lineNumber: 54,\n                columnNumber: 5\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n            lineNumber: 53,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/index.ts":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/index.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationWrap: () => (/* reexport safe */ _NavigationWrap__WEBPACK_IMPORTED_MODULE_0__.NavigationWrap)\n/* harmony export */ });\n/* harmony import */ var _NavigationWrap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NavigationWrap */ \"(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9OYXZpZ2F0aW9uV3JhcC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vTmF2aWdhdGlvbldyYXAvaW5kZXgudHM/NzlkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL05hdmlnYXRpb25XcmFwJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./searchbar.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\");\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst SearchBar = ({ cid, Headline })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading--h3\"),\n                        children: Headline\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__bar)),\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__icon)),\n                            variant: \"search-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 17\n                        }, void 0),\n                        placeholder: \"Logo usages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                lineNumber: 13,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n        lineNumber: 11,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/SearchBar/index.ts":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/index.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_0__.SearchBar)\n/* harmony export */ });\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SearchBar */ \"(ssr)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9TZWFyY2hCYXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1NlYXJjaEJhci9pbmRleC50cz9iZTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vU2VhcmNoQmFyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/SearchBar/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextHorizon: () => (/* binding */ TextHorizon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./texthorizon.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss\");\n/* harmony import */ var _texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst TextHorizon = ({ cid, Headline, BlockQuote, Paragraph, isFirstSection })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper), Headline ? \"\" : \"nohead\", isFirstSection ? \"first__section\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: [\n                Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading\"),\n                    children: Headline\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content)),\n                    children: [\n                        BlockQuote && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().blockquote), \"aidigi__paragraph--lg\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: BlockQuote\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 6\n                        }, undefined),\n                        Paragraph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_texthorizon_module_scss__WEBPACK_IMPORTED_MODULE_2___default().paragraph), \"aidigi__paragraph--md\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                rehypePlugins: [\n                                    rehype_raw__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                ],\n                                children: Paragraph\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 6\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 4\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n            lineNumber: 24,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\TextHorizon\\\\TextHorizon.tsx\",\n        lineNumber: 20,\n        columnNumber: 2\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts":
/*!******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/index.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextHorizon: () => (/* reexport safe */ _TextHorizon__WEBPACK_IMPORTED_MODULE_0__.TextHorizon)\n/* harmony export */ });\n/* harmony import */ var _TextHorizon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/TextHorizon.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9UZXh0SG9yaXpvbi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVGV4dEhvcml6b24vaW5kZXgudHM/MzU0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL1RleHRIb3Jpem9uJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/index.ts":
/*!******************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/index.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* reexport safe */ _BlockContainer__WEBPACK_IMPORTED_MODULE_0__.BlockContainer),\n/* harmony export */   BlockContent: () => (/* reexport safe */ _BlockContent__WEBPACK_IMPORTED_MODULE_4__.BlockContent),\n/* harmony export */   BlockHorizon: () => (/* reexport safe */ _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__.BlockHorizon),\n/* harmony export */   Color: () => (/* reexport safe */ _Color__WEBPACK_IMPORTED_MODULE_5__.Color),\n/* harmony export */   Divider: () => (/* reexport safe */ _Divider__WEBPACK_IMPORTED_MODULE_7__.Divider),\n/* harmony export */   GuidelineLink: () => (/* reexport safe */ _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__.GuidelineLink),\n/* harmony export */   Header: () => (/* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_9__.Header),\n/* harmony export */   Media: () => (/* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_6__.Media),\n/* harmony export */   NavigationWrap: () => (/* reexport safe */ _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__.NavigationWrap),\n/* harmony export */   SearchBar: () => (/* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_1__.SearchBar),\n/* harmony export */   TextHorizon: () => (/* reexport safe */ _TextHorizon__WEBPACK_IMPORTED_MODULE_8__.TextHorizon)\n/* harmony export */ });\n/* harmony import */ var _BlockContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContainer */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts\");\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SearchBar */ \"(ssr)/../../packages/ui-lib/src/base/common/SearchBar/index.ts\");\n/* harmony import */ var _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GuidelineLink */ \"(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/index.ts\");\n/* harmony import */ var _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BlockHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts\");\n/* harmony import */ var _BlockContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BlockContent */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContent/index.ts\");\n/* harmony import */ var _Color__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Color */ \"(ssr)/../../packages/ui-lib/src/base/common/Color/index.ts\");\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Media */ \"(ssr)/../../packages/ui-lib/src/base/common/Media/index.ts\");\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Divider */ \"(ssr)/../../packages/ui-lib/src/base/common/Divider/index.ts\");\n/* harmony import */ var _TextHorizon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TextHorizon */ \"(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Header */ \"(ssr)/../../packages/ui-lib/src/base/common/Header/index.ts\");\n/* harmony import */ var _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NavigationWrap */ \"(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/index.ts\");\n// Export modules\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsaUJBQWlCO0FBQ2U7QUFDTDtBQUNJO0FBQ0Q7QUFDQTtBQUNQO0FBQ0E7QUFDRTtBQUNJO0FBQ0w7QUFDUSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vaW5kZXgudHM/ODAxYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnQgbW9kdWxlc1xuZXhwb3J0ICogZnJvbSAnLi9CbG9ja0NvbnRhaW5lcidcbmV4cG9ydCAqIGZyb20gJy4vU2VhcmNoQmFyJ1xuZXhwb3J0ICogZnJvbSAnLi9HdWlkZWxpbmVMaW5rJ1xuZXhwb3J0ICogZnJvbSAnLi9CbG9ja0hvcml6b24nXG5leHBvcnQgKiBmcm9tICcuL0Jsb2NrQ29udGVudCdcbmV4cG9ydCAqIGZyb20gJy4vQ29sb3InXG5leHBvcnQgKiBmcm9tICcuL01lZGlhJ1xuZXhwb3J0ICogZnJvbSAnLi9EaXZpZGVyJ1xuZXhwb3J0ICogZnJvbSAnLi9UZXh0SG9yaXpvbidcbmV4cG9ydCAqIGZyb20gJy4vSGVhZGVyJ1xuZXhwb3J0ICogZnJvbSAnLi9OYXZpZ2F0aW9uV3JhcCdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/index.ts":
/*!***********************************************!*\
  !*** ../../packages/ui-lib/src/base/index.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContainer),\n/* harmony export */   BlockContent: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContent),\n/* harmony export */   BlockHorizon: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockHorizon),\n/* harmony export */   Color: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Color),\n/* harmony export */   Divider: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Divider),\n/* harmony export */   GuidelineLink: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.GuidelineLink),\n/* harmony export */   Header: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Header),\n/* harmony export */   Media: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Media),\n/* harmony export */   NavigationWrap: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.NavigationWrap),\n/* harmony export */   SearchBar: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.SearchBar),\n/* harmony export */   TextHorizon: () => (/* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.TextHorizon)\n/* harmony export */ });\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common */ \"(ssr)/../../packages/ui-lib/src/base/common/index.ts\");\n // Wrapper should not be exported and needs to be import directly\n // for support dynamic imports with next/dynamic\n // export { Wrapper } from './Wrapper'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF3QixDQUN4QixpRUFBaUU7Q0FDakUsZ0RBQWdEO0NBQ2hELHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9pbmRleC50cz9jYzRmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vY29tbW9uJ1xuLy8gV3JhcHBlciBzaG91bGQgbm90IGJlIGV4cG9ydGVkIGFuZCBuZWVkcyB0byBiZSBpbXBvcnQgZGlyZWN0bHlcbi8vIGZvciBzdXBwb3J0IGR5bmFtaWMgaW1wb3J0cyB3aXRoIG5leHQvZHluYW1pY1xuLy8gZXhwb3J0IHsgV3JhcHBlciB9IGZyb20gJy4vV3JhcHBlcidcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontainer_wrapper__9pM15\",\n\t\"content\": \"blockcontainer_content__CVQDR\",\n\t\"block\": \"blockcontainer_block__FgkM7\",\n\t\"block__title\": \"blockcontainer_block__title__yzrl_\",\n\t\"block__content\": \"blockcontainer_block__content__pprpc\"\n};\n\nmodule.exports.__checksum = \"73f097d7ba0e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRhaW5lci9ibG9ja2NvbnRhaW5lci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250YWluZXIvYmxvY2tjb250YWluZXIubW9kdWxlLnNjc3M/NDVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiYmxvY2tjb250YWluZXJfd3JhcHBlcl9fOXBNMTVcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250YWluZXJfY29udGVudF9fQ1ZRRFJcIixcblx0XCJibG9ja1wiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19GZ2tNN1wiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX190aXRsZV9feXpybF9cIixcblx0XCJibG9ja19fY29udGVudFwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19jb250ZW50X19wcHJwY1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI3M2YwOTdkN2JhMGVcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontent_wrapper__8O_bU\",\n\t\"headline\": \"blockcontent_headline__cMJyZ\",\n\t\"content\": \"blockcontent_content__KH33h\",\n\t\"content__grid\": \"blockcontent_content__grid__GajH2\",\n\t\"block\": \"blockcontent_block__8eue2\",\n\t\"block__title\": \"blockcontent_block__title__Ck2bf\",\n\t\"block__content\": \"blockcontent_block__content__CS7WJ\"\n};\n\nmodule.exports.__checksum = \"7e7468d3bd30\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRlbnQvYmxvY2tjb250ZW50Lm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250ZW50L2Jsb2NrY29udGVudC5tb2R1bGUuc2Nzcz8yZGM3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2NvbnRlbnRfd3JhcHBlcl9fOE9fYlVcIixcblx0XCJoZWFkbGluZVwiOiBcImJsb2NrY29udGVudF9oZWFkbGluZV9fY01KeVpcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250ZW50X2NvbnRlbnRfX0tIMzNoXCIsXG5cdFwiY29udGVudF9fZ3JpZFwiOiBcImJsb2NrY29udGVudF9jb250ZW50X19ncmlkX19HYWpIMlwiLFxuXHRcImJsb2NrXCI6IFwiYmxvY2tjb250ZW50X2Jsb2NrX184ZXVlMlwiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGVudF9ibG9ja19fdGl0bGVfX0NrMmJmXCIsXG5cdFwiYmxvY2tfX2NvbnRlbnRcIjogXCJibG9ja2NvbnRlbnRfYmxvY2tfX2NvbnRlbnRfX0NTN1dKXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjdlNzQ2OGQzYmQzMFwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContent/blockcontent.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss":
/*!***********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockhorizon_wrapper__Z8BFI\",\n\t\"block\": \"blockhorizon_block__NfrMu\",\n\t\"block__title\": \"blockhorizon_block__title__N0rAr\",\n\t\"block__content\": \"blockhorizon_block__content__9MijM\"\n};\n\nmodule.exports.__checksum = \"e0b99015f311\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0hvcml6b24vYmxvY2tob3Jpem9uLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tIb3Jpem9uL2Jsb2NraG9yaXpvbi5tb2R1bGUuc2Nzcz9jZTE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJibG9ja2hvcml6b25fd3JhcHBlcl9fWjhCRklcIixcblx0XCJibG9ja1wiOiBcImJsb2NraG9yaXpvbl9ibG9ja19fTmZyTXVcIixcblx0XCJibG9ja19fdGl0bGVcIjogXCJibG9ja2hvcml6b25fYmxvY2tfX3RpdGxlX19OMHJBclwiLFxuXHRcImJsb2NrX19jb250ZW50XCI6IFwiYmxvY2tob3Jpem9uX2Jsb2NrX19jb250ZW50X185TWlqTVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlMGI5OTAxNWYzMTFcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockHorizon/blockhorizon.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/color.module.scss ***!
  \*********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"color_wrapper__yuBaS\",\n\t\"row\": \"color_row__afjAf\",\n\t\"bg__color\": \"color_bg__color__UyYaA\",\n\t\"tag\": \"color_tag__OFAjQ\",\n\t\"description\": \"color_description__pExio\"\n};\n\nmodule.exports.__checksum = \"efb6142de78d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9Db2xvci9jb2xvci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQ29sb3IvY29sb3IubW9kdWxlLnNjc3M/MDlkZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiY29sb3Jfd3JhcHBlcl9feXVCYVNcIixcblx0XCJyb3dcIjogXCJjb2xvcl9yb3dfX2FmakFmXCIsXG5cdFwiYmdfX2NvbG9yXCI6IFwiY29sb3JfYmdfX2NvbG9yX19VeVlhQVwiLFxuXHRcInRhZ1wiOiBcImNvbG9yX3RhZ19fT0ZBalFcIixcblx0XCJkZXNjcmlwdGlvblwiOiBcImNvbG9yX2Rlc2NyaXB0aW9uX19wRXhpb1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlZmI2MTQyZGU3OGRcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Color/color.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Divider/divider.module.scss":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Divider/divider.module.scss ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"divider_wrapper__qemC4\",\n\t\"divider\": \"divider_divider__N49md\",\n\t\"divider__blank\": \"divider_divider__blank__KPxy4\",\n\t\"divider__line\": \"divider_divider__line__sHQyg\"\n};\n\nmodule.exports.__checksum = \"0f0160253827\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9EaXZpZGVyL2RpdmlkZXIubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9EaXZpZGVyL2RpdmlkZXIubW9kdWxlLnNjc3M/NGIwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiZGl2aWRlcl93cmFwcGVyX19xZW1DNFwiLFxuXHRcImRpdmlkZXJcIjogXCJkaXZpZGVyX2RpdmlkZXJfX040OW1kXCIsXG5cdFwiZGl2aWRlcl9fYmxhbmtcIjogXCJkaXZpZGVyX2RpdmlkZXJfX2JsYW5rX19LUHh5NFwiLFxuXHRcImRpdmlkZXJfX2xpbmVcIjogXCJkaXZpZGVyX2RpdmlkZXJfX2xpbmVfX3NIUXlnXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjBmMDE2MDI1MzgyN1wiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Divider/divider.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss":
/*!*************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"guidelinelink_wrapper__2aoiX\",\n\t\"content\": \"guidelinelink_content__dM8Jn\",\n\t\"link\": \"guidelinelink_link__087_f\",\n\t\"link__icon\": \"guidelinelink_link__icon__q2qEL\",\n\t\"link__label\": \"guidelinelink_link__label__k4pQn\"\n};\n\nmodule.exports.__checksum = \"0df5107c3124\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9HdWlkZWxpbmVMaW5rL2d1aWRlbGluZWxpbmsubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0d1aWRlbGluZUxpbmsvZ3VpZGVsaW5lbGluay5tb2R1bGUuc2Nzcz9jOGMzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJndWlkZWxpbmVsaW5rX3dyYXBwZXJfXzJhb2lYXCIsXG5cdFwiY29udGVudFwiOiBcImd1aWRlbGluZWxpbmtfY29udGVudF9fZE04Sm5cIixcblx0XCJsaW5rXCI6IFwiZ3VpZGVsaW5lbGlua19saW5rX18wODdfZlwiLFxuXHRcImxpbmtfX2ljb25cIjogXCJndWlkZWxpbmVsaW5rX2xpbmtfX2ljb25fX3EycUVMXCIsXG5cdFwibGlua19fbGFiZWxcIjogXCJndWlkZWxpbmVsaW5rX2xpbmtfX2xhYmVsX19rNHBRblwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIwZGY1MTA3YzMxMjRcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss":
/*!***********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Header/header.module.scss ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"header_wrapper__o2Cif\",\n\t\"headline\": \"header_headline__T2YEt\",\n\t\"download\": \"header_download__bfE6Q\"\n};\n\nmodule.exports.__checksum = \"ccb0339c45aa\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9IZWFkZXIvaGVhZGVyLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0hlYWRlci9oZWFkZXIubW9kdWxlLnNjc3M/YjlkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiaGVhZGVyX3dyYXBwZXJfX28yQ2lmXCIsXG5cdFwiaGVhZGxpbmVcIjogXCJoZWFkZXJfaGVhZGxpbmVfX1QyWUV0XCIsXG5cdFwiZG93bmxvYWRcIjogXCJoZWFkZXJfZG93bmxvYWRfX2JmRTZRXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImNjYjAzMzljNDVhYVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Header/header.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Media/media.module.scss":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Media/media.module.scss ***!
  \*********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"media_wrapper__xyR_4\",\n\t\"media\": \"media_media__hEOOi\",\n\t\"inner\": \"media_inner__SNaNH\",\n\t\"download\": \"media_download__WLjJx\",\n\t\"full__scale\": \"media_full__scale__S6sMj\",\n\t\"auto__height\": \"media_auto__height__7lhKV\"\n};\n\nmodule.exports.__checksum = \"9301744a0380\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9NZWRpYS9tZWRpYS5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9NZWRpYS9tZWRpYS5tb2R1bGUuc2Nzcz8xOTQxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJtZWRpYV93cmFwcGVyX194eVJfNFwiLFxuXHRcIm1lZGlhXCI6IFwibWVkaWFfbWVkaWFfX2hFT09pXCIsXG5cdFwiaW5uZXJcIjogXCJtZWRpYV9pbm5lcl9fU05hTkhcIixcblx0XCJkb3dubG9hZFwiOiBcIm1lZGlhX2Rvd25sb2FkX19XTGpKeFwiLFxuXHRcImZ1bGxfX3NjYWxlXCI6IFwibWVkaWFfZnVsbF9fc2NhbGVfX1M2c01qXCIsXG5cdFwiYXV0b19faGVpZ2h0XCI6IFwibWVkaWFfYXV0b19faGVpZ2h0X183bGhLVlwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI5MzAxNzQ0YTAzODBcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Media/media.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"navigationwrap_wrapper__CWlTa\",\n\t\"nav__wrapper\": \"navigationwrap_nav__wrapper__23AWi\",\n\t\"row__nav\": \"navigationwrap_row__nav__N15Ox\",\n\t\"col__nav\": \"navigationwrap_col__nav__9d88W\",\n\t\"nav__list\": \"navigationwrap_nav__list__L5XYj\"\n};\n\nmodule.exports.__checksum = \"15553f962f3c\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9OYXZpZ2F0aW9uV3JhcC9uYXZpZ2F0aW9ud3JhcC5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vTmF2aWdhdGlvbldyYXAvbmF2aWdhdGlvbndyYXAubW9kdWxlLnNjc3M/YjUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwibmF2aWdhdGlvbndyYXBfd3JhcHBlcl9fQ1dsVGFcIixcblx0XCJuYXZfX3dyYXBwZXJcIjogXCJuYXZpZ2F0aW9ud3JhcF9uYXZfX3dyYXBwZXJfXzIzQVdpXCIsXG5cdFwicm93X19uYXZcIjogXCJuYXZpZ2F0aW9ud3JhcF9yb3dfX25hdl9fTjE1T3hcIixcblx0XCJjb2xfX25hdlwiOiBcIm5hdmlnYXRpb253cmFwX2NvbF9fbmF2X185ZDg4V1wiLFxuXHRcIm5hdl9fbGlzdFwiOiBcIm5hdmlnYXRpb253cmFwX25hdl9fbGlzdF9fTDVYWWpcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMTU1NTNmOTYyZjNjXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss":
/*!*****************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"searchbar_wrapper__lQzqp\",\n\t\"search__wrapper\": \"searchbar_search__wrapper__zhJNv\",\n\t\"search__bar\": \"searchbar_search__bar__me_P5\"\n};\n\nmodule.exports.__checksum = \"c5c9e546ee9e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9TZWFyY2hCYXIvc2VhcmNoYmFyLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1NlYXJjaEJhci9zZWFyY2hiYXIubW9kdWxlLnNjc3M/MGNlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwic2VhcmNoYmFyX3dyYXBwZXJfX2xRenFwXCIsXG5cdFwic2VhcmNoX193cmFwcGVyXCI6IFwic2VhcmNoYmFyX3NlYXJjaF9fd3JhcHBlcl9femhKTnZcIixcblx0XCJzZWFyY2hfX2JhclwiOiBcInNlYXJjaGJhcl9zZWFyY2hfX2Jhcl9fbWVfUDVcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiYzVjOWU1NDZlZTllXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss":
/*!*********************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss ***!
  \*********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"texthorizon_wrapper__OQFLj\",\n\t\"headline\": \"texthorizon_headline__QGU5y\",\n\t\"content\": \"texthorizon_content__qfSfB\",\n\t\"blockquote\": \"texthorizon_blockquote__RlgAz\",\n\t\"paragraph\": \"texthorizon_paragraph__ecVT8\"\n};\n\nmodule.exports.__checksum = \"f00ff7b5a709\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9UZXh0SG9yaXpvbi90ZXh0aG9yaXpvbi5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVGV4dEhvcml6b24vdGV4dGhvcml6b24ubW9kdWxlLnNjc3M/OTYxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwidGV4dGhvcml6b25fd3JhcHBlcl9fT1FGTGpcIixcblx0XCJoZWFkbGluZVwiOiBcInRleHRob3Jpem9uX2hlYWRsaW5lX19RR1U1eVwiLFxuXHRcImNvbnRlbnRcIjogXCJ0ZXh0aG9yaXpvbl9jb250ZW50X19xZlNmQlwiLFxuXHRcImJsb2NrcXVvdGVcIjogXCJ0ZXh0aG9yaXpvbl9ibG9ja3F1b3RlX19SbGdBelwiLFxuXHRcInBhcmFncmFwaFwiOiBcInRleHRob3Jpem9uX3BhcmFncmFwaF9fZWNWVDhcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZjAwZmY3YjVhNzA5XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss\n");

/***/ })

};
;