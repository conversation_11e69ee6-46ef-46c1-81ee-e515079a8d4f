'use client'

import { usePathname } from 'next/navigation'
import { AdminLayout } from '@/layouts/admin'
import { AuthLayout } from '@/layouts/auth'

export default function ClientLayout({ children }: { children: React.ReactNode }) {
	const pathname = usePathname()

	// Check if auth pages
	const isAuthPage = pathname.startsWith('/login')
	// || pathname.startsWith('/forget-password')

	// Switch to auth layout
	if (isAuthPage) {
		return <AuthLayout>{children}</AuthLayout>
	}

	return <AdminLayout>{children}</AdminLayout>
}
