@import '@/styles/config';

.wrapper {
	background-color: color('white', 2);
	overflow-x: auto;

	table {
		width: 100%;
		@include fluid($font-size) {
			font-size: size('body', 'sm');
		}
		line-height: 1.4;
	}

	th {
		padding: spacing(s4);
		text-align: left;
		font-weight: 600;

		.check {
			--checkbox-color: #{color('grey', 20)};
			--checkbox-hover-color: var(--collect-primary-color-80);
			--checkbox-active-color: var(--collect-primary-color-80);
		}
	}

	tbody tr {
		transition: 0.2s;
		&:nth-child(2n-1) {
			background-color: color('white', 0);
		}

		&:global(.on__selection) {
			background-color: var(--collect-primary-color-80);
		}
	}

	td {
		padding: spacing(s3) spacing(s4);

		.check {
			--checkbox-color: #{color('grey', 20)};
			--checkbox-hover-color: var(--collect-primary-color-80);
			--checkbox-active-color: #{color('white', 0)};
		}

		// Ensure <a> spans the full width and height of <td> to make it clickable
		a {
			display: block;
			width: 100%;
			height: 100%;
		}
	}

	.check {
		--checkbox-size: #{spacing(s4)};
		--checkbox-radius: #{spacing(s1)};
		--checkbox-border-size: #{px-to(2px, rem)};
		height: var(--checkbox-size);

		label {
			height: inherit;
		}
	}
}
