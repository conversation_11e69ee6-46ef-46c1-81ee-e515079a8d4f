/* eslint-disable prettier/prettier */
export const InitialData = [
  {
    label: 'Introduction',
    path: 'intro',
    children: [{ label: 'Who This Guide is For', isLocked: false, path: 'who-this-guide-is-for' }],
  },
  {
    label: 'Vision',
    path: 'vision',
    children: [
      { label: 'Overview', isLocked: false, path: 'overview' },
      { label: 'Mission', isLocked: false, path: 'mission' },
      { label: 'Craft', isLocked: false, path: 'craft' },
      { label: 'Outcomes', isLocked: false, path: 'outcomes' },
      { label: 'Value', isLocked: false, path: 'value' },
    ],
  },
  {
    label: 'Verbal',
    path: 'verbal',
    children: [
      { label: 'Narratives', isLocked: false, path: 'narratives' },
      { label: 'Tone of Voice', isLocked: false, path: 'tone-of-voice' },
      { label: 'Writing & Style', isLocked: false, path: 'writing-n-style' },
      { label: 'Naming Guide', isLocked: false, path: 'naming-guide' },
      { label: 'Messaging Framework', isLocked: false, path: 'messaging-framework' },
      { label: 'ToV in Action', isLocked: false, path: 'tov-in-action' },
    ],
  },
  {
    label: 'Logo',
    path: 'logo',
    children: [{ label: 'Downloadable Logo', isLocked: true, path: 'downloadable-logo' }],
  },
  {
    label: 'Visual',
    path: 'visual',
    children: [
      { label: 'Color', isLocked: false, path: 'color' },
      { label: 'Visual Motif', isLocked: false, path: 'visual-motif' },
    ],
  },
  { label: 'Photography', isLocked: false, path: 'photography', children: [] },
  {
    label: 'Archive',
    path: 'Archive',
    children: [
      { label: 'CDA Trung Thu 2023', isLocked: false, path: 'mid-autumn-2023' },
      { label: 'Demo', isLocked: false, path: 'demo' },
    ],
  },
]
