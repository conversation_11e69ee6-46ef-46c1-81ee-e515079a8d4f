'use client'

import { createContext, useState, type ReactNode, useEffect, useMemo } from 'react'

type ContextType = {
	toggleDark: () => void
	isDark: boolean
}

const defaultContext: ContextType = {
	toggleDark: () => {
		console.warn('Should have been overriden')
	},
	isDark: true,
}

const ThemeContext = createContext(defaultContext)
export const ThemeContextProvider = ({ children }: { children: ReactNode }) => {
	const [isDark, setIsDark] = useState(true)

	useEffect(() => {
		const localStore = localStorage.getItem('ThemeContext:isDark')
		if (localStore !== undefined && localStore !== null) {
			const lsDark = JSON.parse(localStore)
			setIsDark(lsDark)
		} else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
			setIsDark(false)
		}
	}, [])

	const context: ContextType = useMemo(
		() => ({
			toggleDark: () => {
				localStorage.setItem('ThemeContext:isDark', String(!isDark))
				setIsDark(!isDark)
			},
			isDark,
		}),
		[isDark]
	)
	return <ThemeContext.Provider value={context}>{children}</ThemeContext.Provider>
}
