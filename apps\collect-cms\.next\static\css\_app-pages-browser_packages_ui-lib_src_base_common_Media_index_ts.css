/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Media/media.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.media_wrapper__xyR_4 {
  margin-bottom: var(--section-mg-btm);
}
.media_wrapper__xyR_4 .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  grid-template-columns: repeat(calc(var(--count) / 3), 1fr);
}
@media (min-width: 75rem) {
  .media_wrapper__xyR_4 .aidigi__grid {
    grid-template-columns: repeat(calc(var(--count) / 2), 1fr);
  }
}
@media (min-width: 90rem) {
  .media_wrapper__xyR_4 .aidigi__grid {
    grid-template-columns: repeat(calc(var(--count)), 1fr);
  }
}
.media_wrapper__xyR_4 .media_media__hEOOi {
  background-color: #f6f6f6;
  height: auto;
  min-height: clamp(12.5rem, 29.4375rem - 4.234375rem * (var(--count) - 2), 29.4375rem);
  border-radius: 1.5625rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
.media_wrapper__xyR_4 .media_inner__SNaNH {
  position: relative;
  width: 100%;
  flex: 1 1 100%;
  display: flex;
  justify-content: center;
}
.media_wrapper__xyR_4 .media_download__WLjJx {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 3.5rem;
  height: 3.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-left-radius: 1.5625rem;
  border-bottom-right-radius: 1.5625rem;
  background-color: #f0f0f0;
}
.media_wrapper__xyR_4 .media_download__WLjJx svg {
  --size: 1.125rem;
}
.media_wrapper__xyR_4 img {
  position: absolute;
  width: auto;
  height: auto;
  max-height: 100%;
  object-fit: cover;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.media_wrapper__xyR_4 .media_full__scale__S6sMj img {
  width: 100%;
  height: 100%;
  max-height: none;
}
.media_wrapper__xyR_4 .media_auto__height__7lhKV {
  min-height: unset;
}
.media_wrapper__xyR_4 .media_auto__height__7lhKV img {
  position: static;
  transform: none;
}
.media_wrapper__xyR_4 p {
  padding: 0.625rem;
  color: #646464;
  font-size: clamp(0.875rem, 1.25rem - 0.075rem * (var(--count) - 2), 1.25rem);
}
