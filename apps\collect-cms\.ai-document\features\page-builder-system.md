# Collect CMS Page Builder System

This document explains the page builder system in the Collect CMS application, detailing how content is structured, edited, and managed through the visual page builder interface.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Component Structure](#component-structure)
4. [Page Builder Routes](#page-builder-routes)
5. [Builder Context](#builder-context)
6. [Layout Editor](#layout-editor)
7. [Component Editor](#component-editor)
8. [Data Flow](#data-flow)
9. [Integration with Strapi](#integration-with-strapi)

## Overview

The Collect CMS page builder is a visual interface for creating and editing content. It allows users to:

- Arrange components in a drag-and-drop interface
- Edit component properties through a user-friendly UI
- Preview content changes in real-time
- Save and publish content to the Strapi backend

The page builder is designed around a component-based architecture where pages are composed of reusable components that can be arranged and configured as needed.

## Architecture

The page builder system consists of several key parts:

1. **Page Builder Layout**: The overall UI structure with toolbars and sidebars
2. **Layout Editor**: The main canvas where components are arranged
3. **Component Editor**: UI for editing component properties
4. **Builder Context**: State management for the page builder
5. **Component Registry**: Definitions of available components
6. **Strapi Integration**: Communication with the backend CMS

These parts work together to provide a complete content editing experience.

## Component Structure

Content in the page builder is structured as a tree of components:

```typescript
// Example content structure
{
  components: [
    {
      __component: 'common.header',
      Headline: 'Welcome to our site',
      Subhead: 'Learn more about our services',
      ActionButton: {
        ButtonText: 'Get Started',
        ButtonLink: '/services'
      }
    },
    {
      __component: 'common.text-horizon',
      Headline: 'Our Mission',
      Paragraph: 'We strive to provide the best service...'
    },
    {
      __component: 'common.media',
      Media: [
        {
          IsDownloadable: false,
          Media: {
            url: '/hero-image.jpg'
          }
        }
      ]
    }
  ]
}
```

Each component has:
- A type identifier (`__component`)
- Properties specific to that component type
- Possibly nested components or component arrays

## Page Builder Routes

The page builder is accessible through specific routes:

```
/[lng]/content-builder/[group]/[type]/[id]
```

For example:
- `/en/content-builder/page/page/about-us`
- `/en/content-builder/other/home-page/home`

These routes are handled by the content builder layout:

```typescript
// src/app/[lng]/(pagebuilder)/content-builder/[...slug]/layout.tsx
export default async function ContentBuilderLayout({
  children,
  params: { lng, slug },
}: Readonly<{ children: React.ReactNode; params: { lng: string; slug: string[] } }>) {
  // Get authentication token
  const cookieStore = cookies()
  const authToken = cookieStore.get('adminJwt')?.value
  
  // Find the current navigation group and collection
  const currentNav = NavigationData.find((nav) => nav.apiId === slug[0])
  const collectionApiId = slug[1]
  if (!currentNav) {
    notFound()
  }
  if (slug.length < 2) {
    redirect(`/content-manager/${slug[0]}`)
  }
  const currentCollection = currentNav.layouts.find((layout) => layout.apiId === collectionApiId)
  if (!currentCollection) {
    notFound()
  }
  if (slug.length < 3) {
    redirect(`/content-manager/${slug[0]}/${slug[1]}`)
  }
  
  // Fetch necessary data
  const currentUid = currentCollection.uid
  const [globalInit, components, contentType, configuration, CmsData] = await Promise.all([
    getGlobalInit({ authToken }),
    getComponents(),
    getContentType(`api::${currentUid}.${currentUid}`),
    getConfiguration({ uid: currentUid, authToken }),
    getCollectionTypesData({
      path: currentCollection.uid,
      authToken,
      filter: `filters[slug][$eq]=${slug[2]}`,
    }),
  ])
  
  // Get the current content item
  const currentData = CmsData.results[0]
  if (!currentData) {
    notFound()
  }
  
  // Render the page builder
  return (
    <PageBuilderLayout
      value={{
        components,
        globals: globalInit,
        configuration,
        data: { data: currentData },
        contentType,
        locale: lng,
        slug: slug,
      }}
    >
      {children}
    </PageBuilderLayout>
  )
}
```

This layout:
1. Resolves the route parameters to find the content being edited
2. Fetches the necessary data from the Strapi backend
3. Initializes the page builder with the content data
4. Renders the page builder interface

## Builder Context

The page builder state is managed through a React context:

```typescript
// contexts/BuilderContext.tsx (simplified)
export type BuilderBaseProps = {
  components: any
  globals: any
  configuration: any
  data: {
    data: IResultDataProps
  }
  contentType: any
  locale: string
  slug: string[]
}

const BuilderContext = createContext<{
  state: BuilderBaseProps
  // Additional state and methods...
}>({
  state: {
    components: {},
    globals: {},
    configuration: {},
    data: { data: {} as IResultDataProps },
    contentType: {},
    locale: 'en',
    slug: [],
  },
  // Additional state and methods...
})

export const PageBuilderProvider = ({ children, value }: { children: ReactNode; value: BuilderBaseProps }) => {
  // State management logic...
  
  return (
    <BuilderContext.Provider
      value={{
        state: value,
        // Additional state and methods...
      }}
    >
      {children}
    </BuilderContext.Provider>
  )
}
```

This context provides:
- The current content being edited
- Available components and their configurations
- Methods for modifying the content
- Global settings and configurations

## Layout Editor

The layout editor is the main canvas where components are arranged:

```typescript
// src/components/Builder/LayoutEditor.tsx (simplified)
export const LayoutEditor = ({ children }: { children: React.ReactNode }) => {
  const { state, selectedComponent, setSelectedComponent } = useContext(BuilderContext)
  const { data } = state
  
  return (
    <div className={styles.editor}>
      <div className={styles.canvas}>
        {data.data.components.map((component, index) => (
          <ComponentWrapper
            key={index}
            component={component}
            index={index}
            isSelected={selectedComponent === index}
            onSelect={() => setSelectedComponent(index)}
          />
        ))}
      </div>
      {children}
    </div>
  )
}
```

The layout editor:
- Renders each component in the content
- Handles component selection
- Provides the interface for rearranging components
- Shows the visual representation of the content

## Component Editor

The component editor provides the interface for editing component properties:

```typescript
// src/components/Builder/ComponentEditor.tsx (simplified)
export const ComponentEditor = () => {
  const { state, selectedComponent, updateComponent } = useContext(BuilderContext)
  const { data, components } = state
  
  if (selectedComponent === null) {
    return <div className={styles.empty}>Select a component to edit</div>
  }
  
  const component = data.data.components[selectedComponent]
  const componentType = component.__component
  const componentSchema = components[componentType]
  
  return (
    <div className={styles.editor}>
      <h3>{componentSchema.info.displayName}</h3>
      
      {Object.entries(componentSchema.attributes).map(([key, attribute]) => (
        <FieldEditor
          key={key}
          name={key}
          attribute={attribute}
          value={component[key]}
          onChange={(value) => updateComponent(selectedComponent, key, value)}
        />
      ))}
    </div>
  )
}
```

The component editor:
- Shows the properties of the selected component
- Provides appropriate editors for each property type
- Updates the component data when properties are changed
- Validates input according to the component schema

## Data Flow

The data flow in the page builder follows this pattern:

1. **Initialization**:
   - Fetch content data from Strapi
   - Load component definitions and configurations
   - Initialize the builder context with this data

2. **Editing**:
   - User selects a component in the layout editor
   - Component editor shows the properties of the selected component
   - User modifies properties through the component editor
   - Changes are applied to the content data in the builder context
   - Layout editor updates to reflect the changes

3. **Saving**:
   - User clicks the save button
   - Content data is sent to the Strapi backend
   - Backend validates and stores the data
   - User receives confirmation of successful save

This flow ensures that changes are immediately visible in the layout editor while maintaining a clean separation between the UI and the data.

## Integration with Strapi

The page builder integrates with Strapi through several API calls:

1. **Fetching Content**:
   ```typescript
   const CmsData = await getCollectionTypesData({
     path: currentCollection.uid,
     authToken,
     filter: `filters[slug][$eq]=${slug[2]}`,
   })
   ```

2. **Fetching Component Definitions**:
   ```typescript
   const components = await getComponents()
   ```

3. **Fetching Content Type Schema**:
   ```typescript
   const contentType = await getContentType(`api::${currentUid}.${currentUid}`)
   ```

4. **Saving Content**:
   ```typescript
   // Example of how content might be saved
   const saveContent = async () => {
     try {
       await updateCollectionTypeData({
         path: currentCollection.uid,
         id: currentData.id,
         data: {
           components: data.data.components,
           // Other fields...
         },
         authToken,
       })
       showSuccessMessage('Content saved successfully')
     } catch (error) {
       showErrorMessage('Failed to save content')
     }
   }
   ```

These API calls allow the page builder to work seamlessly with the Strapi backend, providing a complete content management solution.
