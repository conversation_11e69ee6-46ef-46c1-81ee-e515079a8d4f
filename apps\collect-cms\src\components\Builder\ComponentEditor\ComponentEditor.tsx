import cn from 'classnames'
import { useContext, useMemo } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './componenteditor.module.scss'

export const ComponentEditor = () => {
	const context = useContext(PageBuilderContext)
	const { editingIden, data } = context
	const { data: commonData } = data ?? {}

	const currentEdit = useMemo(() => {
		return commonData.components.find((component) =>
			component.id ? component.id === editingIden.id : component.__temp_key__ === editingIden.id
		)
	}, [commonData, editingIden])

	return <div>{currentEdit?.__component}</div>
}
