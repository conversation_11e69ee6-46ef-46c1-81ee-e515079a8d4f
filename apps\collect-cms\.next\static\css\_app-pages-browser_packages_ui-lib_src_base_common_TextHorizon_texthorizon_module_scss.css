/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/TextHorizon/texthorizon.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.texthorizon_wrapper__OQFLj {
  margin-bottom: var(--section-mg-btm);
  margin-top: 1.5rem;
  position: relative;
}
.texthorizon_wrapper__OQFLj::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0.0625rem;
  background-color: #dddddd;
}
.texthorizon_wrapper__OQFLj .aidigi__grid {
  display: grid;
  gap: 1.25rem;
  padding-top: 4rem;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .aidigi__grid {
    grid-template-columns: repeat(12, 1fr);
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_headline__QGU5y {
  font-weight: 500;
  color: #1d1d1f;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .texthorizon_headline__QGU5y {
    grid-column: span 6;
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB {
    grid-column: span 6;
  }
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB .texthorizon_blockquote__RlgAz {
  color: #1d1d1f;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB .texthorizon_paragraph__ecVT8 {
  color: #6e6e73;
  font-size: 0.9375rem;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB ul {
  padding-left: 1.5rem;
  list-style-type: disc;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB i,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB em {
  font-style: italic;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB b,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB strong {
  font-weight: 700;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB a,
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB u {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}
.texthorizon_wrapper__OQFLj .texthorizon_content__qfSfB del {
  text-decoration: line-through;
}
.texthorizon_wrapper__OQFLj.nohead {
  margin-top: 0;
}
@media (min-width: 90rem) {
  .texthorizon_wrapper__OQFLj.nohead .texthorizon_content__qfSfB {
    grid-column-start: 7;
    grid-column-end: span 6;
  }
}
.texthorizon_wrapper__OQFLj.nohead::before {
  display: none;
}
.texthorizon_wrapper__OQFLj.nohead .aidigi__grid {
  padding-top: 0;
}
