import type { IMediaProps } from '@collective/integration-lib/cms';
import * as jose from 'jose';
export type IJwtBaseProps = {
    id: number;
    iat: number;
    nbf: number;
    exp: number;
};
export type IUserJWTProps = {
    id: number;
    username: string;
    email: string;
    fullName: string;
    avatar?: IMediaProps;
    strapiToken: string;
};
export declare function generateKeyPair(): Promise<{
    publicKey: jose.KeyLike;
    privateKey: jose.KeyLike;
}>;
export declare function exportJWK(keyJWK: jose.KeyLike | Uint8Array): Promise<jose.JWK>;
export declare function verifyJWT(jws: string): Promise<{
    payload: string;
    protectedHeader: jose.CompactJWSHeaderParameters;
} | null>;
export declare function checkValidJWT<PayloadProps>(jws: string): Promise<(IJwtBaseProps & PayloadProps) | null>;
export declare function checkIsLogin(token: string | null): Promise<(IJwtBaseProps & IUserJWTProps) | null>;
export declare function checkIsLoginClient(): Promise<(IJwtBaseProps & IUserJWTProps) | null>;
