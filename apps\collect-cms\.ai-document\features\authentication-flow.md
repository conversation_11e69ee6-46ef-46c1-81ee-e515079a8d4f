# Collect CMS Authentication Flow

This document details the authentication system in the Collect CMS application, explaining how users are authenticated, how tokens are managed, and how protected routes are secured.

## Table of Contents

1. [Overview](#overview)
2. [Authentication Components](#authentication-components)
3. [Login Process](#login-process)
4. [Token Management](#token-management)
5. [Route Protection](#route-protection)
6. [Logout Process](#logout-process)
7. [Authentication State](#authentication-state)
8. [Security Considerations](#security-considerations)

## Overview

Collect CMS uses a JWT (JSON Web Token) based authentication system that integrates with a Strapi backend. The authentication flow involves:

1. User login through a dedicated login page
2. Token storage in cookies and browser storage
3. Middleware-based route protection
4. User menu for profile management and logout

## Authentication Components

The authentication system consists of several key components:

### 1. Login Page

Located at `src/app/[lng]/(dashboard)/login/page.tsx`, this page:

- Renders a login form with email and password fields
- Includes a "Remember me" option
- <PERSON>les form validation and submission
- Communicates with the Strapi backend API
- Stores authentication tokens upon successful login
- Redirects users to their intended destination

### 2. Authentication Middleware

Located at `src/middleware.ts`, the middleware:

- Intercepts all non-static requests
- Checks for the presence of a valid JWT token in cookies
- Redirects unauthenticated users to the login page
- Preserves the original destination URL as a redirect parameter
- Allows public routes to be accessed without authentication

### 3. User Menu Component

Located at `src/components/UserMenu/UserMenu.tsx`, this component:

- Displays the current user's information
- Provides access to user-related actions like profile viewing and logout
- Handles the logout process by clearing tokens

## Login Process

The login process follows these steps:

1. **Form Submission**:

   ```typescript
   const handleLogin = async (e: React.FormEvent) => {
   	e.preventDefault()
   	// Form validation
   	// ...

   	try {
   		const response = await postJsonFormData<{
   			data: {
   				token: string
   				user: { firstname: string; lastname: string | null; username: string | null }
   			}
   		}>({
   			fullPath: `${process.env.NEXT_PUBLIC_STRAPI_HOST}/admin/login`,
   			body: JSON.stringify({ email, password }),
   		})

   		// Token storage
   		// ...

   		// Redirect
   		window.location.href = redirectPath
   	} catch (error) {
   		// Error handling
   	}
   }
   ```

2. **Redirect Handling**:
   The login page accepts a `redirect` query parameter that specifies where to send the user after successful login:

   ```typescript
   const redirectParam = searchParams.get("redirect")
   let redirectPath = `/${lng}`

   if (redirectParam && !redirectParam.includes("/login") && redirectParam.startsWith("/")) {
   	if (redirectParam.split("/")[1] !== lng) {
   		redirectPath = `/${lng}${redirectParam}`
   	} else {
   		redirectPath = redirectParam
   	}
   }
   ```

## Token Management

Authentication tokens are managed in multiple locations for different purposes:

1. **Cookie Storage**:

   ```typescript
   setCookie("adminJwt", response.data.token || "", rememberMe ? 30 : 1)
   ```

   - Used by the server-side middleware for route protection
   - Expiration depends on the "Remember me" option (30 days or 1 day)

2. **LocalStorage/SessionStorage**:

   ```typescript
   if (rememberMe) {
   	localStorage.setItem("adminJwt", response.data.token || "")
   } else {
   	sessionStorage.setItem("adminJwt", response.data.token || "")
   }
   ```

   - Used for client-side authentication checks
   - LocalStorage for persistent sessions ("Remember me")
   - SessionStorage for session-only persistence

3. **User Data Storage**:
   ```typescript
   localStorage.setItem("adminUser", JSON.stringify(response.data.user))
   ```
   - Stores user profile information for display in the UI
   - Used by the UserMenu component to show user details

## Route Protection

Routes are protected through the middleware:

```typescript
// Check if the route is a public route that doesn't require authentication
const isPublicRoute =
	request.nextUrl.pathname.includes("/login") ||
	request.nextUrl.pathname === `/${lng}` ||
	request.nextUrl.pathname === `/${lng}/` ||
	request.nextUrl.pathname.includes("/forgot-password") ||
	request.nextUrl.pathname.includes("/reset-password")

// If it's not a public route, check for authentication
if (!isPublicRoute) {
	// Get the token from cookies
	const token = request.cookies.get("adminJwt")?.value

	// If no token is found, redirect to login page
	if (!token) {
		const loginUrl = new URL(`/${lng}/login`, request.url)
		const redirectPath = request.nextUrl.pathname
		loginUrl.searchParams.set("redirect", redirectPath)
		return NextResponse.redirect(loginUrl)
	}
}
```

This ensures that:

1. Public routes are accessible to everyone
2. Protected routes require a valid token
3. Unauthenticated users are redirected to login with the original destination preserved

## Logout Process

The logout process is handled by the UserMenu component:

```typescript
const handleSignOut = () => {
	localStorage.removeItem("adminJwt")
	sessionStorage.removeItem("adminJwt")
	setCookie("adminJwt", "", 0)
	localStorage.removeItem("adminUser")
	setData(null)
	window.location.href = "/login"
}
```

This process:

1. Removes the token from localStorage and sessionStorage
2. Clears the cookie by setting an empty value with immediate expiration
3. Removes the user data from localStorage
4. Redirects the user to the login page

## Authentication State

The authentication state is primarily determined by the presence of a valid token:

1. **Server-side**: The middleware checks for the `adminJwt` cookie
2. **Client-side**: Components can check localStorage/sessionStorage for the token

The UserMenu component loads user data from localStorage on mount:

```typescript
useIsomorphicLayoutEffect(() => {
	const getUser = async () => {
		const userDataString = localStorage.getItem("adminUser")
		if (userDataString) {
			try {
				const userData = JSON.parse(userDataString)
				setData(userData)
			} catch (error) {
				console.error("Error parsing user data:", error)
			}
		}
	}
	getUser()
}, [])
```

## Security Considerations

The authentication system implements several security practices:

1. **Token Storage**:

   - Cookies for server-side authentication
   - Browser storage for client-side authentication
   - Different expiration times based on "Remember me"

2. **Route Protection**:

   - Middleware-based protection for all non-public routes
   - No direct access to protected content without authentication

3. **Redirect Safety**:

   - Validation of redirect URLs to prevent open redirect vulnerabilities
   - Ensuring redirects stay within the application

4. **Token Cleanup**:
   - Complete removal of tokens from all storage locations on logout
   - Immediate expiration of cookies on logout

However, some potential improvements could include:

- Token refresh mechanism for long-lived sessions
- Token validation beyond presence checking
- CSRF protection for authentication endpoints
