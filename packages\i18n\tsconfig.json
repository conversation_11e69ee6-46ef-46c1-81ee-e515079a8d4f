{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@collective/tsconfig/base.json", "compilerOptions": {"baseUrl": "./src", "outDir": "./dist", "target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "jsx": "react-jsx", "noEmit": false, "incremental": true, "paths": {}}, "path": {"@collective/core": ["../core/src"]}, "include": [".eslintrc.*", "**/*.ts", "**/*.tsx", "**/*.mts", "**/*.js", "**/*.cjs", "**/*.mjs", "**/*.jsx", "**/*.json"], "exclude": ["**/node_modules", "**/.*/", "dist", "build"]}