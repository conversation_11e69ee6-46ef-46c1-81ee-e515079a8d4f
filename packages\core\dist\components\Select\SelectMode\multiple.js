import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import React, { useContext } from 'react';
import styles from '../../Input/input.module.scss';
import { SelectContext } from '../Select';
import styleSelect from '../select.module.scss';
export const Multiple = React.forwardRef(({ id, className, startIcon, endIcon, label, placeholder, required }, ref) => {
    const { setIsShow, isShow } = useContext(SelectContext);
    return (_jsxs("div", { className: cn('form__wrapper', styles['form-control-wrapper'], className, startIcon && styles['form-control-wrapper--icon-left'], endIcon && styles['form-control-wrapper--icon-right'], isShow && 'is__triggered'), children: [label && (_jsxs("span", { className: styles.label, children: [label, required && _jsx("span", { className: styles.mark, children: "*" })] })), _jsxs("div", { className: styles.wrapper, children: [startIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--left'], 'icon--left'), children: startIcon })), _jsx("button", { id: id, type: "button", className: cn(styles['form-control']), onClick: () => setIsShow(!isShow), ref: ref, children: _jsx("span", { className: "form-label", children: placeholder }) }), endIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--right'], 'icon--right'), children: endIcon }))] })] }));
});
export function MultipleItem({ value, children, className, }) {
    const { option, setOption, onChange } = useContext(SelectContext);
    const defaultOption = !Array.isArray(option) ? [] : option;
    const matchOption = (valueMatch) => defaultOption.some((item) => item.value.toString() === valueMatch);
    const handleChange = (optionValue, optionLabel) => {
        let updateValue;
        if (matchOption(optionValue)) {
            updateValue = defaultOption.filter((item) => item.value.toString() !== optionValue);
            setOption(updateValue);
        }
        else {
            updateValue = [
                ...defaultOption,
                {
                    value: optionValue,
                    label: optionLabel,
                },
            ];
            setOption(updateValue);
        }
        onChange?.(updateValue);
    };
    const optionData = {
        isActive: matchOption(value),
    };
    const RenderChildren = () => (typeof children === 'function' ? children(optionData) : children);
    return (_jsx("div", { role: "button", tabIndex: 0, className: cn(styleSelect.option, matchOption(value) && styleSelect.active, matchOption(value) && 'option__active', className), onClick: () => handleChange(value, children), children: _jsx(RenderChildren, {}) }));
}
