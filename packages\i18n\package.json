{"name": "@collective/i18n", "type": "module", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "sideEffects": false, "files": ["./src/**/*.ts*", "./public/**/*"], "exports": {".": "./src/index.ts", "./client": "./src/client/index.tsx", "./link/client": "./src/link/client/index.tsx", "./link/server": "./src/link/server/index.tsx", "./middleware": "./src/middleware/index.ts", "./server": "./src/server/index.ts", "./settings": "./src/settings/index.ts"}, "private": true, "dependencies": {"accept-language": "^3.0.20", "i18next": "^23.16.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-resources-to-backend": "^1.2.1", "next": "14.2.26", "next-i18n-router": "5.5.1", "react-i18next": "^14.1.3", "zod": "^3.23.8"}, "devDependencies": {"@collective/eslint-config-bases": "workspace:^", "@collective/tsconfig": "workspace:*", "@types/react": "18.3.19", "dotenv-cli": "^7.4.2", "eslint": "8.57.1", "postcss": "^8.4.47", "react": "^18.3.1", "rimraf": "6.0.1", "typescript": "5.8.2"}, "scripts": {"clean": "rimraf ./dist ./coverage ./tsconfig.tsbuildinfo", "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs . --fix", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs --cache --cache-location ../../.cache/eslint/i18n.eslintcache", "typecheck": "tsc --project ./tsconfig.json --noEmit"}}