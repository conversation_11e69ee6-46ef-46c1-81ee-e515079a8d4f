/* eslint-disable @typescript-eslint/naming-convention */
import { Mei<PERSON><PERSON>earch } from 'meilisearch'
import type { IMediaProps, INavigationWrapProps } from '../cms'

const CLOUDFLARE_HEADER = {
	'CF-Access-Client-Id': `${process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID}`,
	'CF-Access-Client-Secret': `${process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET}`,
}

const meiliClient = new MeiliSearch({
	host: `${process.env.NEXT_PUBLIC_MEILISEARCH_HOST}`,
	apiKey: `${process.env.NEXT_PUBLIC_MEILISEARCH_KEY}`,
	requestConfig: {
		headers: {
			...(process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET ? CLOUDFLARE_HEADER : {}),
		},
		next: {
			revalidate: 120,
		},
	},
})

export const generateMeiliSearchFilter = (
	filter: Record<string, Record<string, string>[]>,
	mapping: Record<string, { name: string; type: string }>,
	currentFilter?: string
) => {
	const filterData: (string | string[])[] = currentFilter ? [currentFilter] : []
	if (!filter || !Object.keys(filter).length) {
		return filterData
	}
	Object.entries(filter).forEach(([key, value]) => {
		const tmp: string[] = []
		value.forEach((option) => {
			if (mapping[key] === undefined) {
				return
			}
			if (mapping[key].type === 'number') {
				tmp.push(`${mapping[key].name} = ${option.value}`)
			} else {
				tmp.push(`${mapping[key].name} = "${option.value}"`)
			}
		})
		filterData.push(tmp)
	})
	return filterData
}

export async function enableMeiliSettings(client: MeiliSearch) {
	await Promise.all([
		client.index('general-setting').updateSettings({
			filterableAttributes: ['locale'],
		}),
		client.index('page').updateSettings({}),
	])
}

export type GeneralSettingProps = {
	locale: string
	Social: {
		platformName: string
		platformLink: string
	}[]
}

export async function searchGeneralSetting() {
	const index = meiliClient.index('general-setting')
	const response = await index.search<GeneralSettingProps>('', {})
	return response
}

// EXAMPLE
export type ExampleProps = {
	Headline: string
	Slug: string
}
export async function searchExample(
	query: string,
	options: {
		hitsPerPage: number
		page: number
		filter?: string
		sort?: string[]
		attributesToRetrieve?: string[]
		attributesToHighlight?: string[]
		attributesToSearchOn?: string[]
	}
) {
	const index = meiliClient.index('example')
	const response = index.search<ExampleProps, typeof options>(query, options)
	return response
}

export async function searchNavigation(
	query: string,
	options: {
		hitsPerPage: number
		page: number
		filter?: string | (string | string[])[]
		sort?: string[]
		attributesToRetrieve?: string[]
		attributesToHighlight?: string[]
		attributesToSearchOn?: string[]
	}
) {
	const index = meiliClient.index('category')
	const response = index.search<INavigationWrapProps, typeof options>(query, options)
	return response
}
