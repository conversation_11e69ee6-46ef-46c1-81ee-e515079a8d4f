'use client'

import { getC<PERSON>ie, Icon, useIsomorphicLayoutEffect } from '@collective/core'
import { type INavigationProps } from '@collective/integration-lib'
import { useModal } from '@collective/ui-lib/contexts/ModalContext'
import { NavigationContext } from '@collective/ui-lib/contexts/NavigationContext'
import Link from 'next/link'
import { useContext, useEffect, useState } from 'react'
import styles from './quicknav.module.scss'

type Props = {
	currentCategory?: {
		Headline: string
		slug: string
		isLocked?: boolean
	}
	currentPage?: {
		Headline: string
		slug: string
		isLocked?: boolean
	}
	lng?: string
}

type PrevNextType = {
	prev?: {
		label?: string
		path?: string
		isLocked?: boolean
	}
	next?: {
		label?: string
		path?: string
		isLocked?: boolean
	}
}

export const QuickNav = ({ currentCategory, currentPage, lng = 'en' }: Props) => {
	const { slug: catPath } = currentCategory || {}
	const { onOpenModal } = useModal()
	const pagesData = useContext(NavigationContext) // All categories
	const curCatIndex = pagesData.findIndex((cat) => cat.path === catPath) // Current category index
	const curSubPageIndex =
		pagesData[curCatIndex]?.children?.findIndex((page) => page.path === currentPage?.slug) || 0

	const homePage = { label: 'Home', path: '', isLocked: false }
	const cosmoPage = { label: 'Cōsmo', path: 'cosmo', isLocked: false }

	const [prevNext, setPrevNext] = useState<PrevNextType>()
	const [token, setToken] = useState<string | null>(null)

	useIsomorphicLayoutEffect(() => {
		setToken(localStorage.getItem('token') || sessionStorage.getItem('token') || getCookie('token'))
	}, [])

	const getPrevNext = (categories: INavigationProps[], catIndex: number, subPageIndex: number) => {
		const curCat = categories[catIndex]
		const prevCat = categories[catIndex - 1]
		const nextCat = categories[catIndex + 1]
		const curSubPages = curCat?.children

		// Category without subpage
		if (curSubPages?.length === 0) {
			return {
				prev: prevCat
					? prevCat.children?.length === 0
						? prevCat // Prev Category without subpage
						: {
								label: prevCat?.children?.at(-1)?.label,
								path: `${prevCat?.path}/${prevCat?.children?.at(-1)?.path}`,
								isLocked: prevCat?.children?.at(-1)?.isLocked,
							}
					: homePage,
				next: nextCat ? nextCat : cosmoPage,
			}
		}

		// First category
		if (catIndex === 0 && subPageIndex < 0) {
			return {
				prev: homePage,
				next: curSubPages?.at(0)
					? {
							label: curSubPages?.at(0)?.label,
							path: `${curCat?.path}/${curSubPages?.at(0)?.path}`,
							isLocked: curSubPages?.at(0)?.isLocked,
						}
					: nextCat,
			}
		}

		// First subpage of category
		if (subPageIndex === 0 && curSubPages && curSubPages?.length > 1) {
			return {
				prev: curCat,
				next: {
					label: curSubPages?.at(1)?.label,
					path: `${curCat?.path}/${curSubPages?.at(1)?.path}`,
					isLocked: curSubPages?.at(1)?.isLocked,
				},
			}
		}

		// Last subpage of category
		if (curSubPages && subPageIndex === curSubPages?.length - 1 && curSubPages?.length > 1) {
			return {
				prev: {
					label: curSubPages?.at(subPageIndex - 1)?.label,
					path: `${curCat?.path}/${curSubPages?.at(subPageIndex - 1)?.path}`,
					isLocked: curSubPages?.at(subPageIndex - 1)?.isLocked,
				},
				next: nextCat ? nextCat : cosmoPage,
			}
		}

		// Only one subpage of category
		if (curSubPages && curSubPages?.length === 1 && subPageIndex === 0) {
			return {
				prev: curCat,
				next: nextCat ? nextCat : cosmoPage,
			}
		}

		// Other cases
		if (subPageIndex) {
			return {
				prev:
					subPageIndex === -1 && prevCat
						? prevCat.children?.length === 0
							? prevCat // Prev Category without subpage
							: {
									label: prevCat?.children?.at(-1)?.label,
									path: `${prevCat?.path}/${prevCat?.children?.at(-1)?.path}`,
									isLocked: prevCat?.children?.at(-1)?.isLocked,
								}
						: {
								label: curSubPages?.at(subPageIndex - 1)?.label,
								path: `${curCat?.path}/${curSubPages?.at(subPageIndex - 1)?.path}`,
								isLocked: curSubPages?.at(subPageIndex - 1)?.isLocked,
							},
				next: {
					label: curSubPages?.at(subPageIndex + 1)?.label,
					path: `${curCat?.path}/${curSubPages?.at(subPageIndex + 1)?.path}`,
					isLocked: curSubPages?.at(subPageIndex + 1)?.isLocked,
				},
			}
		}
	}

	useEffect(() => {
		const prevNextData = getPrevNext(pagesData, curCatIndex, curSubPageIndex)
		pagesData && setPrevNext({ prev: prevNextData?.prev, next: prevNextData?.next })
	}, [pagesData, curCatIndex, curSubPageIndex])

	const handleClick = (
		e: React.MouseEvent,
		page?: {
			label?: string
			path?: string
			isLocked?: boolean
		}
	) => {
		if (page?.isLocked && !token) {
			e.preventDefault()
			onOpenModal('signin')
		}
	}
	return (
		<div className={styles.wrapper}>
			<div className="aidigi__grid">
				<Link
					href={prevNext ? `/${lng}/${prevNext?.prev?.path}` : ''}
					onClick={(e) => handleClick(e, prevNext?.prev)}
				>
					<h3 className="aidigi__heading">
						<Icon variant="prev" />
						Previous
					</h3>
					<h1 className="aidigi__heading">
						{prevNext?.prev?.isLocked && !token && <Icon variant="padlock" />}
						{prevNext ? prevNext?.prev?.label : ''}
					</h1>
				</Link>
				<Link
					href={prevNext ? `/${lng}/${prevNext?.next?.path}` : ''}
					onClick={(e) => handleClick(e, prevNext?.next)}
				>
					<h3 className="aidigi__heading">
						Next
						<Icon variant="next" />
					</h3>
					<h1 className="aidigi__heading">
						{prevNext?.next?.isLocked && !token && <Icon variant="padlock" />}
						{prevNext ? prevNext?.next?.label : ''}
					</h1>
				</Link>
			</div>
		</div>
	)
}
