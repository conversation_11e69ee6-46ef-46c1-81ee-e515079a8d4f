import { fetcher } from '@collective/core'
import type {
	IComponentSchemaProps,
	IConfigurationProps,
	IContentTypeSchemaProps,
	IMultiDataProps,
	IResultDataProps,
	ISingleDataProps,
} from '../interface'

export type IUserDataProps = {
	firstname: string
	id: number
	lastname: string
	username: string
}

export type IResultMetaProps = {
	availableLocales: IResultDataProps[]
	availableStatus: {
		id: number
		locale?: string | null
		updatedAt?: string
		createdAt?: string
		publishedAt?: string | null
		createdBy?: IUserDataProps
		updatedBy?: IUserDataProps
	}[]
}

export const getSingleTypesData = async ({
	path,
	authToken,
	deep = 4,
	page = 1,
	pageSize = 10,
	sort = 'updatedAt:DESC',
	filter,
}: {
	path: string
	authToken?: string
	deep?: number
	page?: number
	pageSize?: number
	sort?: string
	filter?: string
}): Promise<ISingleDataProps<IResultDataProps>> => {
	if (typeof window !== 'undefined') {
		return Promise.reject(new Error('This function is only available on the server'))
	}
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/single-types/api::${path}.${[path]}?pLevel=${deep}&page=${page}&pageSize=${pageSize}&sort=${sort}${filter ? `&${filter}` : ''}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${authToken}`,
		},
	}
	return fetcher(url, options)
}

export const getCollectionTypesData = async ({
	path,
	authToken,
	deep = 4,
	page = 1,
	pageSize = 10,
	sort = 'updatedAt:DESC',
	filter,
}: {
	path: string
	authToken?: string
	deep?: number
	page?: number
	pageSize?: number
	sort?: string
	filter?: string
}): Promise<IMultiDataProps<IResultDataProps>> => {
	if (typeof window !== 'undefined') {
		return Promise.reject(new Error('This function is only available on the server'))
	}
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/collection-types/api::${path}.${[path]}?pLevel=${deep}&page=${page}&pageSize=${pageSize}&sort=${sort}${filter ? `&${filter}` : ''}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${authToken}`,
		},
	}
	return fetcher(url, options)
}

export type IContentTypeProps = {
	apiID: string
	uid: string
	schema: IContentTypeSchemaProps
}
export const getContentTypes = async (): Promise<{ data: IContentTypeProps[] }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/content-types`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}
export const getContentType = async (type: string): Promise<{ data: IContentTypeProps }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/content-types/${type}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}

export type IServerComponentProps = {
	apiID: string
	uid: string
	schema: IComponentSchemaProps
	category: string
}
export const getComponents = async (): Promise<{ data: IServerComponentProps[] }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/components`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}
export const getComponent = async (type: string): Promise<{ data: IServerComponentProps }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/components/${type}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}

export const getConfiguration = async ({
	uid,
	authToken,
}: {
	uid: string
	authToken?: string
}): Promise<{ data: IConfigurationProps }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/content-types/api::${uid}.${uid}/configuration`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${authToken}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}

type IServerComponentPropsMinimal = {
	uid: string
	apiID: string
	category: string
	isDisplayed: boolean
	info: {
		displayName: string
		description?: string
		icon?: string
	}
	options: Record<string, unknown>
	attributes: {
		[key: string]: unknown
	}
}
export type IGlobalInit = {
	/**
	 * Array of component definitions
	 */
	components: IServerComponentPropsMinimal[]
	/**
	 * Field size configuration for different field types
	 */
	fieldSizes: {
		[key: string]: {
			default: number
			isResizable: boolean
		}
	}
	/**
	 * Content types configuration
	 */
	contentTypes: Array<{
		uid: string
		isDisplayed: boolean
		apiID: string
		kind: 'collectionType' | 'singleType'
		info: {
			singularName: string
			pluralName: string
			displayName: string
			description?: string
			name?: string
			collectionName?: string
		}
		options: Record<string, unknown>
		attributes: Record<string, unknown>
		pluginOptions?: Record<string, unknown>
	}>
}
export const getGlobalInit = async ({
	authToken,
}: {
	authToken?: string
}): Promise<{ data: IGlobalInit }> => {
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/init`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${authToken}`,
		},
		next: { revalidate: 120 },
	}
	return fetcher(url, options)
}
