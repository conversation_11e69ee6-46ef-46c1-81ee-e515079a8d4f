import { useWindowDimensions } from '@collective/core'
import { useIsomorphicLayoutEffect } from 'framer-motion'
import { useRef } from 'react'
import { createPortal } from 'react-dom'
import styles from './component.module.scss'

type ComponentDataProps = {
	ref: (EventTarget & Element) | null
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	data: any
}

export const ComponentDisplay = ({ data }: { data: ComponentDataProps }) => {
	const element = useRef<HTMLDivElement>(null)
	const { height } = useWindowDimensions()
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const { data: componentInfo } = data ?? {}

	useIsomorphicLayoutEffect(() => {
		if (!data.ref || !element.current) return

		const container = data.ref
		const sidebar = document.querySelector('aside')
		const GAP = 12

		const menu = element.current
		const { top } = container.getBoundingClientRect()
		if (top + element.current.clientHeight > (height as number)) {
			menu.style.top = `${(height as number) - element.current.clientHeight - GAP}px`
		} else {
			menu.style.top = `${top}px`
		}
		menu.style.left = `${(sidebar?.clientWidth ?? 0) + GAP}px`
	}, [data, element, height])

	return createPortal(
		<div className={styles.tooltip} ref={element}>
			<h4 className="collect__body--md">
				{componentInfo.schema.displayName}
				{componentInfo.schema.icon && (
					<span className={styles.tag}>{componentInfo.schema.icon}</span>
				)}
			</h4>
			<div className={styles.thumbnail} />
			{componentInfo.schema.description && (
				<p className="collect__body--xs">{componentInfo.schema.description}</p>
			)}
		</div>,
		document.body
	)
}
