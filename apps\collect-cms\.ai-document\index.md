# CollectCMS Documentation

This directory contains comprehensive documentation for the CollectCMS application, including architecture diagrams, flow charts, and code structure explanations.

## Table of Contents

For an explanation of how this documentation is organized, see the [Documentation Structure](./meta/documentation-structure.md) file.

### Core Documentation

1. [Architecture Overview](./core/architecture-overview.md)

   - High-level system architecture
   - Key components diagram
   - Data flow diagram
   - Technology stack

2. [Application Flow](./core/application-flow.md)

   - Authentication flow
   - Content management flow
   - Page builder flow
   - Navigation flow
   - Data fetching flow

3. [Component Structure](./core/component-structure.md)

   - Component hierarchy
   - Core components
   - Layout components
   - Builder components
   - Form components
   - Navigation components
   - Authentication components

4. [Data Models and API Integration](./core/data-models-and-api.md)

   - Data models
   - API integration
   - Authentication API
   - Content type API
   - Content management API
   - Media API
   - Error handling

5. [Codebase Structure](./core/codebase-structure.md)
   - Directory structure
   - Key files
   - Module organization
   - Dependency structure
   - Build configuration

### Specialized Documentation

6. [Routing Documentation](./features/routing-documentation.md)

   - Directory structure
   - Route types
   - Authentication flow
   - Internationalization
   - Route groups
   - Dynamic routes
   - Middleware
   - Layout hierarchy

7. [CMS Integration](./features/cms-integration.md)

   - Environment configuration
   - API communication
   - Content types
   - Authentication
   - Media management
   - Internationalization
   - Error handling

8. [Navigation Structure](./features/navigation-structure.md)

   - Navigation data structure
   - Navigation context
   - Sidebar navigation
   - Content type navigation
   - Route resolution
   - Navigation state management

9. [Authentication Flow](./features/authentication-flow.md)

   - Authentication components
   - Login process
   - Token management
   - Route protection
   - Logout process
   - Authentication state
   - Security considerations

10. [Page Builder System](./features/page-builder-system.md)
    - Component structure
    - Page builder routes
    - Builder context
    - Layout editor
    - Component editor
    - Data flow
    - Integration with Strapi

## System Overview

CollectCMS is a headless CMS frontend built with Next.js, designed to integrate with a Strapi backend. It provides a user-friendly interface for content management, with features like:

- Content creation and editing
- Media management
- User authentication
- Internationalization
- Dynamic content types and components

### Key Features

```mermaid
flowchart TD
  CollectCMS((CollectCMS))

  CollectCMS --> Auth[Authentication]
  CollectCMS --> CM[Content Management]
  CollectCMS --> PB[Page Builder]
  CollectCMS --> I18n[Internationalization]
  CollectCMS --> Integration[Integration]

  Auth --> Auth1[JWT-based auth]
  Auth --> Auth2[Protected routes]
  Auth --> Auth3[User management]

  CM --> CM1[Single types]
  CM --> CM2[Collection types]
  CM --> CM3[Dynamic fields]
  CM --> CM4[Media library]

  PB --> PB1[Visual editor]
  PB --> PB2[Component library]
  PB --> PB3[Drag and drop]
  PB --> PB4[Preview]

  I18n --> I18n1[Multi-language support]
  I18n --> I18n2[Language switching]
  I18n --> I18n3[Localized content]

  Integration --> Int1[Strapi backend]
  Integration --> Int2[API communication]
  Integration --> Int3[Data synchronization]
```

### Architecture Diagram

```mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js App]
    NextJS --> Middleware[Authentication Middleware]
    NextJS --> LayoutSystem[Layout System]
    NextJS --> PageBuilder[Page Builder]
    NextJS --> ContentManager[Content Manager]
    NextJS --> API[API Integration Layer]
    API --> Strapi[Strapi Backend]

    subgraph "CollectCMS Frontend"
        Middleware
        LayoutSystem
        PageBuilder
        ContentManager
        API
    end
```

### Application Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant NextJS as Next.js App
    participant API as API Integration
    participant Strapi as Strapi Backend

    User->>Browser: Access CMS
    Browser->>NextJS: Request page
    NextJS->>API: Check authentication
    API->>Strapi: Validate token
    Strapi-->>API: Token valid/invalid

    alt Token Valid
        API-->>NextJS: Authenticated
        NextJS->>API: Fetch content types
        API->>Strapi: Request content types
        Strapi-->>API: Return content types
        API-->>NextJS: Content types data
        NextJS-->>Browser: Render CMS interface
        Browser-->>User: Display CMS
    else Token Invalid
        API-->>NextJS: Not authenticated
        NextJS-->>Browser: Redirect to login
        Browser-->>User: Show login page
    end
```

## Getting Started

To run the CollectCMS application:

1. Ensure you have the required environment variables set up:

   ```
   NEXT_PUBLIC_STRAPI_HOST=https://your-strapi-instance.com
   NEXT_PUBLIC_STRAPI_API_KEY=your-strapi-api-key
   NEXT_PUBLIC_STRAPI_POST_API_KEY=your-strapi-post-api-key
   STRAPI_COLLECT_CMS_API_KEY=your-strapi-cms-api-key
   ```

2. Install dependencies with `yarn install`
3. Run the development server with `yarn dev` or `yarn dev:cms` from the monorepo root
4. Access the application at `http://localhost:3000`

## Development Workflow

The typical development workflow for CollectCMS includes:

1. Setting up the environment
2. Understanding the codebase structure
3. Making changes to components or API integration
4. Testing changes locally
5. Building for production with `yarn build` or `yarn build-cloudflare`
6. Deploying to Vercel or Cloudflare

## Key Technologies

- **Frontend Framework**: Next.js with App Router
- **UI Components**: Custom React components
- **Styling**: SCSS modules
- **State Management**: React Context API
- **API Communication**: Fetch API
- **Authentication**: JWT tokens
- **Internationalization**: i18n with next-i18n-router
- **Deployment**: Supports Vercel and Cloudflare

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Strapi Documentation](https://docs.strapi.io)
- [React Documentation](https://reactjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
