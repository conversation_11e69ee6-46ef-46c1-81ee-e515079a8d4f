'use client'

import { createContext } from 'react'

export type INavigationKind = 'collectionType' | 'singleType'

export type INavigationLayoutProps = {
	name: string
	uid: string
	apiId: string
	kind: INavigationKind
	visible: boolean
	identifierField: string
	defaultMode: 'builder' | 'editor'
}

/**
 * Navigation props
 *
 * @param uid - Unique identifier for the CMS content type
 * @param apiId - API identifier used for client/browser requests
 * @param isDisplayed - Whether the navigation item is visible in the UI
 * @param isPinned - Whether the navigation item is pinned to stay in view
 * @param info - Display information for the navigation item
 * @param kind - Type of navigation item (must be 'group' at the first level)
 * @param layouts - Child navigation items contained within this group
 *
 */
export type INavigationProps = {
	uid: string
	apiId: string
	isDisplayed: boolean
	isPinned: boolean
	info: {
		description?: string
		displayName: string
	}
	kind: 'group'
	layouts: INavigationLayoutProps[]
}

const GLOBAL_ROUTE: INavigationProps[] = [
	{
		uid: '',
		apiId: '',
		isDisplayed: false,
		isPinned: false,
		info: {
			description: undefined,
			displayName: '',
		},
		kind: 'group',
		layouts: [],
	},
]

const defaultContext: INavigationProps[] = GLOBAL_ROUTE

export const NavigationContext = createContext(defaultContext)

export default function NavigationProvider({
	data,
	children,
}: {
	data: INavigationProps[]
	children: React.ReactNode
}) {
	return <NavigationContext.Provider value={data}>{children}</NavigationContext.Provider>
}
