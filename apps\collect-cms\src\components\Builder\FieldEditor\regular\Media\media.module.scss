@import '@/styles/config';

.wrapper {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	align-items: start;
	gap: spacing(s5);
	position: relative;
}

.empty {
	height: var(--height);
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: color('white', 5);
	color: color('grey', 50);
	gap: px-to(10px, rem);
	cursor: pointer;
	svg {
		--icon-size: #{spacing(s8)};
	}
	p {
		@include fluid($font-size) {
			font-size: size('body', 'sm');
		}
		button {
			padding: 0;
			color: color('blue', 100);
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('body', 'sm');
			}
		}
	}
	small {
		@include fluid($font-size) {
			font-size: size('body', 'xs');
		}
	}
}

.controller {
	position: relative;
	display: grid;
	// grid-column: span 8;
	grid-column: span var(--controller-cols);
	border: px-to(1px, rem) solid color('grey', 15);
	border-radius: spacing(s2);
}

.nav {
	position: absolute;
	right: spacing(s6);
	bottom: calc(100% + spacing(s3));
	display: inline-flex;
	align-items: center;
	gap: spacing(s3);
	color: color('grey', 90);
	button.nav__btn {
		padding: 0;
		@include fluid($font-size) {
			--size: #{size('body', 'md')};
		}
	}
	&__index {
		font-weight: 600;
		@include fluid($font-size) {
			--size: #{size('body', 'sm')};
		}
	}
}

.body {
	position: relative;
	display: grid;
	overflow: hidden;
	background-image: url('/mediaPreview.svg');
	border-radius: spacing(s2);
	transition: 0.2s all var(--ease-transition-2);
	&.detailed {
		padding: spacing(s4) spacing(s2);
	}
	&.detailed__multi {
		padding: spacing(s4) spacing(s2) px-to(72px, rem);
	}
	&.detailed,
	&.detailed__multi {
		.item {
			display: block;
		}
		.thumbnail img {
			height: auto;
			width: auto;
			object-fit: contain;
		}
	}
}

.items {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: spacing(s2);
	gap: spacing(s4);
	background-color: rgba(42, 42, 42, 0.6);
	&__list {
		display: flex;
		align-items: center;
		gap: spacing(s2);
	}
	&__thumb {
		width: px-to(62px, rem);
		height: px-to(40px, rem);
		border-radius: spacing(s1);
		overflow: hidden;
		border: px-to(2px, rem) solid transparent;
		cursor: pointer;
		&.active {
			border-color: color('white', 0);
		}
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	&__nav {
		--size: #{spacing(s4)};
		color: color('white', 0);
		cursor: pointer;
	}
}

.item {
	display: grid;
	position: relative;
	// height: 100%;
	height: var(--height);
	&:hover {
		.mask {
			display: flex;
		}
	}
}

.tag {
	padding: px-to(2px, rem) spacing(s2);
	background-color: color('grey', 10);
	border-radius: spacing(s8);
	position: absolute;
	left: spacing(s2);
	top: spacing(s2);
	text-transform: uppercase;
	font-weight: 500;
	z-index: 2;
	@include fluid($font-size) {
		font-size: size('body', 'xs');
	}
}

.thumbnail {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	img {
		max-width: 100%;
		max-height: 100%;
		height: 100%;
		width: 100%;
		object-fit: cover;
	}
}

.mask {
	position: absolute;
	width: 100%;
	height: 100%;
	display: none;
	align-items: center;
	justify-content: center;
	background-color: rgba(69, 69, 69, 0.5);
	z-index: 1;
	button {
		background-color: color('grey', 10);
		padding: spacing(s4);
		border-radius: spacing(s1);
		flex-shrink: 0;
		--size: #{px-to(17px, rem)};
		span {
			height: px-to(17px, rem);
		}
		svg {
			margin-top: px-to(-1 * 4px, rem);
		}
	}
}

.toolbar {
	background-color: color('grey', 10);
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 spacing(s2);
	&__list {
		display: inline-flex;
		align-items: center;
	}
	&__button {
		display: flex;
		justify-content: center;
		align-items: center;
		width: px-to(50px, rem);
		height: px-to(50px, rem);
		padding: spacing(s4);
		color: color('grey', 90);
		font-weight: 600;
		cursor: pointer;
		@include fluid($font-size) {
			--size: #{size('body', 'md')};
		}

		&.text {
			width: auto;
		}

		&:hover {
			background-color: color('grey', 15);
		}
	}
}

.info {
	display: grid;
	gap: spacing(s4);
	grid-column: span var(--info-cols);
	&__title {
		display: flex;
		align-items: center;
		gap: spacing(s3);
		button {
			cursor: pointer;
			--size: #{px-to(16px, rem)};
		}
	}
	&__media {
		position: relative;
		display: grid;
		overflow: hidden;
		border-radius: spacing(s2);
	}
	&__fixed {
		height: fit-content;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		align-items: start;
		gap: spacing(s5);
		background-color: color('grey', 10);
		border-radius: spacing(s1);
		padding: spacing(s3) spacing(s5);
		&_item {
			grid-column: span 1;
			display: grid;
			gap: px-to(2px, rem);
			text-transform: uppercase;
			color: color('grey', 70);
		}
		&_label {
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('body', 'sm');
			}
		}
		&_value {
			@include fluid($font-size) {
				font-size: size('body', 'xs');
			}
		}
	}
	&__editable {
		padding: spacing(s2) 0;
		display: grid;
		gap: spacing(s4);
		&_item {
			display: grid;
			gap: spacing(s3);
			color: color('grey', 90);
			label {
				font-weight: 600;
				@include fluid($font-size) {
					font-size: size('body', 'md');
				}
			}
			input {
				padding: spacing(s2) spacing(s3);
				border-color: color('grey', 20);
				--input-radius: #{spacing(s1)};
				@include fluid($font-size) {
					font-size: size('body', 'sm');
				}
				&::placeholder {
					color: color('grey', 40);
				}
			}
		}
	}
	&__builder {
		background-color: color('white', 5);
		position: fixed;
		top: calc(var(--toolbar-height) + spacing(s4));
		left: calc(px-to(320px, rem) + spacing(s4));
		width: px-to(320px, rem);
		padding: spacing(s6);
		border-radius: spacing(s2);
		border: px-to(1px, rem) solid color('grey', 15);
		box-shadow: 0 spacing(s1) spacing(s2) rgba(69, 69, 69, 0.04);
	}
}
