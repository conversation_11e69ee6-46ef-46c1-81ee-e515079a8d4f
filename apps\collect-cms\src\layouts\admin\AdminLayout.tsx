import cn from 'classnames'
import type { ReactNode } from 'react'
import { UserMenu } from '@/components/UserMenu'
import { AdminSidebar } from '@/layouts/admin/AdminSidebar'
import styles from './adminlayout.module.scss'

export const AdminLayout = (props: { children: ReactNode }) => {
	const { children } = props
	return (
		<div className={cn(styles.wrapper)}>
			<AdminSidebar />
			<main>
				<div className={styles.header}>
					<div className={styles.header_title}>Dashboard</div>
					<div className={styles.header_actions}>
						<UserMenu />
					</div>
				</div>
				<div className={styles.content}>{children}</div>
			</main>
		</div>
	)
}
