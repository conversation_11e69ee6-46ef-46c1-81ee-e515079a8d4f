@import '@/styles/config';

.wrapper {
	display: grid;
	align-items: start;
	grid-template-columns: repeat(12, 1fr);
	gap: spacing(s6);
}

.layout {
	&__editor {
		display: grid;
		align-items: start;
		background-color: color('white', 0);
		padding: spacing(s8);
		border-radius: 0 spacing(s2) spacing(s2) spacing(s2);
		box-shadow: 0 spacing(s1) spacing(s6) rgba(69, 69, 69, 0.15);
		border-top: spacing(s1) solid color('yellow', 80);
		grid-column: span 9;
	}
	&__info {
		display: flex;
		flex-direction: column;
		gap: spacing(s5);
		grid-column: span 3;
	}
	&__popup {
		position: fixed;
		width: 100vw;
		height: 100vh;
		display: flex;
		// display: none;
		justify-content: flex-end;
		top: 0;
		left: 0;
		z-index: 9;
		background-color: rgba(0, 0, 0, 0.8);
	}
}

.editor {
	display: grid;
	gap: spacing(s8);
	&__section {
		display: grid;
		gap: spacing(s2);
		h6 {
			color: color('grey', 90);
		}
	}
	&__components {
		display: grid;
		grid-template-columns: repeat(12, 1fr);
		gap: spacing(s8);
		padding: spacing(s6);
		border-radius: spacing(s2);
		background-color: color('white', 5);
		& > div {
			padding: 0;
		}
	}
}

.info {
	display: grid;
	gap: spacing(s5);
	background-color: color('white', 0);
	border-radius: spacing(s2);
	padding: spacing(s5) spacing(s4);
	box-shadow: 0 spacing(s1) spacing(s6) rgba(69, 69, 69, 0.15);

	.lastupdate {
		color: color('grey', 70);
		margin-top: calc(-1 * spacing(s2));
		font-weight: 500;
		@include fluid($font-size) {
			font-size: size('body', 'sm');
		}
	}
	&__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		p {
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('body', 'md');
			}
		}
		button {
			padding: px-to(10px, rem) spacing(s3);
		}
	}
	&__body {
		display: grid;
		overflow: hidden;
	}
	&__item {
		display: grid;
		gap: spacing(s2);
		padding: spacing(s2) 0 spacing(s4);
		border-top: px-to(1px, rem) solid color('grey', 15);
	}
	&__label {
		display: flex;
		align-items: center;
		justify-content: space-between;
		p {
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('body', 'sm');
			}
		}
	}
	&__value {
		display: flex;
		flex-wrap: wrap;
		gap: px-to(6px, rem);
		font-weight: 500;
		color: color('grey', 70);
		@include fluid($font-size) {
			font-size: size('body', 'xs');
		}

		.tag {
			padding: px-to(2px, rem) spacing(s2);
			background-color: color('grey', 10);
			border-radius: 50%;
			@include fluid($font-size) {
				font-size: size('body', 'xs');
			}
		}
	}
}

.popup {
	width: 50%;
	height: 100%;
	display: grid;
	align-items: start;
	padding: spacing(s8) spacing(s6);
	background-color: color('white', 5);
	border-radius: spacing(s5) 0 0 spacing(s5);
	gap: spacing(s7);

	&__header {
		display: grid;
		gap: spacing(s4);
		button {
			cursor: pointer;
			width: fit-content;
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('label', 'subheader');
			}
		}
	}

	&__title {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	&__body {
		display: grid;
		height: 100%;
		overflow-y: scroll;
		grid-template-columns: repeat(12, 1fr);
		padding: spacing(s8);
		gap: spacing(s8);
		border-radius: spacing(s2);
		box-shadow: 0 spacing(s1) spacing(s6) rgba(69, 69, 69, 0.15);
		& > div {
			padding: 0;
		}
	}
}
