'use client'

import type { INavigationProps } from '@collective/integration-lib/cms'
import { createContext } from 'react'

const GLOBAL_ROUTE = [
	{
		label: 'Our Podcast',
		path: '/our-podcast',
		isLocked: false,
	},
	{
		label: 'Work with us',
		path: '/work-with-us',
		isLocked: false,
	},
	{
		label: 'Newsletters',
		path: '/newsletters',
		isLocked: false,
	},
]

const defaultContext: INavigationProps[] = GLOBAL_ROUTE

export const NavigationContext = createContext(defaultContext)

export default function NavigationProvider({
	data,
	children,
}: {
	data: INavigationProps[]
	children: React.ReactNode
}) {
	return <NavigationContext.Provider value={data}>{children}</NavigationContext.Provider>
}
