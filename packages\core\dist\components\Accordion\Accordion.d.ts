import React from 'react';
export declare const Accordion: ({ children }: {
    children: React.ReactNode;
}) => import("react/jsx-runtime").JSX.Element;
export declare const AccordionItem: ({ title, children, onActive, isActive, isDisabled, icon, }: {
    title: React.ReactNode;
    children: React.ReactNode;
    onActive?: () => void;
    isActive?: boolean;
    isDisabled?: boolean;
    icon?: React.ReactNode;
}) => import("react/jsx-runtime").JSX.Element;
