import { Icon, Image } from '@collective/core'
import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import Link from 'next/link'
import styles from './media.module.scss'

type MediaProps = {
	IsFixedHeight?: boolean
	Media: {
		Media: IMediaProps
		IsDownloadable: boolean
		IsFullScale: boolean
		DefaultBackground: string
	}[]
}

export const Media = ({ Media, IsFixedHeight = false }: MediaProps) => {
	return (
		<section className={styles.wrapper}>
			<div
				className="aidigi__grid"
				style={
					{
						'--count': Media.map((item) => item).length,
					} as React.CSSProperties
				}
			>
				{Media?.map((item, index) => {
					const media = item?.Media
					const mediaUrl = `${process.env.NEXT_PUBLIC_STRAPI_HOST}${media?.url}?original=true&download=true`
					const { DefaultBackground } = item
					return (
						<div key={index}>
							<div
								className={cn(
									styles.media,
									item.IsFullScale && styles.full__scale,
									!IsFixedHeight && styles.auto__height
								)}
								style={DefaultBackground ? { backgroundColor: item.DefaultBackground } : {}}
							>
								<div className={styles.inner}>
									<Image media={item.Media} placeholder="empty" alt={item.Media.caption || ''} />
								</div>
								{item?.IsDownloadable && media?.url && (
									<Link target="_blank" href={mediaUrl} download className={styles.download}>
										<Icon variant="arrow-down" />
									</Link>
								)}
							</div>
							{media?.caption && <p className="aidigi__paragraph--md">{media?.caption}</p>}
						</div>
					)
				})}
			</div>
		</section>
	)
}
