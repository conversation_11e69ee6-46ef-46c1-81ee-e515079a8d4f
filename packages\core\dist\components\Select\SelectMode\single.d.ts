import React from 'react';
import { type OptionData } from '../Select';
type IconProps = {
    startIcon: React.ReactElement;
    endIcon?: never;
} | {
    endIcon: React.ReactElement;
    startIcon?: never;
} | {
    endIcon?: undefined;
    startIcon?: undefined;
};
type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
    label: string;
    required: boolean;
} & IconProps;
export type SingleItem = {
    value: string;
    children: React.ReactNode | ((data: OptionData) => React.ReactNode);
    className?: string;
};
export declare const Single: React.ForwardRefExoticComponent<ButtonProps & React.RefAttributes<HTMLButtonElement>>;
export declare function SingleItem({ value, children, className }: SingleItem): import("react/jsx-runtime").JSX.Element;
export {};
