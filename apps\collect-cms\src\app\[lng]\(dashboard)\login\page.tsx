'use client'
import { But<PERSON>, Checkbox, Icon, Input, setCookie } from '@collective/core'
import { postJsonFormData } from '@collective/integration-lib/cms'
import cn from 'classnames'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import styles from '@/layouts/auth/authlayout.module.scss'

// export const runtime = 'edge'
export default function Dashboard({ params: { lng } }: Readonly<{ params: { lng: string } }>) {
	const router = useRouter()
	const searchParams = useSearchParams()
	const redirectParam = searchParams.get('redirect')

	// Validate the redirect path to prevent redirection loops
	// Make sure the path starts with the language code
	let redirectPath = `/${lng}`

	if (redirectParam && !redirectParam.includes('/login') && redirectParam.startsWith('/')) {
		// If the redirect path doesn't start with the language code, add it
		if (redirectParam.split('/')[1] !== lng) {
			redirectPath = `/${lng}${redirectParam}`
		} else {
			redirectPath = redirectParam
		}
	}

	console.log('Redirect path:', redirectPath)

	const [isLoading, setIsLoading] = useState(false)
	const [isPwVisible, setIsPwVisible] = useState(false)
	const [rememberMe, setRememberMe] = useState(false)
	const [formData, setFormData] = useState({ email: '', password: '' })
	const [errors, setErrors] = useState({ email: '', password: '', overall: '' })

	// Check if user is already logged in
	useEffect(() => {
		// Only run on client side
		if (typeof window !== 'undefined') {
			const token = localStorage.getItem('adminJwt') || sessionStorage.getItem('adminJwt')
			if (token) {
				console.log('User already logged in, redirecting to:', redirectPath)
				// Use window.location.href for a full page refresh
				window.location.href = redirectPath
			}
		}
	}, [redirectPath])

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target
		setFormData((prev) => ({ ...prev, [name]: value }))
	}

	const handleValidate = () => {
		const newErrors = { email: '', password: '', overall: '' }

		if (!formData.email) newErrors.email = 'Email is required!'
		else if (!/^\S[^\s@]*@\S[^\s.]*\.\S+$/.test(formData.email))
			newErrors.email = 'Invalid email format!'

		if (!formData.password) newErrors.password = 'Password is required!'
		else if (formData.password.length < 6)
			newErrors.password = 'Password must be at least 6 characters long!'

		setErrors(newErrors)
		return Object.values(newErrors).every((err) => err === '')
	}

	const handleShowPws = () => {
		setIsPwVisible(!isPwVisible)
	}

	const handleRememberMeChange = (checked: boolean) => {
		setRememberMe(checked)
	}

	const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault()
		setIsLoading(true)

		// Validating...
		if (!handleValidate()) {
			setIsLoading(false)
			return
		}
		const email = formData.email
		const password = formData.password

		try {
			if (localStorage) {
				const response = await postJsonFormData<{
					data: {
						token: string
						user: { firstname: string; lastname: string | null; username: string | null }
					}
				}>({
					fullPath: `${process.env.NEXT_PUBLIC_STRAPI_HOST}/admin/login`,
					body: JSON.stringify({ email, password }),
				})
				// Clear any existing tokens
				localStorage.removeItem('adminJwt')
				sessionStorage.removeItem('adminJwt')
				setCookie('adminJwt', '', 1)

				// Set the token in the appropriate storage based on "Remember me"
				setCookie('adminJwt', response.data.token || '', rememberMe ? 30 : 1)

				if (rememberMe) {
					localStorage.setItem('adminJwt', response.data.token || '')
				} else {
					sessionStorage.setItem('adminJwt', response.data.token || '')
				}

				if (response.data.token) {
					localStorage.setItem('adminJwt', response.data.token)
					localStorage.setItem('adminUser', JSON.stringify(response.data.user))
					setCookie('adminJwt', response.data.token, 30)
					console.log('Logged in with token:', response.data.token)

					// Set a shorter timeout and make sure to reset loading state
					setIsLoading(false)
					setErrors({ email: '', password: '', overall: '' })

					// Navigate to the validated redirect path immediately
					// This helps avoid the "stuck" state
					console.log('Login successful, redirecting to:', redirectPath)
					window.location.href = redirectPath
				}
			}
		} catch (error) {
			console.log(error)
			setIsLoading(false)
			setErrors({
				...errors,
				overall: 'Login failed. Please check your credentials and try again.',
			})
		}
	}
	return (
		<>
			<div className={styles.auth}>
				<div className={styles.auth__header}>
					<div className={styles.auth__logo}>
						<Image src="/CoAI.png" width={80} height={80} alt="brand_logo" />
					</div>
					<div className={styles.auth__title}>
						<h2>Welcome back</h2>
						<span>Glad to see you again. Log in to continue your works</span>
					</div>
				</div>
				<form onSubmit={handleLogin} noValidate>
					<div className={styles.auth__form}>
						{/* Email */}
						<div className={cn(styles.auth__controller, errors.email ? styles.auth__error : '')}>
							<Input
								name="email"
								type="email"
								placeholder="Enter your email address"
								value={formData.email}
								onChange={handleChange}
								autoComplete="off"
								startIcon={<Icon type="cms" className={styles.auth__icon} variant="mail" />}
							/>

							{errors.email && <small className={styles.auth__error__msg}>{errors.email}</small>}
						</div>

						{/* Password */}
						<div className={cn(styles.auth__controller, errors.password ? styles.auth__error : '')}>
							<Input
								name="password"
								type={isPwVisible ? 'input' : 'password'}
								placeholder="Password"
								value={formData.password}
								onChange={handleChange}
								autoComplete="off"
								startIcon={<Icon type="cms" className={styles.auth__icon} variant="padlock" />}
								endIcon={
									<Button className={cn(styles.auth__icon, styles)} onClick={handleShowPws}>
										{isPwVisible ? (
											<Icon type="cms" variant="hide" />
										) : (
											<Icon type="cms" variant="show" />
										)}
									</Button>
								}
							/>

							{errors.password && (
								<small className={styles.auth__error__msg}>{errors.password}</small>
							)}
						</div>

						{/* Remember */}
						<div className={styles.remember}>
							<Checkbox
								label="Remember me"
								checked={rememberMe}
								onChange={(e) => handleRememberMeChange(e.target.checked)}
							/>
							<Link href="#">Forget Password</Link>
						</div>
					</div>

					{/* Overall error message */}
					{errors.overall && (
						<div className={styles.auth__error}>
							<small className={styles.auth__error__msg}>{errors.overall}</small>
						</div>
					)}

					{/* Submit */}
					<Button
						type="submit"
						className={cn('aidigi__button', styles.submit, isLoading ? 'disabled' : '')}
						disabled={isLoading}
					>
						{isLoading ? 'Signing in...' : 'Sign in'}
					</Button>

					{/* Note */}
					<span className={styles.note}>
						Have problem with log in? <Link href="#">Contact support</Link>
					</span>
				</form>
			</div>
			<span className={styles.auth__copyright}>2025 Collect Design Agency, All right reserved</span>
		</>
	)
}
