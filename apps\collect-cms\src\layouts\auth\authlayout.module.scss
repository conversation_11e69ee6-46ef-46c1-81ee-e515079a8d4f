@import '@/styles/config';

.wrapper {
	display: grid;
	position: relative;
	height: 100vh;
	overflow: hidden;

	main {
		padding: spacing(s15) spacing(s8) spacing(s10);
		background: color('white', 5);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}
}

.auth {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: spacing(s9);
	text-align: center;
	height: 100%;
	&__header {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: spacing(s11);
	}

	&__logo {
		border: px-to(1px, rem) solid color('grey', 20);
		border-radius: spacing(s4);
		background-color: color('white', 0);
	}

	&__title {
		display: grid;
		gap: spacing(s2);
		h2 {
			color: color('grey', 90);
			font-weight: 600;
			@include fluid($font-size) {
				font-size: size('heading', 'h2');
			}
		}
		span {
			color: color('grey', 50);
			white-space: nowrap;
		}
	}

	form {
		display: grid;
		gap: spacing(s5);
		width: px-to(360px, rem);
	}

	&__form {
		display: grid;
		gap: spacing(s3);
	}

	&__controller {
		display: grid;
		gap: spacing(s1);
		input {
			--input-bg: #{color('white', 0)};
			--input-radius: #{spacing(s1)};
			--input-padding-x: #{spacing(s3)};
			--input-padding-y: #{spacing(s3)};
			--input-height: auto;
			border: px-to(1px, rem) solid color('grey', 15);
			box-shadow: none;
			font-weight: 400 !important;
			@include fluid($font-size) {
				font-size: size('body', 'md');
			}
			&::placeholder {
				color: color('grey', 40);
			}
			&:hover {
				border-color: color('black', 0);
			}
			&:focus {
				box-shadow: none;
				border-color: color('black', 0);
			}
		}

		svg {
			cursor: default;
		}
	}

	&__error {
		text-align: left;
		color: color('red');

		input {
			border: px-to(1px, rem) solid color('red');

			&:hover {
				border-color: color('red');
			}
		}

		&__msg {
			color: color('red', 100);
			font-weight: 500;
			@include fluid($font-size) {
				font-size: size('body', 'xs');
			}
		}
	}

	.remember {
		display: flex;
		justify-content: space-between;
		align-items: center;

		* {
			color: color('black', 0);
			font-weight: 500;
			@include fluid($font-size) {
				font-size: size('body', 'sm');
			}
		}

		input {
			& + label {
				display: flex;
				align-items: center;
				gap: spacing(s2);
				--checkbox-size: #{px-to(18px, rem)};
				--checkbox-radius: #{px-to(4px, rem)};
				--checkbox-color: #{color('grey', 20)};
				--checkbox-active-color: #{color('black', 0)};
			}
			& + label::after {
				top: px-to(-1.5px, rem) !important;
				left: px-to(-4px, rem) !important;
				width: px-to(26px, rem) !important;
				height: px-to(26px, rem) !important;
			}

			&:checked + label::after {
				background-color: color('white', 0);
			}
		}
		a {
			&:hover {
				text-decoration: underline;
			}
		}
	}

	button.submit {
		--button-padding-y: #{spacing(s3)};
		background-color: color('black', 0);
		color: color('white', 0);
		border: px-to(1px, rem) solid color('black', 0);
		font-weight: 600;
		@include fluid($font-size) {
			font-size: size('body', 'lg');
		}
		&:hover {
			background-color: color('white', 0);
			color: color('black', 0);
		}
	}

	.note {
		color: color('grey', 40);
		@include fluid($font-size) {
			font-size: size('body', 'sm');
		}

		a {
			color: color('black', 0);
			font-weight: 500;
			&:hover {
				text-decoration: underline;
			}
		}
	}

	&__copyright {
		color: color('grey', 40);
		@include fluid($font-size) {
			font-size: size('body', 'xs');
		}
	}
}
