import { checkValidJWT, type IUserJWTProps } from '@collective/core'
import { deleteChat } from '@collective/integration-lib/cms'
import { cookies } from 'next/headers'

// export const runtime = 'edge'

export async function DELETE(request: Request) {
	const { searchParams } = new URL(request.url)
	const id = searchParams.get('id')

	const cookieStore = cookies()
	const authHeaeder = cookieStore.get('token')?.value
	if (!authHeaeder) {
		return Response.json({ error: 'Invalid token' }, { status: 401 })
	}
	const check = await checkValidJWT<IUserJWTProps>(authHeaeder)
	if (!check) {
		return Response.json({ error: 'Invalid token' }, { status: 401 })
	}
	if (!id) {
		return Response.json({ error: 'Invalid id' }, { status: 400 })
	}

	const response = await deleteChat(id)

	return Response.json({ response }, { status: 200 })
}
