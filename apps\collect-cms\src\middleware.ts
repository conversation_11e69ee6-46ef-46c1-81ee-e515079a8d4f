import { fallbackLng, languages } from '@collective/i18n'
import { middleware as i18nMiddleware } from '@collective/i18n/middleware'
import {
	NextResponse,
	type NextFetchEvent,
	type NextMiddleware,
	type NextRequest,
} from 'next/server'

type MiddlewareFactory = (middleware: NextMiddleware) => NextMiddleware
function stackMiddlewares(functions: MiddlewareFactory[] = [], index = 0): NextMiddleware {
	const current = functions[index]
	if (current) {
		const next = stackMiddlewares(functions, index + 1)
		return current(next)
	}
	return () => NextResponse.next()
}

const checkAuthMiddleware: MiddlewareFactory = (next) => {
	return async (request: NextRequest, _next: NextFetchEvent) => {
		const pathname = request.nextUrl.pathname

		// Extract the language from the URL or use default
		const pathParts = pathname.split('/')
		const lng = pathParts[1] && languages.includes(pathParts[1]) ? pathParts[1] : fallbackLng

		// Check if the route is a public route that doesn't require authentication
		const isPublicRoute =
			pathname.includes('/login') ||
			pathname.includes('/forgot-password') ||
			pathname.includes('/reset-password')

		// If it's not a public route, check for authentication
		if (!isPublicRoute) {
			// Get the token from cookies
			const token = request.cookies.get('adminJwt')?.value

			// If no token is found, redirect to login page
			if (!token) {
				// Create the login URL with the correct language
				const loginUrl = new URL(`/${lng}/login`, request.url)

				// Add a redirect parameter to return to the original page after login
				// Make sure the redirect path is properly formatted
				const redirectPath = pathname
				console.log('Setting redirect path:', redirectPath)

				loginUrl.searchParams.set('redirect', redirectPath)

				return NextResponse.redirect(loginUrl)
			}
		}
		return next(request, _next)
	}
}

const i18nWrapperMiddleware: MiddlewareFactory = (next) => {
	return async (request: NextRequest, _next: NextFetchEvent) => {
		const pathname = request.nextUrl.pathname

		if (['/']?.some((path) => pathname.startsWith(path))) {
			return i18nMiddleware(request)
		}
		return next(request, _next)
	}
}

const middlewares = [checkAuthMiddleware, i18nWrapperMiddleware]
export default stackMiddlewares(middlewares)

// applies this middleware only to files in the app directory
export const config = {
	matcher: ['/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js).*)'],
}
