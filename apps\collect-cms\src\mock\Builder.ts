import type { IComponentProps } from '@collective/integration-lib/cms'
import type {
	IConfigurationProps,
	IContentTypeProps,
	IGlobalInit,
	IMultiDataProps,
	IRelationResultDataProps,
	IResultDataProps,
	IResultMetaProps,
} from 'common/cms'

export const initApiData: { data: IGlobalInit } = {
	data: {
		fieldSizes: {
			dynamiczone: {
				default: 12,
				isResizable: false,
			},
			component: {
				default: 12,
				isResizable: false,
			},
			json: {
				default: 12,
				isResizable: false,
			},
			richtext: {
				default: 12,
				isResizable: false,
			},
			blocks: {
				default: 12,
				isResizable: false,
			},
			checkbox: {
				default: 4,
				isResizable: true,
			},
			boolean: {
				default: 4,
				isResizable: true,
			},
			date: {
				default: 4,
				isResizable: true,
			},
			time: {
				default: 4,
				isResizable: true,
			},
			biginteger: {
				default: 4,
				isResizable: true,
			},
			decimal: {
				default: 4,
				isResizable: true,
			},
			float: {
				default: 4,
				isResizable: true,
			},
			integer: {
				default: 4,
				isResizable: true,
			},
			number: {
				default: 4,
				isResizable: true,
			},
			datetime: {
				default: 6,
				isResizable: true,
			},
			email: {
				default: 6,
				isResizable: true,
			},
			enumeration: {
				default: 6,
				isResizable: true,
			},
			media: {
				default: 6,
				isResizable: true,
			},
			password: {
				default: 6,
				isResizable: true,
			},
			relation: {
				default: 6,
				isResizable: true,
			},
			string: {
				default: 6,
				isResizable: true,
			},
			text: {
				default: 6,
				isResizable: true,
			},
			timestamp: {
				default: 6,
				isResizable: true,
			},
			uid: {
				default: 6,
				isResizable: true,
			},
			'plugin::multi-select.multi-select': {
				default: 12,
				isResizable: true,
			},
		},
		components: [
			{
				uid: 'test.test-component',
				isDisplayed: true,
				apiID: 'test-component',
				category: 'test',
				info: {
					displayName: 'Test Component',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					TextShort: {
						type: 'string',
						minLength: 1,
						maxLength: 255,
						unique: true,
					},
					TextLong: {
						type: 'text',
						minLength: 1,
						maxLength: 100000,
						unique: true,
					},
					Boolean: {
						type: 'boolean',
					},
					RichTextJson: {
						type: 'blocks',
					},
					JSON: {
						type: 'json',
					},
					Integer: {
						type: 'integer',
					},
					BigInteger: {
						type: 'biginteger',
					},
					Decimal: {
						type: 'decimal',
					},
					Float: {
						type: 'float',
					},
					Email: {
						type: 'email',
					},
					Date: {
						type: 'date',
					},
					DateTime: {
						type: 'datetime',
					},
					TIme: {
						type: 'time',
					},
					Password: {
						type: 'password',
					},
					MediaMultiple: {
						type: 'media',
						multiple: true,
						required: false,
						allowedTypes: ['images', 'files', 'videos', 'audios'],
					},
					MediaSingle: {
						type: 'media',
						multiple: false,
						required: false,
						allowedTypes: ['images', 'files', 'videos', 'audios'],
					},
					Enum: {
						type: 'enumeration',
						enum: ['Value1', 'Value2', 'Value3'],
					},
					RelationSingle: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'api::test.test',
						targetModel: 'api::test.test',
						relationType: 'oneToOne',
					},
					RelationMany: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::test.test',
						targetModel: 'api::test.test',
						relationType: 'oneToMany',
					},
					UID: {
						type: 'uid',
						targetField: 'TextShort',
					},
					RichTextMarkdown: {
						type: 'richtext',
					},
					Level1Elm: {
						type: 'component',
						repeatable: true,
						component: 'test.level1',
					},
					CKEditorSimple: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					CKEditorSimpleParagraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					CKEditorLight: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'light',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					Color: {
						type: 'string',
						regex: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
						customField: 'plugin::color-picker.color',
					},
					MultiSelec: {
						type: 'json',
						default: '[]',
						options: ['Multi1', 'Multi2', 'Multi3'],
						required: false,
						min: 0,
						max: 100,
						customField: 'plugin::multi-select.multi-select',
					},
				},
			},
			{
				uid: 'test.level3',
				isDisplayed: true,
				apiID: 'level3',
				category: 'test',
				info: {
					displayName: 'Level3',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Level3Field: {
						type: 'string',
					},
				},
			},
			{
				uid: 'test.level2',
				isDisplayed: true,
				apiID: 'level2',
				category: 'test',
				info: {
					displayName: 'Level2',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Level2Field: {
						type: 'string',
						required: false,
						unique: false,
					},
					Level3Elm: {
						type: 'component',
						repeatable: true,
						component: 'test.level3',
					},
				},
			},
			{
				uid: 'test.level1',
				isDisplayed: true,
				apiID: 'level1',
				category: 'test',
				info: {
					displayName: 'Level1',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Level1Field: {
						type: 'string',
					},
					Level2Elm: {
						type: 'component',
						repeatable: false,
						component: 'test.level2',
					},
				},
			},
			{
				uid: 'shared.seo',
				isDisplayed: true,
				apiID: 'seo',
				category: 'shared',
				info: {
					displayName: 'seo',
					icon: 'search',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					metaTitle: {
						required: true,
						type: 'string',
						maxLength: 60,
					},
					metaDescription: {
						type: 'string',
						required: true,
						maxLength: 160,
						minLength: 50,
					},
					metaImage: {
						type: 'media',
						multiple: false,
						required: false,
						allowedTypes: ['images', 'files', 'videos'],
					},
					metaSocial: {
						type: 'component',
						repeatable: true,
						component: 'shared.meta-social',
					},
					keywords: {
						type: 'text',
						regex: '[^,]+',
					},
					metaRobots: {
						type: 'string',
						regex: '[^,]+',
					},
					structuredData: {
						type: 'json',
					},
					metaViewport: {
						type: 'string',
					},
					canonicalURL: {
						type: 'string',
					},
				},
			},
			{
				uid: 'shared.meta-social',
				isDisplayed: true,
				apiID: 'meta-social',
				category: 'shared',
				info: {
					displayName: 'metaSocial',
					icon: 'project-diagram',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					socialNetwork: {
						type: 'enumeration',
						enum: ['Facebook', 'Twitter'],
						required: true,
					},
					title: {
						type: 'string',
						required: true,
						maxLength: 60,
					},
					description: {
						type: 'string',
						maxLength: 65,
						required: true,
					},
					image: {
						allowedTypes: ['images', 'files', 'videos'],
						type: 'media',
						multiple: false,
					},
				},
			},
			{
				uid: 'homepage.search-bar',
				isDisplayed: true,
				apiID: 'search-bar',
				category: 'homepage',
				info: {
					displayName: 'Search Bar',
					icon: 'search',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Headline: {
						type: 'string',
						required: true,
					},
				},
			},
			{
				uid: 'homepage.list-elm',
				isDisplayed: true,
				apiID: 'list-elm',
				category: 'homepage',
				info: {
					displayName: 'List Elm',
					icon: 'bulletList',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					label: {
						type: 'string',
						required: true,
					},
					path: {
						type: 'string',
						required: true,
						default: '#',
					},
				},
			},
			{
				uid: 'homepage.hero-scene',
				isDisplayed: true,
				apiID: 'hero-scene',
				category: 'homepage',
				info: {
					displayName: 'Hero Scene',
					icon: 'house',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					cid: {
						type: 'string',
					},
					Media: {
						allowedTypes: ['images'],
						type: 'media',
						multiple: false,
						required: true,
					},
				},
			},
			{
				uid: 'general.social',
				isDisplayed: true,
				apiID: 'social',
				category: 'general',
				info: {
					displayName: 'Social',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					facebookLink: {
						type: 'string',
					},
					instagramLink: {
						type: 'string',
					},
					twitterLink: {
						type: 'string',
					},
					linkedInLink: {
						type: 'string',
					},
				},
			},
			{
				uid: 'general.newsletter',
				isDisplayed: true,
				apiID: 'newsletter',
				category: 'general',
				info: {
					displayName: 'Newsletter',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					headline: {
						type: 'string',
					},
				},
			},
			{
				uid: 'general.footer',
				isDisplayed: true,
				apiID: 'footer',
				category: 'general',
				info: {
					displayName: 'Footer',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					address: {
						type: 'string',
					},
					phone: {
						type: 'string',
					},
					email: {
						type: 'string',
					},
					copyright: {
						type: 'string',
					},
				},
			},
			{
				uid: 'general.contact-form',
				isDisplayed: true,
				apiID: 'contact-form',
				category: 'general',
				info: {
					displayName: 'Contact Form',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					headline: {
						type: 'string',
					},
				},
			},
			{
				uid: 'common.text-horizon',
				isDisplayed: true,
				apiID: 'text-horizon',
				category: 'common',
				info: {
					displayName: 'Text Horizon',
					icon: 'bold',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					BlockQuote: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						required: false,
						customField: 'plugin::ckeditor.CKEditor',
					},
					Headline: {
						type: 'string',
					},
					Paragraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						required: false,
						customField: 'plugin::ckeditor.CKEditor',
					},
				},
			},
			{
				uid: 'common.newsletter',
				isDisplayed: true,
				apiID: 'newsletter',
				category: 'common',
				info: {
					displayName: 'Newsletter',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
				},
			},
			{
				uid: 'common.navigation-wrap',
				isDisplayed: true,
				apiID: 'navigation-wrap',
				category: 'common',
				info: {
					displayName: 'Navigation Wrap',
					icon: 'bulletList',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					List: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::category.category',
						targetModel: 'api::category.category',
						relationType: 'oneToMany',
					},
				},
			},
			{
				uid: 'common.media',
				isDisplayed: true,
				apiID: 'media',
				category: 'common',
				info: {
					displayName: 'Media',
					icon: 'picture',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Media: {
						type: 'component',
						repeatable: true,
						component: 'common.media-elm',
						required: true,
					},
					IsFixedHeight: {
						type: 'boolean',
						default: false,
						required: true,
					},
				},
			},
			{
				uid: 'common.media-elm',
				isDisplayed: true,
				apiID: 'media-elm',
				category: 'common',
				info: {
					displayName: 'Media Elm',
					icon: 'picture',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					Media: {
						type: 'media',
						multiple: false,
						required: true,
						allowedTypes: ['images', 'videos'],
					},
					IsDownloadable: {
						type: 'boolean',
						default: false,
						required: true,
					},
					IsFullScale: {
						type: 'boolean',
						default: false,
						required: true,
					},
					DefaultBackground: {
						type: 'string',
						default: '#',
					},
				},
			},
			{
				uid: 'common.link-elm',
				isDisplayed: true,
				apiID: 'link-elm',
				category: 'common',
				info: {
					displayName: 'Link Elm',
					icon: 'cursor',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Label: {
						type: 'string',
						required: true,
					},
					Link: {
						type: 'string',
						required: true,
						default: '#',
					},
				},
			},
			{
				uid: 'common.header',
				isDisplayed: true,
				apiID: 'header',
				category: 'common',
				info: {
					displayName: 'Header',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					cid: {
						type: 'string',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					Subhead: {
						type: 'string',
					},
					ActionButton: {
						type: 'component',
						repeatable: false,
						component: 'common.button-elm',
						required: false,
					},
				},
			},
			{
				uid: 'common.guideline-link',
				isDisplayed: true,
				apiID: 'guideline-link',
				category: 'common',
				info: {
					displayName: 'Guideline Link',
					icon: 'cursor',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Variant: {
						type: 'enumeration',
						enum: ['fill', 'right'],
					},
					Links: {
						displayName: 'Link Elm',
						type: 'component',
						repeatable: true,
						component: 'common.link-elm',
						required: true,
					},
				},
			},
			{
				uid: 'common.embed-block',
				isDisplayed: true,
				apiID: 'embed-block',
				category: 'common',
				info: {
					displayName: 'Embed Block',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					html: {
						type: 'string',
						required: true,
					},
				},
			},
			{
				uid: 'common.divider',
				isDisplayed: true,
				apiID: 'divider',
				category: 'common',
				info: {
					displayName: 'Divider',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Variant: {
						type: 'enumeration',
						enum: ['blank', 'line'],
					},
				},
			},
			{
				uid: 'common.container-elm',
				isDisplayed: true,
				apiID: 'container-elm',
				category: 'common',
				info: {
					displayName: 'Container Elm',
					icon: 'archive',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					ColorVariant: {
						type: 'enumeration',
						enum: ['green', 'gray', 'yellow', 'red'],
						required: true,
					},
					Icon: {
						type: 'enumeration',
						enum: ['cross', 'tick', 'warning', 'danger'],
					},
					Headline: {
						type: 'string',
						required: false,
					},
					Paragraph: {
						type: 'text',
						required: true,
					},
				},
			},
			{
				uid: 'common.contact-form',
				isDisplayed: true,
				apiID: 'contact-form',
				category: 'common',
				info: {
					displayName: 'Contact Form',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
				},
			},
			{
				uid: 'common.color',
				isDisplayed: true,
				apiID: 'color',
				category: 'common',
				info: {
					displayName: 'Color',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Colors: {
						type: 'component',
						repeatable: true,
						component: 'common.color-elm',
					},
				},
			},
			{
				uid: 'common.color-elm',
				isDisplayed: true,
				apiID: 'color-elm',
				category: 'common',
				info: {
					displayName: 'Color Elm',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					ColorName: {
						type: 'string',
						required: true,
					},
					HexColor: {
						type: 'string',
						regex: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
						customField: 'plugin::color-picker.color',
						required: true,
					},
					AdditionalField: {
						type: 'component',
						repeatable: true,
						component: 'common.additional-field-elm',
					},
				},
			},
			{
				uid: 'common.button-elm',
				isDisplayed: true,
				apiID: 'button-elm',
				category: 'common',
				info: {
					displayName: 'Button Elm',
					icon: 'cursor',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					ButtonText: {
						type: 'string',
						required: true,
					},
					ButtonLink: {
						type: 'string',
						required: true,
						default: '#',
					},
				},
			},
			{
				uid: 'common.block-horizon',
				isDisplayed: true,
				apiID: 'block-horizon',
				category: 'common',
				info: {
					displayName: 'FullW Wrap',
					icon: 'bulletList',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					isWrapContent: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Blocks: {
						type: 'component',
						repeatable: true,
						component: 'common.block-elm',
					},
				},
			},
			{
				uid: 'common.block-elm',
				isDisplayed: true,
				apiID: 'block-elm',
				category: 'common',
				info: {
					displayName: 'Block Elm',
					icon: 'bulletList',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Title: {
						type: 'string',
						required: true,
					},
					Content: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						required: false,
						customField: 'plugin::ckeditor.CKEditor',
					},
				},
			},
			{
				uid: 'common.block-content',
				isDisplayed: true,
				apiID: 'block-content',
				category: 'common',
				info: {
					displayName: 'Text and HalfW Wrap',
					icon: 'bulletList',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Headline: {
						type: 'string',
						required: false,
					},
					Variant: {
						type: 'enumeration',
						enum: ['vertical', 'grid'],
					},
					Blocks: {
						type: 'component',
						repeatable: true,
						component: 'common.block-elm',
						required: true,
					},
				},
			},
			{
				uid: 'common.block-container',
				isDisplayed: true,
				apiID: 'block-container',
				category: 'common',
				info: {
					displayName: 'Advanced Wrap',
					icon: 'apps',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Align: {
						type: 'enumeration',
						enum: ['Vertical', 'Horizontal'],
						required: true,
					},
					Size: {
						type: 'enumeration',
						enum: ['Full', 'Half'],
					},
					Blocks: {
						type: 'component',
						repeatable: true,
						component: 'common.container-elm',
						required: true,
					},
				},
			},
			{
				uid: 'common.additional-field-elm',
				isDisplayed: true,
				apiID: 'additional-field-elm',
				category: 'common',
				info: {
					displayName: 'Additional Field Elm',
					description: '',
				},
				options: {},
				attributes: {
					id: {
						type: 'integer',
					},
					FieldName: {
						type: 'enumeration',
						enum: ['CMYK', 'PMS'],
						required: true,
					},
					FieldValue: {
						type: 'string',
						required: true,
					},
				},
			},
		],
		contentTypes: [
			{
				uid: 'plugin::upload.file',
				isDisplayed: false,
				apiID: 'file',
				kind: 'collectionType',
				info: {
					singularName: 'file',
					pluralName: 'files',
					displayName: 'File',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						configurable: false,
						required: true,
					},
					alternativeText: {
						type: 'string',
						configurable: false,
					},
					caption: {
						type: 'string',
						configurable: false,
					},
					width: {
						type: 'integer',
						configurable: false,
					},
					height: {
						type: 'integer',
						configurable: false,
					},
					formats: {
						type: 'json',
						configurable: false,
					},
					hash: {
						type: 'string',
						configurable: false,
						required: true,
					},
					ext: {
						type: 'string',
						configurable: false,
					},
					mime: {
						type: 'string',
						configurable: false,
						required: true,
					},
					size: {
						type: 'decimal',
						configurable: false,
						required: true,
					},
					url: {
						type: 'string',
						configurable: false,
						required: true,
					},
					previewUrl: {
						type: 'string',
						configurable: false,
					},
					provider: {
						type: 'string',
						configurable: false,
						required: true,
					},
					provider_metadata: {
						type: 'json',
						configurable: false,
					},
					folder: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::upload.folder',
						inversedBy: 'files',
						private: true,
						targetModel: 'plugin::upload.folder',
						relationType: 'manyToOne',
					},
					folderPath: {
						type: 'string',
						minLength: 1,
						required: true,
						private: true,
						searchable: false,
					},
					blurhash: {
						type: 'string',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::upload.folder',
				isDisplayed: false,
				apiID: 'folder',
				kind: 'collectionType',
				info: {
					singularName: 'folder',
					pluralName: 'folders',
					displayName: 'Folder',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						minLength: 1,
						required: true,
					},
					pathId: {
						type: 'integer',
						unique: true,
						required: true,
					},
					parent: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::upload.folder',
						inversedBy: 'children',
						targetModel: 'plugin::upload.folder',
						relationType: 'manyToOne',
					},
					children: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::upload.folder',
						mappedBy: 'parent',
						targetModel: 'plugin::upload.folder',
						relationType: 'oneToMany',
					},
					files: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::upload.file',
						mappedBy: 'folder',
						targetModel: 'plugin::upload.file',
						relationType: 'oneToMany',
					},
					path: {
						type: 'string',
						minLength: 1,
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::i18n.locale',
				isDisplayed: false,
				apiID: 'locale',
				kind: 'collectionType',
				info: {
					singularName: 'locale',
					pluralName: 'locales',
					collectionName: 'locales',
					displayName: 'Locale',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						min: 1,
						max: 50,
						configurable: false,
					},
					code: {
						type: 'string',
						unique: true,
						configurable: false,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::content-releases.release',
				isDisplayed: false,
				apiID: 'release',
				kind: 'collectionType',
				info: {
					singularName: 'release',
					pluralName: 'releases',
					displayName: 'Release',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						required: true,
					},
					releasedAt: {
						type: 'datetime',
					},
					scheduledAt: {
						type: 'datetime',
					},
					timezone: {
						type: 'string',
					},
					status: {
						type: 'enumeration',
						enum: ['ready', 'blocked', 'failed', 'done', 'empty'],
						required: true,
					},
					actions: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::content-releases.release-action',
						mappedBy: 'release',
						targetModel: 'plugin::content-releases.release-action',
						relationType: 'oneToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::content-releases.release-action',
				isDisplayed: false,
				apiID: 'release-action',
				kind: 'collectionType',
				info: {
					singularName: 'release-action',
					pluralName: 'release-actions',
					displayName: 'Release Action',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					type: {
						type: 'enumeration',
						enum: ['publish', 'unpublish'],
						required: true,
					},
					contentType: {
						type: 'string',
						required: true,
					},
					entryDocumentId: {
						type: 'string',
					},
					release: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::content-releases.release',
						inversedBy: 'actions',
						targetModel: 'plugin::content-releases.release',
						relationType: 'manyToOne',
					},
					isEntryValid: {
						type: 'boolean',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::review-workflows.workflow',
				isDisplayed: false,
				apiID: 'workflow',
				kind: 'collectionType',
				info: {
					name: 'Workflow',
					description: '',
					singularName: 'workflow',
					pluralName: 'workflows',
					displayName: 'Workflow',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						required: true,
						unique: true,
					},
					stages: {
						type: 'relation',
						target: 'plugin::review-workflows.workflow-stage',
						relation: 'oneToMany',
						mappedBy: 'workflow',
						targetModel: 'plugin::review-workflows.workflow-stage',
						relationType: 'oneToMany',
					},
					stageRequiredToPublish: {
						type: 'relation',
						target: 'plugin::review-workflows.workflow-stage',
						relation: 'oneToOne',
						required: false,
						targetModel: 'plugin::review-workflows.workflow-stage',
						relationType: 'oneToOne',
					},
					contentTypes: {
						type: 'json',
						required: true,
						default: '[]',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::review-workflows.workflow-stage',
				isDisplayed: false,
				apiID: 'workflow-stage',
				kind: 'collectionType',
				info: {
					name: 'Workflow Stage',
					description: '',
					singularName: 'workflow-stage',
					pluralName: 'workflow-stages',
					displayName: 'Stages',
				},
				options: {
					version: '1.1.0',
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						configurable: false,
					},
					color: {
						type: 'string',
						configurable: false,
						default: '#4945FF',
					},
					workflow: {
						type: 'relation',
						target: 'plugin::review-workflows.workflow',
						relation: 'manyToOne',
						inversedBy: 'stages',
						configurable: false,
						targetModel: 'plugin::review-workflows.workflow',
						relationType: 'manyToOne',
					},
					permissions: {
						type: 'relation',
						target: 'admin::permission',
						relation: 'manyToMany',
						configurable: false,
						targetModel: 'admin::permission',
						relationType: 'manyToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::slugify.slug',
				isDisplayed: false,
				apiID: 'slug',
				kind: 'collectionType',
				info: {
					singularName: 'slug',
					pluralName: 'slugs',
					displayName: 'slug',
				},
				options: {
					draftAndPublish: false,
					comment: '',
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					slug: {
						type: 'text',
					},
					count: {
						type: 'integer',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::users-permissions.permission',
				isDisplayed: false,
				apiID: 'permission',
				kind: 'collectionType',
				info: {
					name: 'permission',
					description: '',
					singularName: 'permission',
					pluralName: 'permissions',
					displayName: 'Permission',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					action: {
						type: 'string',
						required: true,
						configurable: false,
					},
					role: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::users-permissions.role',
						inversedBy: 'permissions',
						configurable: false,
						targetModel: 'plugin::users-permissions.role',
						relationType: 'manyToOne',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::users-permissions.role',
				isDisplayed: false,
				apiID: 'role',
				kind: 'collectionType',
				info: {
					name: 'role',
					description: '',
					singularName: 'role',
					pluralName: 'roles',
					displayName: 'Role',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						minLength: 3,
						required: true,
						configurable: false,
					},
					description: {
						type: 'string',
						configurable: false,
					},
					type: {
						type: 'string',
						unique: true,
						configurable: false,
					},
					permissions: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::users-permissions.permission',
						mappedBy: 'role',
						configurable: false,
						targetModel: 'plugin::users-permissions.permission',
						relationType: 'oneToMany',
					},
					users: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::users-permissions.user',
						mappedBy: 'role',
						configurable: false,
						targetModel: 'plugin::users-permissions.user',
						relationType: 'oneToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::users-permissions.user',
				isDisplayed: true,
				apiID: 'user',
				kind: 'collectionType',
				info: {
					name: 'user',
					description: '',
					singularName: 'user',
					pluralName: 'users',
					displayName: 'C. User',
				},
				options: {
					draftAndPublish: false,
				},
				attributes: {
					id: {
						type: 'integer',
					},
					username: {
						type: 'string',
						minLength: 3,
						unique: true,
						configurable: false,
						required: true,
					},
					email: {
						type: 'email',
						minLength: 6,
						configurable: false,
						required: true,
					},
					provider: {
						type: 'string',
						configurable: false,
					},
					password: {
						type: 'password',
						minLength: 6,
						configurable: false,
						private: true,
						searchable: false,
					},
					resetPasswordToken: {
						type: 'string',
						configurable: false,
						private: true,
						searchable: false,
					},
					confirmationToken: {
						type: 'string',
						configurable: false,
						private: true,
						searchable: false,
					},
					confirmed: {
						type: 'boolean',
						default: false,
						configurable: false,
					},
					blocked: {
						type: 'boolean',
						default: false,
						configurable: false,
					},
					role: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::users-permissions.role',
						inversedBy: 'users',
						configurable: false,
						targetModel: 'plugin::users-permissions.role',
						relationType: 'manyToOne',
					},
					fullName: {
						type: 'string',
						required: true,
					},
					avatar: {
						type: 'media',
						multiple: false,
						required: false,
						allowedTypes: ['images', 'files'],
					},
					chats: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::chat.chat',
						mappedBy: 'user',
						targetModel: 'api::chat.chat',
						relationType: 'oneToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::navigation.audience',
				isDisplayed: true,
				apiID: 'audience',
				kind: 'collectionType',
				info: {
					singularName: 'audience',
					pluralName: 'audiences',
					displayName: 'b. Audience',
					name: 'audience',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						required: true,
					},
					key: {
						type: 'uid',
						targetField: 'name',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::navigation.navigation',
				isDisplayed: false,
				apiID: 'navigation',
				kind: 'collectionType',
				info: {
					singularName: 'navigation',
					pluralName: 'navigations',
					displayName: 'Navigation',
					name: 'navigation',
				},
				options: {
					comment: '',
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
					i18n: {
						localized: true,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'text',
						configurable: false,
						required: true,
					},
					slug: {
						type: 'uid',
						target: 'name',
						configurable: false,
						required: true,
					},
					visible: {
						type: 'boolean',
						default: false,
						configurable: false,
					},
					items: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::navigation.navigation-item',
						configurable: false,
						mappedBy: 'master',
						targetModel: 'plugin::navigation.navigation-item',
						relationType: 'oneToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'plugin::navigation.navigation-item',
				isDisplayed: false,
				apiID: 'navigation-item',
				kind: 'collectionType',
				info: {
					singularName: 'navigation-item',
					pluralName: 'navigation-items',
					displayName: 'Navigation Item',
					name: 'navigation-item',
				},
				options: {
					increments: true,
					timestamps: true,
					comment: 'Navigation Item',
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
					i18n: {
						localized: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					title: {
						type: 'text',
						configurable: false,
						required: true,
						pluginOptions: {
							i18n: {
								localized: false,
							},
						},
					},
					type: {
						type: 'enumeration',
						enum: ['INTERNAL', 'EXTERNAL', 'WRAPPER'],
						default: 'INTERNAL',
						configurable: false,
					},
					path: {
						type: 'text',
						targetField: 'title',
						configurable: false,
					},
					externalPath: {
						type: 'text',
						configurable: false,
					},
					uiRouterKey: {
						type: 'string',
						configurable: false,
					},
					menuAttached: {
						type: 'boolean',
						default: false,
						configurable: false,
					},
					order: {
						type: 'integer',
						default: 0,
						configurable: false,
					},
					collapsed: {
						type: 'boolean',
						default: false,
						configurable: false,
					},
					autoSync: {
						type: 'boolean',
						default: true,
						configurable: false,
					},
					parent: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'plugin::navigation.navigation-item',
						configurable: false,
						default: null,
						targetModel: 'plugin::navigation.navigation-item',
						relationType: 'oneToOne',
					},
					master: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::navigation.navigation',
						configurable: false,
						inversedBy: 'items',
						targetModel: 'plugin::navigation.navigation',
						relationType: 'manyToOne',
					},
					audience: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'plugin::navigation.audience',
						targetModel: 'plugin::navigation.audience',
						relationType: 'oneToMany',
					},
					additionalFields: {
						type: 'json',
						require: false,
						default: {},
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::category.category',
				isDisplayed: true,
				apiID: 'category',
				kind: 'collectionType',
				info: {
					singularName: 'category',
					pluralName: 'categories',
					displayName: '1. Category',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					order: {
						type: 'integer',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					components: {
						type: 'dynamiczone',
						components: [
							'common.text-horizon',
							'common.header',
							'common.divider',
							'homepage.search-bar',
							'common.navigation-wrap',
							'homepage.hero-scene',
							'common.color',
							'common.media',
							'common.guideline-link',
							'common.block-horizon',
							'common.block-content',
							'common.block-container',
						],
						required: true,
					},
					Pages: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::page.page',
						mappedBy: 'Category',
						targetModel: 'api::page.page',
						relationType: 'oneToMany',
					},
					slug: {
						type: 'string',
						required: true,
					},
					isLocked: {
						type: 'boolean',
						default: false,
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::chat.chat',
				isDisplayed: true,
				apiID: 'chat',
				kind: 'collectionType',
				info: {
					singularName: 'chat',
					pluralName: 'chats',
					displayName: '2. Chat',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					Message: {
						type: 'json',
					},
					uuid: {
						type: 'uid',
						customField: 'plugin::strapi-advanced-uuid.uuid',
					},
					user: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'plugin::users-permissions.user',
						inversedBy: 'chats',
						targetModel: 'plugin::users-permissions.user',
						relationType: 'manyToOne',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::contact-form.contact-form',
				isDisplayed: true,
				apiID: 'contact-form',
				kind: 'collectionType',
				info: {
					singularName: 'contact-form',
					pluralName: 'contact-forms',
					displayName: 'z1. Contact Form Data',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						required: true,
					},
					email: {
						type: 'email',
						required: true,
					},
					message: {
						type: 'string',
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::general-setting.general-setting',
				isDisplayed: true,
				apiID: 'general-setting',
				kind: 'singleType',
				info: {
					singularName: 'general-setting',
					pluralName: 'general-settings',
					displayName: 'a. General Setting',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					contactForm: {
						type: 'component',
						repeatable: false,
						component: 'general.contact-form',
					},
					newsletter: {
						type: 'component',
						repeatable: false,
						component: 'general.newsletter',
					},
					footer: {
						type: 'component',
						repeatable: false,
						component: 'general.footer',
					},
					social: {
						type: 'component',
						repeatable: false,
						component: 'general.social',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::home-page.home-page',
				isDisplayed: true,
				apiID: 'home-page',
				kind: 'singleType',
				info: {
					singularName: 'home-page',
					pluralName: 'home-pages',
					displayName: '0. Home Page',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					components: {
						type: 'dynamiczone',
						components: [
							'homepage.hero-scene',
							'common.header',
							'common.text-horizon',
							'homepage.search-bar',
							'common.navigation-wrap',
							'common.media',
							'common.guideline-link',
							'common.block-horizon',
							'common.block-content',
							'common.block-container',
						],
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::newsletter.newsletter',
				isDisplayed: true,
				apiID: 'newsletter',
				kind: 'collectionType',
				info: {
					singularName: 'newsletter',
					pluralName: 'newsletters',
					displayName: 'z2. Newsletter Data',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					email: {
						type: 'email',
						unique: true,
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::page.page',
				isDisplayed: true,
				apiID: 'page',
				kind: 'collectionType',
				info: {
					singularName: 'page',
					pluralName: 'pages',
					displayName: '0. Pages',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					order: {
						type: 'integer',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					components: {
						type: 'dynamiczone',
						components: [
							'common.text-horizon',
							'common.header',
							'common.divider',
							'homepage.search-bar',
							'common.navigation-wrap',
							'homepage.hero-scene',
							'common.color',
							'common.media',
							'common.guideline-link',
							'common.block-horizon',
							'common.block-content',
							'common.block-container',
						],
						required: true,
					},
					Category: {
						type: 'relation',
						relation: 'manyToOne',
						target: 'api::category.category',
						inversedBy: 'Pages',
						targetModel: 'api::category.category',
						relationType: 'manyToOne',
					},
					slug: {
						type: 'string',
						required: true,
					},
					isLocked: {
						type: 'boolean',
						default: false,
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::privacy-page.privacy-page',
				isDisplayed: true,
				apiID: 'privacy-page',
				kind: 'singleType',
				info: {
					singularName: 'privacy-page',
					pluralName: 'privacy-pages',
					displayName: 'b. Privacy Page',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					headline: {
						type: 'string',
						required: true,
					},
					paragraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					seo: {
						type: 'component',
						repeatable: false,
						component: 'shared.seo',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::term-page.term-page',
				isDisplayed: true,
				apiID: 'term-page',
				kind: 'singleType',
				info: {
					singularName: 'term-page',
					pluralName: 'term-pages',
					displayName: 'c. Term Page',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					headline: {
						type: 'string',
						required: true,
					},
					paragraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					seo: {
						type: 'component',
						repeatable: false,
						component: 'shared.seo',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::test.test',
				isDisplayed: true,
				apiID: 'test',
				kind: 'collectionType',
				info: {
					singularName: 'test',
					pluralName: 'tests',
					displayName: '99.1 Test',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				attributes: {
					id: {
						type: 'integer',
					},
					order: {
						type: 'integer',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					components: {
						type: 'dynamiczone',
						components: [
							'common.text-horizon',
							'common.header',
							'common.divider',
							'homepage.search-bar',
							'common.navigation-wrap',
							'homepage.hero-scene',
							'common.color',
							'common.media',
						],
						required: true,
					},
					slug: {
						type: 'string',
						required: true,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::test-page.test-page',
				isDisplayed: true,
				apiID: 'test-page',
				kind: 'singleType',
				info: {
					singularName: 'test-page',
					pluralName: 'test-pages',
					displayName: '99. Test Page',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				attributes: {
					id: {
						type: 'integer',
					},
					components: {
						type: 'dynamiczone',
						components: [
							'homepage.hero-scene',
							'common.header',
							'common.text-horizon',
							'homepage.search-bar',
							'common.navigation-wrap',
							'test.test-component',
						],
						required: true,
					},
					TextShort: {
						type: 'string',
						minLength: 1,
						maxLength: 255,
						unique: true,
					},
					TextLong: {
						type: 'text',
						minLength: 1,
						maxLength: 100000,
						unique: true,
					},
					Boolean: {
						type: 'boolean',
					},
					RichTextJson: {
						type: 'blocks',
					},
					JSON: {
						type: 'json',
					},
					Integer: {
						type: 'integer',
					},
					BigInteger: {
						type: 'biginteger',
					},
					Decimal: {
						type: 'decimal',
					},
					Float: {
						type: 'float',
					},
					Email: {
						type: 'email',
					},
					Date: {
						type: 'date',
					},
					DateTime: {
						type: 'datetime',
					},
					TIme: {
						type: 'time',
					},
					Password: {
						type: 'password',
					},
					MediaMultiple: {
						type: 'media',
						multiple: true,
						required: false,
						allowedTypes: ['images', 'files', 'videos', 'audios'],
					},
					MediaSingle: {
						type: 'media',
						multiple: false,
						required: false,
						allowedTypes: ['images', 'files', 'videos', 'audios'],
					},
					Enum: {
						type: 'enumeration',
						enum: ['Value1', 'Value2', 'Value3'],
					},
					RelationSingle: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'api::test.test',
						targetModel: 'api::test.test',
						relationType: 'oneToOne',
					},
					RelationMany: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::test.test',
						targetModel: 'api::test.test',
						relationType: 'oneToMany',
					},
					UID: {
						type: 'uid',
						targetField: 'TextShort',
					},
					RichTextMarkdown: {
						type: 'richtext',
					},
					Level1Elm: {
						type: 'component',
						repeatable: true,
						component: 'test.level1',
					},
					CKEditorSimple: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					CKEditorSimpleParagraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					CKEditorLight: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'light',
						},
						customField: 'plugin::ckeditor.CKEditor',
					},
					Color: {
						type: 'string',
						regex: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
						customField: 'plugin::color-picker.color',
					},
					MultiSelec: {
						type: 'json',
						default: '[]',
						options: ['Multi1', 'Multi2', 'Multi3'],
						required: false,
						min: 0,
						max: 100,
						customField: 'plugin::multi-select.multi-select',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'api::testimonial.testimonial',
				isDisplayed: true,
				apiID: 'testimonial',
				kind: 'collectionType',
				info: {
					singularName: 'testimonial',
					pluralName: 'testimonials',
					displayName: 'a. Testimonial',
					description: '',
				},
				options: {
					draftAndPublish: true,
				},
				pluginOptions: {},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						required: true,
					},
					position: {
						type: 'string',
						required: true,
					},
					testimonial: {
						type: 'richtext',
						options: {
							output: 'Markdown',
							preset: 'light',
						},
						customField: 'plugin::ckeditor.CKEditor',
						required: true,
					},
					media: {
						type: 'media',
						multiple: false,
						required: true,
						allowedTypes: ['images', 'videos'],
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::permission',
				isDisplayed: false,
				apiID: 'permission',
				kind: 'collectionType',
				info: {
					name: 'Permission',
					description: '',
					singularName: 'permission',
					pluralName: 'permissions',
					displayName: 'Permission',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					action: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
					},
					actionParameters: {
						type: 'json',
						configurable: false,
						required: false,
						default: {},
					},
					subject: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: false,
					},
					properties: {
						type: 'json',
						configurable: false,
						required: false,
						default: {},
					},
					conditions: {
						type: 'json',
						configurable: false,
						required: false,
						default: [],
					},
					role: {
						configurable: false,
						type: 'relation',
						relation: 'manyToOne',
						inversedBy: 'permissions',
						target: 'admin::role',
						targetModel: 'admin::role',
						relationType: 'manyToOne',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::user',
				isDisplayed: false,
				apiID: 'user',
				kind: 'collectionType',
				info: {
					name: 'User',
					description: '',
					singularName: 'user',
					pluralName: 'users',
					displayName: 'User',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					firstname: {
						type: 'string',
						unique: false,
						minLength: 1,
						configurable: false,
						required: false,
					},
					lastname: {
						type: 'string',
						unique: false,
						minLength: 1,
						configurable: false,
						required: false,
					},
					username: {
						type: 'string',
						unique: false,
						configurable: false,
						required: false,
					},
					email: {
						type: 'email',
						minLength: 6,
						configurable: false,
						required: true,
						unique: true,
						private: true,
					},
					password: {
						type: 'password',
						minLength: 6,
						configurable: false,
						required: false,
						private: true,
						searchable: false,
					},
					resetPasswordToken: {
						type: 'string',
						configurable: false,
						private: true,
						searchable: false,
					},
					registrationToken: {
						type: 'string',
						configurable: false,
						private: true,
						searchable: false,
					},
					isActive: {
						type: 'boolean',
						default: false,
						configurable: false,
						private: true,
					},
					roles: {
						configurable: false,
						private: true,
						type: 'relation',
						relation: 'manyToMany',
						inversedBy: 'users',
						target: 'admin::role',
						collectionName: 'strapi_users_roles',
						targetModel: 'admin::role',
						relationType: 'manyToMany',
					},
					blocked: {
						type: 'boolean',
						default: false,
						configurable: false,
						private: true,
					},
					preferedLanguage: {
						type: 'string',
						configurable: false,
						required: false,
						searchable: false,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::role',
				isDisplayed: false,
				apiID: 'role',
				kind: 'collectionType',
				info: {
					name: 'Role',
					description: '',
					singularName: 'role',
					pluralName: 'roles',
					displayName: 'Role',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						minLength: 1,
						unique: true,
						configurable: false,
						required: true,
					},
					code: {
						type: 'string',
						minLength: 1,
						unique: true,
						configurable: false,
						required: true,
					},
					description: {
						type: 'string',
						configurable: false,
					},
					users: {
						configurable: false,
						type: 'relation',
						relation: 'manyToMany',
						mappedBy: 'roles',
						target: 'admin::user',
						targetModel: 'admin::user',
						relationType: 'manyToMany',
					},
					permissions: {
						configurable: false,
						type: 'relation',
						relation: 'oneToMany',
						mappedBy: 'role',
						target: 'admin::permission',
						targetModel: 'admin::permission',
						relationType: 'oneToMany',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::api-token',
				isDisplayed: false,
				apiID: 'api-token',
				kind: 'collectionType',
				info: {
					name: 'Api Token',
					singularName: 'api-token',
					pluralName: 'api-tokens',
					displayName: 'Api Token',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
						unique: true,
					},
					description: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: false,
						default: '',
					},
					type: {
						type: 'enumeration',
						enum: ['read-only', 'full-access', 'custom'],
						configurable: false,
						required: true,
						default: 'read-only',
					},
					accessKey: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
						searchable: false,
					},
					lastUsedAt: {
						type: 'datetime',
						configurable: false,
						required: false,
					},
					permissions: {
						type: 'relation',
						target: 'admin::api-token-permission',
						relation: 'oneToMany',
						mappedBy: 'token',
						configurable: false,
						required: false,
						targetModel: 'admin::api-token-permission',
						relationType: 'oneToMany',
					},
					expiresAt: {
						type: 'datetime',
						configurable: false,
						required: false,
					},
					lifespan: {
						type: 'biginteger',
						configurable: false,
						required: false,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::api-token-permission',
				isDisplayed: false,
				apiID: 'api-token-permission',
				kind: 'collectionType',
				info: {
					name: 'API Token Permission',
					description: '',
					singularName: 'api-token-permission',
					pluralName: 'api-token-permissions',
					displayName: 'API Token Permission',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					action: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
					},
					token: {
						configurable: false,
						type: 'relation',
						relation: 'manyToOne',
						inversedBy: 'permissions',
						target: 'admin::api-token',
						targetModel: 'admin::api-token',
						relationType: 'manyToOne',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::transfer-token',
				isDisplayed: false,
				apiID: 'transfer-token',
				kind: 'collectionType',
				info: {
					name: 'Transfer Token',
					singularName: 'transfer-token',
					pluralName: 'transfer-tokens',
					displayName: 'Transfer Token',
					description: '',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					name: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
						unique: true,
					},
					description: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: false,
						default: '',
					},
					accessKey: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
					},
					lastUsedAt: {
						type: 'datetime',
						configurable: false,
						required: false,
					},
					permissions: {
						type: 'relation',
						target: 'admin::transfer-token-permission',
						relation: 'oneToMany',
						mappedBy: 'token',
						configurable: false,
						required: false,
						targetModel: 'admin::transfer-token-permission',
						relationType: 'oneToMany',
					},
					expiresAt: {
						type: 'datetime',
						configurable: false,
						required: false,
					},
					lifespan: {
						type: 'biginteger',
						configurable: false,
						required: false,
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::transfer-token-permission',
				isDisplayed: false,
				apiID: 'transfer-token-permission',
				kind: 'collectionType',
				info: {
					name: 'Transfer Token Permission',
					description: '',
					singularName: 'transfer-token-permission',
					pluralName: 'transfer-token-permissions',
					displayName: 'Transfer Token Permission',
				},
				options: {
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					action: {
						type: 'string',
						minLength: 1,
						configurable: false,
						required: true,
					},
					token: {
						configurable: false,
						type: 'relation',
						relation: 'manyToOne',
						inversedBy: 'permissions',
						target: 'admin::transfer-token',
						targetModel: 'admin::transfer-token',
						relationType: 'manyToOne',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
			{
				uid: 'admin::audit-log',
				isDisplayed: false,
				apiID: 'audit-log',
				kind: 'collectionType',
				info: {
					singularName: 'audit-log',
					pluralName: 'audit-logs',
					displayName: 'Audit Log',
				},
				options: {
					timestamps: false,
					draftAndPublish: false,
				},
				pluginOptions: {
					'content-manager': {
						visible: false,
					},
					'content-type-builder': {
						visible: false,
					},
				},
				attributes: {
					id: {
						type: 'integer',
					},
					action: {
						type: 'string',
						required: true,
					},
					date: {
						type: 'datetime',
						required: true,
					},
					user: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					payload: {
						type: 'json',
					},
					createdAt: {
						type: 'datetime',
					},
					updatedAt: {
						type: 'datetime',
					},
					createdBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
					updatedBy: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'admin::user',
						configurable: false,
						writable: false,
						visible: false,
						useJoinTable: false,
						private: true,
						targetModel: 'admin::user',
						relationType: 'oneToOne',
					},
				},
			},
		],
	},
}

export const configurationApiData: { data: IConfigurationProps } = {
	data: {
		contentType: {
			uid: 'api::page.page',
			settings: {
				bulkable: true,
				filterable: true,
				searchable: true,
				pageSize: 10,
				mainField: 'Headline',
				defaultSortBy: 'order',
				defaultSortOrder: 'ASC',
			},
			metadatas: {
				id: {
					edit: {},
					list: {
						label: 'id',
						searchable: true,
						sortable: true,
					},
				},
				order: {
					edit: {
						label: 'order',
						description: '',
						placeholder: '',
						visible: true,
						editable: true,
					},
					list: {
						label: 'order',
						searchable: true,
						sortable: true,
					},
				},
				Headline: {
					edit: {
						label: 'Headline',
						description: '',
						placeholder: '',
						visible: true,
						editable: true,
					},
					list: {
						label: 'Headline',
						searchable: true,
						sortable: true,
					},
				},
				components: {
					edit: {
						label: 'components',
						description: '',
						placeholder: '',
						visible: true,
						editable: true,
					},
					list: {
						label: 'components',
						searchable: false,
						sortable: false,
					},
				},
				Category: {
					edit: {
						label: 'Category',
						description: '',
						placeholder: '',
						visible: true,
						editable: true,
						mainField: 'Headline',
					},
					list: {
						label: 'Category',
						searchable: true,
						sortable: true,
						mainField: 'Headline',
					},
				},
				slug: {
					edit: {
						label: 'slug',
						description: '',
						placeholder: '',
						visible: true,
						editable: true,
					},
					list: {
						label: 'slug',
						searchable: true,
						sortable: true,
					},
				},
				createdAt: {
					edit: {
						label: 'createdAt',
						description: '',
						placeholder: '',
						visible: false,
						editable: true,
					},
					list: {
						label: 'createdAt',
						searchable: true,
						sortable: true,
					},
				},
				updatedAt: {
					edit: {
						label: 'updatedAt',
						description: '',
						placeholder: '',
						visible: false,
						editable: true,
					},
					list: {
						label: 'updatedAt',
						searchable: true,
						sortable: true,
					},
				},
				createdBy: {
					edit: {
						label: 'createdBy',
						description: '',
						placeholder: '',
						visible: false,
						editable: true,
						mainField: 'firstname',
					},
					list: {
						label: 'createdBy',
						searchable: true,
						sortable: true,
						mainField: 'firstname',
					},
				},
				updatedBy: {
					edit: {
						label: 'updatedBy',
						description: '',
						placeholder: '',
						visible: false,
						editable: true,
						mainField: 'firstname',
					},
					list: {
						label: 'updatedBy',
						searchable: true,
						sortable: true,
						mainField: 'firstname',
					},
				},
			},
			layouts: {
				edit: [
					[
						{
							name: 'Headline',
							size: 6,
						},
					],
					[
						{
							name: 'components',
							size: 12,
						},
					],
					[
						{
							name: 'Category',
							size: 6,
						},
						{
							name: 'slug',
							size: 6,
						},
					],
					[
						{
							name: 'order',
							size: 4,
						},
					],
				],
				list: ['id', 'Headline', 'Category', 'order', 'slug'],
			},
		},
		components: {
			'common.text-horizon': {
				uid: 'common.text-horizon',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'Headline',
					defaultSortBy: 'Headline',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					Headline: {
						edit: {
							label: 'Headline',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Headline',
							searchable: true,
							sortable: true,
						},
					},
					Paragraph: {
						edit: {
							label: 'Paragraph',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Paragraph',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'Headline'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
						],
						[
							{
								name: 'Headline',
								size: 12,
							},
						],
						[
							{
								name: 'Paragraph',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.header': {
				uid: 'common.header',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'cid',
					defaultSortBy: 'cid',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					cid: {
						edit: {
							label: 'cid',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'cid',
							searchable: true,
							sortable: true,
						},
					},
					Headline: {
						edit: {
							label: 'Headline',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Headline',
							searchable: true,
							sortable: true,
						},
					},
					Subhead: {
						edit: {
							label: 'Subhead',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Subhead',
							searchable: true,
							sortable: true,
						},
					},
					ActionButton: {
						edit: {
							label: 'ActionButton',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'ActionButton',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'cid', 'Headline'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
							{
								name: 'cid',
								size: 6,
							},
						],
						[
							{
								name: 'Headline',
								size: 6,
							},
							{
								name: 'Subhead',
								size: 6,
							},
						],
						[
							{
								name: 'ActionButton',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.button-elm': {
				uid: 'common.button-elm',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'ButtonText',
					defaultSortBy: 'ButtonText',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					ButtonText: {
						edit: {
							label: 'ButtonText',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'ButtonText',
							searchable: true,
							sortable: true,
						},
					},
					ButtonLink: {
						edit: {
							label: 'ButtonLink',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'ButtonLink',
							searchable: true,
							sortable: true,
						},
					},
				},
				layouts: {
					list: ['id', 'ButtonText', 'ButtonLink'],
					edit: [
						[
							{
								name: 'ButtonText',
								size: 6,
							},
							{
								name: 'ButtonLink',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.divider': {
				uid: 'common.divider',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'id',
					defaultSortBy: 'id',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					Variant: {
						edit: {
							label: 'Variant',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Variant',
							searchable: true,
							sortable: true,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'Variant'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
							{
								name: 'Variant',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'homepage.search-bar': {
				uid: 'homepage.search-bar',
				category: 'homepage',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'Headline',
					defaultSortBy: 'id',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					Headline: {
						edit: {
							label: 'Headline',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Headline',
							searchable: true,
							sortable: true,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'Headline'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
							{
								name: 'Headline',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'homepage.nagivation-wrap': {
				uid: 'homepage.nagivation-wrap',
				category: 'homepage',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'id',
					defaultSortBy: 'id',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					List: {
						edit: {
							label: 'List',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
							mainField: 'Headline',
						},
						list: {
							label: 'List',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'List'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
							{
								name: 'List',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'homepage.hero-scene': {
				uid: 'homepage.hero-scene',
				category: 'homepage',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'cid',
					defaultSortBy: 'cid',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					cid: {
						edit: {
							label: 'cid',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'cid',
							searchable: true,
							sortable: true,
						},
					},
					Media: {
						edit: {
							label: 'Media',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Media',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'cid', 'Media'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
							{
								name: 'cid',
								size: 6,
							},
						],
						[
							{
								name: 'Media',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.color': {
				uid: 'common.color',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'id',
					defaultSortBy: 'id',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					Colors: {
						edit: {
							label: 'Colors',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Colors',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'Colors'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
						],
						[
							{
								name: 'Colors',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.color-elm': {
				uid: 'common.color-elm',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'ColorName',
					defaultSortBy: 'ColorName',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					ColorName: {
						edit: {
							label: 'ColorName',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'ColorName',
							searchable: true,
							sortable: true,
						},
					},
					HexColor: {
						edit: {
							label: 'HexColor',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'HexColor',
							searchable: true,
							sortable: true,
						},
					},
					AdditionalField: {
						edit: {
							label: 'AdditionalField',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'AdditionalField',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'ColorName', 'HexColor', 'AdditionalField'],
					edit: [
						[
							{
								name: 'ColorName',
								size: 6,
							},
							{
								name: 'HexColor',
								size: 6,
							},
						],
						[
							{
								name: 'AdditionalField',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.additional-field-elm': {
				uid: 'common.additional-field-elm',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'FieldName',
					defaultSortBy: 'FieldValue',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					FieldName: {
						edit: {
							label: 'FieldName',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'FieldName',
							searchable: true,
							sortable: true,
						},
					},
					FieldValue: {
						edit: {
							label: 'FieldValue',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'FieldValue',
							searchable: true,
							sortable: true,
						},
					},
				},
				layouts: {
					list: ['id', 'FieldName', 'FieldValue'],
					edit: [
						[
							{
								name: 'FieldName',
								size: 6,
							},
							{
								name: 'FieldValue',
								size: 6,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.media': {
				uid: 'common.media',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'id',
					defaultSortBy: 'id',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					enable: {
						edit: {
							label: 'enable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'enable',
							searchable: true,
							sortable: true,
						},
					},
					Media: {
						edit: {
							label: 'Media',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Media',
							searchable: false,
							sortable: false,
						},
					},
				},
				layouts: {
					list: ['id', 'enable', 'Media'],
					edit: [
						[
							{
								name: 'enable',
								size: 4,
							},
						],
						[
							{
								name: 'Media',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
			'common.media-elm': {
				uid: 'common.media-elm',
				category: 'common',
				settings: {
					bulkable: true,
					filterable: true,
					searchable: true,
					pageSize: 10,
					mainField: 'Headline',
					defaultSortBy: 'Headline',
					defaultSortOrder: 'ASC',
				},
				metadatas: {
					id: {
						edit: {},
						list: {
							label: 'id',
							searchable: false,
							sortable: false,
						},
					},
					Headline: {
						edit: {
							label: 'Headline',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Headline',
							searchable: true,
							sortable: true,
						},
					},
					Media: {
						edit: {
							label: 'Media',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'Media',
							searchable: false,
							sortable: false,
						},
					},
					IsDownloadable: {
						edit: {
							label: 'IsDownloadable',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'IsDownloadable',
							searchable: true,
							sortable: true,
						},
					},
					IsFullScale: {
						edit: {
							label: 'IsFullScale',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'IsFullScale',
							searchable: true,
							sortable: true,
						},
					},
					DefaultBackground: {
						edit: {
							label: 'DefaultBackground',
							description: '',
							placeholder: '',
							visible: true,
							editable: true,
						},
						list: {
							label: 'DefaultBackground',
							searchable: true,
							sortable: true,
						},
					},
				},
				layouts: {
					list: ['id', 'Headline', 'Media', 'IsDownloadable'],
					edit: [
						[
							{
								name: 'Headline',
								size: 12,
							},
						],
						[
							{
								name: 'IsDownloadable',
								size: 4,
							},
							{
								name: 'IsFullScale',
								size: 4,
							},
							{
								name: 'DefaultBackground',
								size: 4,
							},
						],
						[
							{
								name: 'Media',
								size: 12,
							},
						],
					],
				},
				isComponent: true,
			},
		},
	},
}

export const pageDetailApiData: { data: IResultDataProps; meta: IResultMetaProps } = {
	data: {
		id: 56,
		Headline: 'CDA Trung Thu 2023',
		slug: 'mid-autumn-2023',
		createdAt: '2025-03-04T08:16:36.691Z',
		updatedAt: '2025-03-04T08:17:16.719Z',
		publishedAt: null,
		order: 0,
		documentId: 'aijxq2ojfp1p2pnjtc3k8j95',
		locale: null,
		components: [
			{
				__component: 'common.media',
				id: 52,
				enable: true,
				Media: [
					{
						id: 97,
						Headline: 'front',
						IsDownloadable: false,
						IsFullScale: false,
						DefaultBackground: '#',
						Media: {
							id: 49,
							name: 'Frame 195.png',
							alternativeText: null,
							caption: null,
							width: 1684,
							height: 2380,
							formats: {
								thumbnail: {
									ext: '.png',
									url: '/uploads/thumbnail_Frame_195_9f005eb20f.png',
									hash: 'thumbnail_Frame_195_9f005eb20f',
									mime: 'image/png',
									name: 'thumbnail_Frame 195.png',
									path: null,
									size: 12.28,
									width: 110,
									height: 156,
									sizeInBytes: 12277,
								},
							},
							hash: 'Frame_195_9f005eb20f',
							ext: '.png',
							mime: 'image/png',
							size: 49.77,
							url: '/uploads/Frame_195_9f005eb20f.png',
							previewUrl: null,
							provider: 'local',
							provider_metadata: null,
							folderPath: '/5/6',
							createdAt: '2025-03-04T08:15:45.875Z',
							updatedAt: '2025-03-04T08:15:45.875Z',
							blurhash: 'U;RLH;Thp0t6$|ofWZWFxuW?WAnhxtogofoJ',
							documentId: 'h974f4dfcs87qhdczgrhziup',
							locale: null,
							publishedAt: '2025-03-04T08:15:46.018Z',
							folder: {
								id: 6,
								name: 'CDA Trung Thu 2023',
								pathId: 6,
								path: '/5/6',
								createdAt: '2025-03-04T08:15:21.852Z',
								updatedAt: '2025-03-04T08:15:21.852Z',
								documentId: 'czdoynlha0uqu56gagcppp69',
								locale: null,
								publishedAt: '2025-03-04T08:15:21.852Z',
							},
						},
					},
					{
						id: 98,
						Headline: 'back',
						IsDownloadable: false,
						IsFullScale: true,
						DefaultBackground: '#',
						Media: {
							id: 50,
							name: 'Frame 202.png',
							alternativeText: null,
							caption: null,
							width: 1684,
							height: 2380,
							formats: {
								thumbnail: {
									ext: '.png',
									url: '/uploads/thumbnail_Frame_202_1fa20d4691.png',
									hash: 'thumbnail_Frame_202_1fa20d4691',
									mime: 'image/png',
									name: 'thumbnail_Frame 202.png',
									path: null,
									size: 8.68,
									width: 110,
									height: 156,
									sizeInBytes: 8684,
								},
							},
							hash: 'Frame_202_1fa20d4691',
							ext: '.png',
							mime: 'image/png',
							size: 44.66,
							url: '/uploads/Frame_202_1fa20d4691.png',
							previewUrl: null,
							provider: 'local',
							provider_metadata: null,
							folderPath: '/5/6',
							createdAt: '2025-03-04T08:15:46.064Z',
							updatedAt: '2025-03-04T08:15:46.064Z',
							blurhash: 'UES;{h?G*K^b?bj[nMj?*KnhG1WB^aoKRjjY',
							documentId: 'gsv05r466pde4hrqfc12hm0a',
							locale: null,
							publishedAt: '2025-03-04T08:15:46.228Z',
							folder: {
								id: 6,
								name: 'CDA Trung Thu 2023',
								pathId: 6,
								path: '/5/6',
								createdAt: '2025-03-04T08:15:21.852Z',
								updatedAt: '2025-03-04T08:15:21.852Z',
								documentId: 'czdoynlha0uqu56gagcppp69',
								locale: null,
								publishedAt: '2025-03-04T08:15:21.852Z',
							},
						},
					},
				],
			},
			{
				__component: 'common.media',
				id: 54,
				enable: true,
				Media: [
					{
						id: 101,
						Headline: '1',
						IsDownloadable: false,
						IsFullScale: true,
						DefaultBackground: '#',
						Media: {
							id: 53,
							name: 'A4 - 4.png',
							alternativeText: null,
							caption: null,
							width: 6736,
							height: 4760,
							formats: {
								thumbnail: {
									ext: '.png',
									url: '/uploads/thumbnail_A4_4_0ed4191692.png',
									hash: 'thumbnail_A4_4_0ed4191692',
									mime: 'image/png',
									name: 'thumbnail_A4 - 4.png',
									path: null,
									size: 32.69,
									width: 221,
									height: 156,
									sizeInBytes: 32688,
								},
							},
							hash: 'A4_4_0ed4191692',
							ext: '.png',
							mime: 'image/png',
							size: 306.26,
							url: '/uploads/A4_4_0ed4191692.png',
							previewUrl: null,
							provider: 'local',
							provider_metadata: null,
							folderPath: '/5/6',
							createdAt: '2025-03-04T08:15:48.376Z',
							updatedAt: '2025-03-04T08:15:48.376Z',
							blurhash: 'UMRV2R~Bm6^f={r?ni%KqGM_p1WA-hofRjRP',
							documentId: 'wdnbhk9n8d109w4ha376xudr',
							locale: null,
							publishedAt: '2025-03-04T08:15:48.572Z',
							folder: {
								id: 6,
								name: 'CDA Trung Thu 2023',
								pathId: 6,
								path: '/5/6',
								createdAt: '2025-03-04T08:15:21.852Z',
								updatedAt: '2025-03-04T08:15:21.852Z',
								documentId: 'czdoynlha0uqu56gagcppp69',
								locale: null,
								publishedAt: '2025-03-04T08:15:21.852Z',
							},
						},
					},
					{
						id: 102,
						Headline: '2',
						IsDownloadable: false,
						IsFullScale: true,
						DefaultBackground: '#',
						Media: {
							id: 52,
							name: 'Frame 198.png',
							alternativeText: null,
							caption: null,
							width: 3368,
							height: 2380,
							formats: {
								thumbnail: {
									ext: '.png',
									url: '/uploads/thumbnail_Frame_198_be69d4f6f9.png',
									hash: 'thumbnail_Frame_198_be69d4f6f9',
									mime: 'image/png',
									name: 'thumbnail_Frame 198.png',
									path: null,
									size: 38.84,
									width: 221,
									height: 156,
									sizeInBytes: 38840,
								},
							},
							hash: 'Frame_198_be69d4f6f9',
							ext: '.png',
							mime: 'image/png',
							size: 187.95,
							url: '/uploads/Frame_198_be69d4f6f9.png',
							previewUrl: null,
							provider: 'local',
							provider_metadata: null,
							folderPath: '/5/6',
							createdAt: '2025-03-04T08:15:47.492Z',
							updatedAt: '2025-03-04T08:15:47.492Z',
							blurhash: 'UNQ+~G%y9]-V]|IWSis+=~^ks;NG*0nhxUo~',
							documentId: 'vbgma1n91ryzef16bld5a9jl',
							locale: null,
							publishedAt: '2025-03-04T08:15:47.592Z',
							folder: {
								id: 6,
								name: 'CDA Trung Thu 2023',
								pathId: 6,
								path: '/5/6',
								createdAt: '2025-03-04T08:15:21.852Z',
								updatedAt: '2025-03-04T08:15:21.852Z',
								documentId: 'czdoynlha0uqu56gagcppp69',
								locale: null,
								publishedAt: '2025-03-04T08:15:21.852Z',
							},
						},
					},
					{
						id: 103,
						Headline: '3',
						IsDownloadable: false,
						IsFullScale: true,
						DefaultBackground: '#',
						Media: {
							id: 51,
							name: 'Frame 200.png',
							alternativeText: null,
							caption: null,
							width: 3368,
							height: 2380,
							formats: {
								thumbnail: {
									ext: '.png',
									url: '/uploads/thumbnail_Frame_200_0aa6efc22b.png',
									hash: 'thumbnail_Frame_200_0aa6efc22b',
									mime: 'image/png',
									name: 'thumbnail_Frame 200.png',
									path: null,
									size: 36.5,
									width: 221,
									height: 156,
									sizeInBytes: 36504,
								},
							},
							hash: 'Frame_200_0aa6efc22b',
							ext: '.png',
							mime: 'image/png',
							size: 155.56,
							url: '/uploads/Frame_200_0aa6efc22b.png',
							previewUrl: null,
							provider: 'local',
							provider_metadata: null,
							folderPath: '/5/6',
							createdAt: '2025-03-04T08:15:47.100Z',
							updatedAt: '2025-03-04T08:15:47.100Z',
							blurhash: 'UeQ[_b}YB}-.#bRjcGtRcqtQidWV%xa$i]X7',
							documentId: 'ad4hc7ekvf48d5aml9vda2r0',
							locale: null,
							publishedAt: '2025-03-04T08:15:47.297Z',
							folder: {
								id: 6,
								name: 'CDA Trung Thu 2023',
								pathId: 6,
								path: '/5/6',
								createdAt: '2025-03-04T08:15:21.852Z',
								updatedAt: '2025-03-04T08:15:21.852Z',
								documentId: 'czdoynlha0uqu56gagcppp69',
								locale: null,
								publishedAt: '2025-03-04T08:15:21.852Z',
							},
						},
					},
				],
			},
		],
		Category: {
			count: 1,
		},
		createdBy: {
			id: 3,
			firstname: 'Minh',
			lastname: 'Nguyen',
			username: 'minh.hq.nguyen',
		},
		updatedBy: {
			id: 3,
			firstname: 'Minh',
			lastname: 'Nguyen',
			username: 'minh.hq.nguyen',
		},
		localizations: [],
		strapi_stage: null,
		strapi_assignee: null,
		status: 'published',
	},
	meta: {
		availableLocales: [
			{
				id: 56,
				documentId: 'aijxq2ojfp1p2pnjtc3k8j95',
				locale: null,
				updatedAt: '2025-03-04T08:17:16.719Z',
				createdAt: '2025-03-04T08:16:36.691Z',
				publishedAt: null,
				Headline: 'CDA Trung Thu 2023',
				slug: 'mid-autumn-2023',
				components: [
					{
						__component: 'common.media',
						id: 52,
						enable: true,
						Media: [
							{
								id: 97,
								Headline: 'front',
								IsDownloadable: false,
								IsFullScale: false,
							},
							{
								id: 98,
								Headline: 'back',
								IsDownloadable: false,
								IsFullScale: true,
							},
						],
					},
					{
						__component: 'common.media',
						id: 54,
						enable: true,
						Media: [
							{
								id: 101,
								Headline: '1',
								IsDownloadable: false,
								IsFullScale: true,
							},
							{
								id: 102,
								Headline: '2',
								IsDownloadable: false,
								IsFullScale: true,
							},
							{
								id: 103,
								Headline: '3',
								IsDownloadable: false,
								IsFullScale: true,
							},
						],
					},
				],
				createdBy: {
					id: 3,
					firstname: 'Minh',
					lastname: 'Nguyen',
					username: 'minh.hq.nguyen',
				},
				updatedBy: {
					id: 3,
					firstname: 'Minh',
					lastname: 'Nguyen',
					username: 'minh.hq.nguyen',
				},
				status: 'published',
			},
		],
		availableStatus: [
			{
				id: 58,
				locale: null,
				updatedAt: '2025-03-04T08:17:16.719Z',
				createdAt: '2025-03-04T08:16:36.691Z',
				publishedAt: '2025-03-04T08:17:16.808Z',
				createdBy: {
					id: 3,
					firstname: 'Minh',
					lastname: 'Nguyen',
					username: 'minh.hq.nguyen',
				},
				updatedBy: {
					id: 3,
					firstname: 'Minh',
					lastname: 'Nguyen',
					username: 'minh.hq.nguyen',
				},
			},
		],
	},
}

export const listComponentsApiData: { data: IComponentProps[] } = {
	data: [
		{
			uid: 'shared.seo',
			category: 'shared',
			apiId: 'seo',
			schema: {
				displayName: 'seo',
				description: '',
				icon: 'search',
				collectionName: 'components_shared_seos',
				attributes: {
					metaTitle: {
						required: true,
						type: 'string',
						maxLength: 60,
					},
					metaDescription: {
						type: 'string',
						required: true,
						maxLength: 160,
						minLength: 50,
					},
					metaImage: {
						type: 'media',
						multiple: false,
						required: false,
						private: false,
						allowedTypes: ['images', 'files', 'videos'],
					},
					metaSocial: {
						type: 'component',
						repeatable: true,
						component: 'shared.meta-social',
					},
					keywords: {
						type: 'text',
						regex: '[^,]+',
					},
					metaRobots: {
						type: 'string',
						regex: '[^,]+',
					},
					structuredData: {
						type: 'json',
					},
					metaViewport: {
						type: 'string',
					},
					canonicalURL: {
						type: 'string',
					},
				},
			},
		},
		{
			uid: 'shared.meta-social',
			category: 'shared',
			apiId: 'meta-social',
			schema: {
				displayName: 'metaSocial',
				description: '',
				icon: 'project-diagram',
				collectionName: 'components_shared_meta_socials',
				attributes: {
					socialNetwork: {
						type: 'enumeration',
						enum: ['Facebook', 'Twitter'],
						required: true,
					},
					title: {
						type: 'string',
						required: true,
						maxLength: 60,
					},
					description: {
						type: 'string',
						maxLength: 65,
						required: true,
					},
					image: {
						type: 'media',
						multiple: false,
						required: false,
						private: false,
						allowedTypes: ['images', 'files', 'videos'],
					},
				},
			},
		},
		{
			uid: 'homepage.search-bar',
			category: 'homepage',
			apiId: 'search-bar',
			schema: {
				displayName: 'Search Bar',
				description: '',
				icon: 'search',
				collectionName: 'components_homepage_search_bars',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Headline: {
						type: 'string',
						required: true,
					},
				},
			},
		},
		{
			uid: 'homepage.nagivation-wrap',
			category: 'homepage',
			apiId: 'nagivation-wrap',
			schema: {
				displayName: 'Nagivation Wrap',
				description: '',
				icon: 'bulletList',
				collectionName: 'components_homepage_nagivation_wraps',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					List: {
						type: 'relation',
						relation: 'oneToMany',
						target: 'api::category.category',
						targetAttribute: null,
						private: false,
					},
				},
			},
		},
		{
			uid: 'homepage.list-elm',
			category: 'homepage',
			apiId: 'list-elm',
			schema: {
				displayName: 'List Elm',
				description: '',
				icon: 'bulletList',
				collectionName: 'components_homepage_list_elms',
				attributes: {
					label: {
						type: 'string',
						required: true,
					},
					path: {
						type: 'string',
						required: true,
						default: '#',
					},
				},
			},
		},
		{
			uid: 'homepage.hero-scene',
			category: 'homepage',
			apiId: 'hero-scene',
			schema: {
				displayName: 'Hero Scene',
				description: '',
				icon: 'house',
				collectionName: 'components_homepage_hero_scenes',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					cid: {
						type: 'string',
					},
					Media: {
						type: 'media',
						multiple: false,
						required: true,
						private: false,
						allowedTypes: ['images'],
					},
				},
			},
		},
		{
			uid: 'general.social',
			category: 'general',
			apiId: 'social',
			schema: {
				displayName: 'Social',
				description: '',
				collectionName: 'components_general_socials',
				attributes: {
					facebookLink: {
						type: 'string',
					},
					instagramLink: {
						type: 'string',
					},
					twitterLink: {
						type: 'string',
					},
					linkedInLink: {
						type: 'string',
					},
				},
			},
		},
		{
			uid: 'general.newsletter',
			category: 'general',
			apiId: 'newsletter',
			schema: {
				displayName: 'Newsletter',
				description: '',
				collectionName: 'components_general_newsletters',
				attributes: {
					headline: {
						type: 'string',
					},
				},
			},
		},
		{
			uid: 'general.footer',
			category: 'general',
			apiId: 'footer',
			schema: {
				displayName: 'Footer',
				description: '',
				collectionName: 'components_general_footers',
				attributes: {
					address: {
						type: 'string',
					},
					phone: {
						type: 'string',
					},
					email: {
						type: 'string',
					},
					copyright: {
						type: 'string',
					},
				},
			},
		},
		{
			uid: 'general.contact-form',
			category: 'general',
			apiId: 'contact-form',
			schema: {
				displayName: 'Contact Form',
				description: '',
				collectionName: 'components_general_contact_forms',
				attributes: {
					headline: {
						type: 'string',
					},
				},
			},
		},
		{
			uid: 'common.text-horizon',
			category: 'common',
			apiId: 'text-horizon',
			schema: {
				displayName: 'Text Horizon',
				description: '',
				icon: 'bold',
				collectionName: 'components_common_text_horizons',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					BlockQuote: {
						type: 'string',
					},
					Headline: {
						type: 'string',
					},
					Paragraph: {
						type: 'richtext',
						options: {
							output: 'HTML',
							preset: 'simple-paragraph',
						},
						required: false,
						customField: 'plugin::ckeditor.CKEditor',
					},
				},
			},
		},
		{
			uid: 'common.newsletter',
			category: 'common',
			apiId: 'newsletter',
			schema: {
				displayName: 'Newsletter',
				description: '',
				collectionName: 'components_common_newsletters',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
				},
			},
		},
		{
			uid: 'common.media',
			category: 'common',
			apiId: 'media',
			schema: {
				displayName: 'Media',
				description: '',
				icon: 'picture',
				collectionName: 'components_common_media',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Media: {
						type: 'component',
						repeatable: true,
						component: 'common.media-elm',
						required: true,
					},
				},
			},
		},
		{
			uid: 'common.media-elm',
			category: 'common',
			apiId: 'media-elm',
			schema: {
				displayName: 'Media Elm',
				description: '',
				icon: 'picture',
				collectionName: 'components_common_media_elms',
				attributes: {
					Headline: {
						type: 'string',
						required: true,
					},
					Media: {
						type: 'media',
						multiple: false,
						required: true,
						private: false,
						allowedTypes: ['images'],
					},
					IsDownloadable: {
						type: 'boolean',
						default: false,
						required: true,
					},
					IsFullScale: {
						type: 'boolean',
						default: false,
						required: true,
					},
					DefaultBackground: {
						type: 'string',
						default: '#',
					},
				},
			},
		},
		{
			uid: 'common.header',
			category: 'common',
			apiId: 'header',
			schema: {
				displayName: 'Header',
				description: '',
				collectionName: 'components_common_headers',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					cid: {
						type: 'string',
					},
					Headline: {
						type: 'string',
						required: true,
					},
					Subhead: {
						type: 'string',
					},
					ActionButton: {
						type: 'component',
						repeatable: false,
						component: 'common.button-elm',
						required: false,
					},
				},
			},
		},
		{
			uid: 'common.embed-block',
			category: 'common',
			apiId: 'embed-block',
			schema: {
				displayName: 'Embed Block',
				description: '',
				collectionName: 'components_common_embed_blocks',
				attributes: {
					html: {
						type: 'string',
						required: true,
					},
				},
			},
		},
		{
			uid: 'common.divider',
			category: 'common',
			apiId: 'divider',
			schema: {
				displayName: 'Divider',
				description: '',
				collectionName: 'components_common_dividers',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Variant: {
						type: 'enumeration',
						enum: ['blank', 'line'],
					},
				},
			},
		},
		{
			uid: 'common.contact-form',
			category: 'common',
			apiId: 'contact-form',
			schema: {
				displayName: 'Contact Form',
				description: '',
				collectionName: 'components_common_contact_forms',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
				},
			},
		},
		{
			uid: 'common.color',
			category: 'common',
			apiId: 'color',
			schema: {
				displayName: 'Color',
				description: '',
				collectionName: 'components_common_colors',
				attributes: {
					enable: {
						type: 'boolean',
						default: true,
						required: true,
					},
					Colors: {
						type: 'component',
						repeatable: true,
						component: 'common.color-elm',
					},
				},
			},
		},
		{
			uid: 'common.color-elm',
			category: 'common',
			apiId: 'color-elm',
			schema: {
				displayName: 'Color Elm',
				description: '',
				collectionName: 'components_common_color_elms',
				attributes: {
					ColorName: {
						type: 'string',
						required: true,
					},
					HexColor: {
						type: 'string',
						regex: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
						customField: 'plugin::color-picker.color',
						required: true,
					},
					AdditionalField: {
						type: 'component',
						repeatable: true,
						component: 'common.additional-field-elm',
					},
				},
			},
		},
		{
			uid: 'common.button-elm',
			category: 'common',
			apiId: 'button-elm',
			schema: {
				displayName: 'Button Elm',
				description: '',
				icon: 'cursor',
				collectionName: 'components_common_button_elms',
				attributes: {
					ButtonText: {
						type: 'string',
						required: true,
					},
					ButtonLink: {
						type: 'string',
						required: true,
						default: '#',
					},
				},
			},
		},
		{
			uid: 'common.additional-field-elm',
			category: 'common',
			apiId: 'additional-field-elm',
			schema: {
				displayName: 'Additional Field Elm',
				description: '',
				collectionName: 'components_common_additional_field_elms',
				attributes: {
					FieldName: {
						type: 'enumeration',
						enum: ['CMYK', 'PMS'],
						required: true,
					},
					FieldValue: {
						type: 'string',
						required: true,
					},
				},
			},
		},
	],
}

export const contentTypeDetailApiData: { data: IContentTypeProps } = {
	data: {
		uid: 'api::page.page',
		apiID: 'page',
		schema: {
			draftAndPublish: true,
			displayName: '0. Pages',
			singularName: 'page',
			pluralName: 'pages',
			description: '',
			pluginOptions: {
				paperTrail: {
					enabled: true,
				},
			},
			kind: 'collectionType',
			collectionName: 'pages',
			attributes: {
				order: {
					type: 'integer',
				},
				Headline: {
					type: 'string',
					required: true,
				},
				components: {
					type: 'dynamiczone',
					components: [
						'common.text-horizon',
						'common.header',
						'common.divider',
						'homepage.search-bar',
						'homepage.nagivation-wrap',
						'homepage.hero-scene',
						'common.color',
						'common.media',
					],
					required: true,
				},
				Category: {
					type: 'relation',
					relation: 'manyToOne',
					target: 'api::category.category',
					inversedBy: 'Pages',
					targetAttribute: 'Pages',
					private: false,
				},
				slug: {
					type: 'string',
					required: true,
				},
			},
			visible: true,
			restrictRelationsTo: null,
		},
	},
}

export const defaultConfig = {
	screenTypes: {
		laptop: 1200,
		tablet: 768,
		mobile: 320,
	},
}

// /content-manager/relations/api::page.page/Category?id=aijxq2ojfp1p2pnjtc3k8j95&pageSize=10&_q=&page=1
export const RelationDataById: IMultiDataProps<IRelationResultDataProps> = {
	results: [
		{
			id: 19,
			documentId: 'upc1kn14zg8yu7tp90wvc4ep',
			Headline: 'Introduction',
			publishedAt: null,
			updatedAt: '2025-03-12T11:54:30.208Z',
			status: 'published',
		},
		{
			id: 16,
			documentId: 'gy7j3kfjxs9xdbfk34h9fstj',
			Headline: 'Logo',
			publishedAt: null,
			updatedAt: '2025-03-06T04:46:36.547Z',
			status: 'published',
		},
		{
			id: 20,
			documentId: 'c5468es65qbpeg7br7a0n4fu',
			Headline: 'Photography',
			publishedAt: null,
			updatedAt: '2025-02-21T15:59:30.830Z',
			status: 'published',
		},
		{
			id: 18,
			documentId: 'mbc44v8ctmt0aapcb7s4d8av',
			Headline: 'Verbal',
			publishedAt: null,
			updatedAt: '2025-03-06T05:33:00.670Z',
			status: 'published',
		},
		{
			id: 51,
			documentId: 'eekez8svj4gok1mrt2z06zfj',
			Headline: 'Vision',
			publishedAt: null,
			updatedAt: '2025-03-06T04:44:57.074Z',
			status: 'published',
		},
		{
			id: 17,
			documentId: 'tn1ytgzjl8dic438fi2yxicl',
			Headline: 'Visual',
			publishedAt: null,
			updatedAt: '2025-03-06T04:45:49.139Z',
			status: 'published',
		},
	],
	pagination: {
		page: 1,
		pageSize: 10,
		pageCount: 1,
		total: 6,
	},
}

// /content-manager/relations/api::page.page/aijxq2ojfp1p2pnjtc3k8j95/Category?pageSize=5&page=1
export const RelationDataByPage: IMultiDataProps<IRelationResultDataProps> = {
	pagination: {
		page: 1,
		pageCount: 1,
		pageSize: 10,
		total: 1,
	},
	results: [
		{
			id: 36,
			documentId: 'k2pcfrlzsjmw5enls49qhbn6',
			Headline: 'Archive',
			publishedAt: null,
			updatedAt: '2025-03-04T08:18:04.492Z',
			status: 'published',
		},
	],
}
