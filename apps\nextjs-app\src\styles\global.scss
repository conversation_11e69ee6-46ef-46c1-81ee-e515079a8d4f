@import '@collective/ui-lib/styles/config';

:root {
	// --section-mg-btm: #{px-to(50px, rem)};
	--section-mg-btm: #{px-to(24px, rem)};
}

body {
	display: grid;
	grid-template-columns: px-to(300px, rem) 1fr;
	// min-height: 100vh;
	color: color('black', 100);
	// position: relative;
}

.aidigi__sidebar {
	height: 100vh;
	background-color: color('white', 100);
	position: sticky;
	inset: 0;
	display: flex;
	align-content: flex-start;
	flex-flow: column;
	z-index: 9;
}

.aidigi__grid {
	width: 100%;
	margin-left: auto;
	margin-right: auto;
	max-width: px-to(1331px, rem);
	padding-left: px-to(48px, rem);
	padding-right: px-to(48px, rem);
}
