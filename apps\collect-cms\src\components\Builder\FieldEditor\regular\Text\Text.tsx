import { Icon, Input, useIsomorphicLayoutEffect } from '@collective/core'
import { useMemo, useState, useCallback } from 'react'
import type { FieldProps } from '../../FieldEditor'

export interface TextProps<T> extends FieldProps<T> {
	value?: T
	field?: string
	onChange: (props: { field: string; value: string }) => void
}

/**
 * Text input component that adapts to different field types
 * Supports numeric, string, and password fields with appropriate behaviors
 */
export const Text = <T,>(props: TextProps<T>) => {
	const { type, required, value, onChange, name, placeholder } = props

	const [inputValue, setInputValue] = useState<string>((value as string) || '')
	const [isPasswordVisible, setIsPasswordVisible] = useState<boolean>(false)

	useIsomorphicLayoutEffect(() => {
		setInputValue((value as string) || '')
	}, [value])

	/**
	 * Determines the HTML input type based on the field type
	 */
	const inputType = useMemo(() => {
		if (['integer', 'biginteger', 'decimal', 'float'].includes(type || '')) {
			return 'number'
		}

		if (type === 'string') {
			return 'text'
		}

		if (type === 'password') {
			return isPasswordVisible ? 'text' : 'password'
		}
		return type || ''
	}, [type, isPasswordVisible])

	const handleTogglePasswordVisibility = useCallback(() => {
		setIsPasswordVisible((prevState) => !prevState)
	}, [])

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const newValue = e.target.value
			setInputValue(newValue)
			onChange?.({ field: props.field || name || '', value: newValue })
		},
		[onChange, props.field, name]
	)

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			if (e.key === 'Enter' || e.key === ' ') {
				handleTogglePasswordVisibility()
			}
		},
		[handleTogglePasswordVisibility]
	)

	const renderPasswordToggle = useCallback(() => {
		if (type !== 'password') return null

		return (
			<span
				onClick={handleTogglePasswordVisibility}
				onKeyDown={handleKeyDown}
				role="button"
				tabIndex={0}
				aria-label={isPasswordVisible ? 'Hide password' : 'Show password'}
			>
				<Icon type="cms" variant={isPasswordVisible ? 'hide' : 'show'} />
			</span>
		)
	}, [type, isPasswordVisible, handleTogglePasswordVisibility, handleKeyDown])

	return (
		<Input
			type={inputType}
			className="collect__input has__border"
			required={required}
			value={inputValue}
			placeholder={placeholder}
			onChange={handleInputChange}
			endIcon={renderPasswordToggle()}
		/>
	)
}
