import { checkIsLogin } from '@collective/core'
import {
	createMetadata,
	getCmsData,
	type ICategoryProps,
	// type IComponentProps,
	// type ISEOProps,
	// type INavigationProps,
	type IPageProps,
} from '@collective/integration-lib'
import { Wrapper } from '@collective/ui-lib/src/base/Wrapper'
import cn from 'classnames'
import { data } from 'framer-motion/client'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import { Breadcrumbs } from '@/components/Breadcrumbs/Breadcrumbs'
import { LockedPageAlert } from '@/components/LockedPageAlert/LockedPageAlert'
import { QuickNav } from '@/components/QuickNav/QuickNav'
// import { InitialData as ColorData } from '@/mock/Color'
// import { InitialData as LogoData } from '@/mock/Logo'
// import { InitialData as MockData } from '@/mock/CheatSheets'
import styles from './page.module.scss'

// export const runtime = 'edge'

export async function generateMetadata({
	params,
}: {
	params: { lng: string; category: string; slug: string }
}) {
	try {
		const { lng, slug } = params
		const CmsData = await getCmsData<IPageProps>({
			path: `slugify/slugs/page/${slug}`,
			deep: 2,
			locale: lng,
		})
		const { seo } = CmsData.data
		return createMetadata({ seo, defaultTitle: `AI Digital Branding ${slug} Page` })
	} catch (error) {
		notFound()
	}
}

export default async function Page({
	params: { lng, category, slug },
}: Readonly<{
	params: { lng: string; category: string; slug: string }
}>) {
	try {
		const [PageData, CategoryData] = await Promise.all([
			getCmsData<IPageProps, 'multiple'>({
				path: `pages`,
				deep: 4,
				locale: lng,
				filter: `filters[slug][$eq]=${slug}`,
			}),
			getCmsData<ICategoryProps, 'multiple'>({
				path: `categories`,
				deep: 3,
				locale: lng,
			}),
		])

		if (!PageData?.data?.[0]) {
			notFound()
		}
		if (!CategoryData?.data?.[0]) {
			notFound()
		}

		const { components, Headline } = PageData.data[0]

		const filteredComponents = components.filter((component) => component.enable === true)
		// Find valid first component index (not Header, Media, Hero...)
		const firstValidComponentIndex = filteredComponents.findIndex(
			(component) =>
				component.__component !== 'common.header' &&
				component.__component !== 'common.media' &&
				component.__component !== 'homepage.hero-scene'
		)

		// Get all categories for navigation
		const { Pages, Headline: categoryName } = CategoryData.data[0]

		if (PageData?.data[0]?.isLocked || CategoryData?.data[0]?.isLocked) {
			const cookieStore = cookies()
			const token = cookieStore.get('token')?.value
			if (!token) {
				// notFound() // Trả về giao diện chưa login
				return (
					<div className={styles.wrapper}>
						<LockedPageAlert />
						<QuickNav
							currentCategory={{ Headline: categoryName, slug: category }}
							currentPage={{ Headline, slug }}
							lng={lng}
						/>
					</div>
				)
			}
			const isLogin = await checkIsLogin(token)
			if (!isLogin) {
				// notFound() // Trả về giao diện chưa login
				return (
					<div className={styles.wrapper}>
						<LockedPageAlert />
						<QuickNav
							currentCategory={{ Headline: categoryName, slug: category }}
							currentPage={{ Headline, slug }}
							lng={lng}
						/>
					</div>
				)
			}
		}

		return (
			<div className={cn(styles.wrapper, 'page__content')}>
				<Breadcrumbs
					currentCategory={{
						Headline:
							CategoryData?.data?.find((item: ICategoryProps) => item?.slug === category)
								?.Headline || '',
						slug: category,
					}}
					currentPage={{ Headline, slug }}
					lng={lng}
				/>
				<div className="aidigi__grid">
					{filteredComponents.map((component, index) => (
						<Wrapper
							key={index}
							commonData={{ locales: lng, isFirstSection: index === firstValidComponentIndex }}
							data={component}
						/>
					))}
					{/* {MockData.Components.map((component, index) => (
						<Wrapper key={index} commonData={{ locales: lng }} data={component} />
					))} */}
				</div>
				<QuickNav
					currentCategory={{ Headline: categoryName, slug: category }}
					currentPage={{ Headline, slug }}
					lng={lng}
				/>
			</div>
		)
	} catch (error) {
		notFound()
	}
}
