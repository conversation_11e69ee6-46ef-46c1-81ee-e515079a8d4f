/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/homepage/HeroScene/heroscene.module.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.heroscene_wrapper__U967n {
  position: relative;
  border-radius: 0px 0px 1.5625rem 1.5625rem;
  overflow: hidden;
  margin-bottom: var(--section-mg-btm);
}
.heroscene_wrapper__U967n .heroscene_media__pjqsP {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/homepage/SearchBar/searchbar.module.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.searchbar_wrapper__Xoeeq {
  margin-bottom: var(--section-mg-btm);
}
.searchbar_wrapper__Xoeeq .searchbar_search__wrapper__8dStd {
  display: grid;
  gap: 0.625rem;
}
.searchbar_wrapper__Xoeeq .searchbar_search__wrapper__8dStd h3 {
  font-weight: 400;
}
.searchbar_wrapper__Xoeeq .searchbar_search__bar__OPP_m {
  --input-radius: 9999px;
  --input-padding-x: 2rem;
  --input-padding-y: 0.5rem;
  --input-height: 4rem;
  --input-border-color: #dddde3;
  --icon-gap: 0.625rem;
  margin-bottom: 1.25rem;
}
.searchbar_wrapper__Xoeeq .searchbar_search__bar__OPP_m svg {
  color: #808089;
  --size: 1.25rem;
}
.searchbar_wrapper__Xoeeq .searchbar_search__bar__OPP_m input {
  font-size: 1.25rem;
  font-weight: 400;
}
.searchbar_wrapper__Xoeeq .searchbar_search__bar__OPP_m input:focus {
  --input-border-color: rgba(0, 0, 0, 0.5);
}
.searchbar_wrapper__Xoeeq .searchbar_nav__wrapper__WhL0L {
  display: grid;
  gap: 1.25rem;
}
.searchbar_wrapper__Xoeeq .searchbar_nav__wrapper__WhL0L .searchbar_row__nav__2_Ygr {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 75rem) {
  .searchbar_wrapper__Xoeeq .searchbar_nav__wrapper__WhL0L .searchbar_row__nav__2_Ygr {
    grid-template-columns: repeat(calc(var(--count)), 1fr);
  }
}
.searchbar_wrapper__Xoeeq .searchbar_nav__wrapper__WhL0L .searchbar_col__nav__6Qzj_ {
  background-color: #fafafa;
  padding: 2.5rem;
  border-radius: 1.5625rem;
  min-height: 15rem;
  display: grid;
  gap: 3.125rem;
  align-content: flex-start;
}
.searchbar_wrapper__Xoeeq .searchbar_nav__wrapper__WhL0L .searchbar_col__nav__6Qzj_ h3 {
  font-weight: 400;
}
.searchbar_wrapper__Xoeeq .searchbar_nav__list__le1DN {
  line-height: 180%; /* 36px */
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  color: #686868;
  gap: 4rem;
}
