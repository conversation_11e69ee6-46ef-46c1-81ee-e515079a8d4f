/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.navigationwrap_wrapper__CWlTa {
  margin-bottom: var(--section-mg-btm);
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi {
  display: grid;
  gap: 1.25rem;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_row__nav__N15Ox {
  display: grid;
  gap: 1.25rem;
}
@media (min-width: 75rem) {
  .navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_row__nav__N15Ox {
    grid-template-columns: repeat(12, 1fr);
  }
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W {
  background-color: #fafafa;
  padding: 2.5rem;
  border-radius: 1.5625rem;
  min-height: 15rem;
  display: grid;
  gap: 3.125rem;
  align-content: flex-start;
  grid-column: span 4;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(1):nth-child(3n+1) {
  grid-column: span 12;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(2):nth-child(3n+1), .navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W:nth-last-child(1):nth-child(3n+2) {
  grid-column: span 6;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__wrapper__23AWi .navigationwrap_col__nav__9d88W h2 {
  font-weight: 500;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj {
  display: grid;
  line-height: 180%;
  /* 36px */
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  color: #6e6e73;
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  text-decoration: none;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a {
  position: relative;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a:hover::after {
  width: 100%;
}
.navigationwrap_wrapper__CWlTa .navigationwrap_nav__list__L5XYj a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  height: 0.0625rem;
  width: 0;
  background-color: #6e6e73;
  transition: width 0.2s var(--ease-transition-2);
}
