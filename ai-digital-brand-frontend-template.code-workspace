{
  "folders": [
    {
      "name": "root",
      "path": ".",
    },
    {
      "name": "nextjs-app",
      "path": "apps/nextjs-app",
    },
    {
      "name": "collect-cms",
      "path": "apps/collect-cms",
    },
    {
      "name": "core",
      "path": "packages/core",
    },
    {
      "name": "tsconfig",
      "path": "packages/tsconfig",
    },
    {
      "name": "eslint-config-bases",
      "path": "packages/eslint-config-bases",
    },
    {
      "name": "i18n",
      "path": "packages/i18n",
    },
    {
      "name": "ui-lib",
      "path": "packages/ui-lib",
    },
    {
      "name": "integration-lib",
      "path": "packages/integration-lib",
    },
  ],
  "extensions": {
    "recommendations": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode"],
  },
  "settings": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
    },
    // Disable vscode formatting for js,jsx,ts,tsx files
    // to allow dbaeumer.vscode-eslint to format them
    "[javascript]": {
      "editor.formatOnSave": false,
    },
    "eslint.alwaysShowStatus": true,
    // https://github.com/Microsoft/vscode-eslint#mono-repository-setup
    "eslint.workingDirectories": [
      "./apps/nextjs-app",
      "./apps/collect-cms",
      "./packages/core",
      "./packages/tsconfig",
      "./packages/eslint-config-bases",
      "./packages/i18n",
      "./packages/ui-lib",
      "./packages/integration-lib"
    ],
  },
}
