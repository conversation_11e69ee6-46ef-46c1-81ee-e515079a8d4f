"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/i18next-browser-languagedetector";
exports.ids = ["vendor-chunks/i18next-browser-languagedetector"];
exports.modules = {

/***/ "(ssr)/../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Browser)\n/* harmony export */ });\nconst {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\n");

/***/ })

};
;