@import './config';
@import '@collective/core/styles/reset';

:root {
	--ease-transition: cubic-bezier(0.34, 1.56, 0.64, 1);
	--ease-transition-2: cubic-bezier(0.22, 1, 0.36, 1);
}

.aidigi__display {
	@include fluid($font-size) {
		font-size: size('display');
	}
	line-height: 1.2;
	font-family: var(--anton-font);
}

h1.aidigi__heading,
.aidigi__heading--h1 {
	@include fluid($font-size) {
		font-size: size('heading', 'h1');
	}
	line-height: 1.2;
	font-family: var(--anton-font);
}

h2.aidigi__heading,
.aidigi__heading--h2 {
	@include fluid($font-size) {
		font-size: size('heading', 'h2');
	}
	line-height: 1.2;
	font-family: var(--anton-font);
	font-weight: 600;
}

h3.aidigi__heading,
.aidigi__heading--h3 {
	@include fluid($font-size) {
		font-size: size('heading', 'h3');
	}
	line-height: 1.2;
	font-family: var(--anton-font);
	font-weight: 600;
}

h4.aidigi__heading,
.aidigi__heading--h4 {
	@include fluid($font-size) {
		font-size: size('heading', 'h4');
	}
	line-height: 1.2;
}

h5.aidigi__heading,
.aidigi__heading--h5 {
	@include fluid($font-size) {
		font-size: size('heading', 'h5');
	}
	line-height: 1.2;
}

h6.aidigi__heading,
.aidigi__heading--h6 {
	@include fluid($font-size) {
		font-size: size('heading', 'h6');
	}
	line-height: 1.3;
	font-weight: 600;
}

.aidigi__label {
	@include fluid($font-size) {
		font-size: size('label');
	}
	line-height: 1.2;
	font-weight: 600;
	text-transform: uppercase;
}

p.aidigi__paragraph,
.aidigi__paragraph--md {
	@include fluid($font-size) {
		font-size: size('paragraph', 'md');
	}
	line-height: 1.5;
	font-weight: 500;
}

p.aidigi__paragraph,
.aidigi__paragraph--lg {
	@include fluid($font-size) {
		font-size: size('paragraph', 'lg');
	}
	line-height: 1.5;
	font-weight: 500;
}

.aidigi__button,
.aidigi__link {
	@include fluid($font-size) {
		font-size: size('button');
	}
	line-height: 1.4;
	font-weight: 700;

	&.outline {
		--icon-gap: #{px-to(10px, rem)};
		border-radius: px-to(10px, rem);
		border: 1px solid color('black', 100);
		padding: spacing(s4) spacing(s6);
	}

	&:hover {
		background-color: color('black', 100);
		color: color('white', 100);
	}

	&.disabled {
		color: color('light-gray', 100);
		border-color: color('light-gray', 100);
	}

	&.red {
		color: color('white', 100);
		background-color: color('red', 100);
		border: px-to(1px, rem) solid color('red', 100);
		&:hover {
			color: color('red', 100);
			background-color: color('white', 100);
		}
	}
}

a {
	color: inherit;
	text-decoration: none;
}

section.first__section {
	margin-top: 0;
	&::before {
		display: none;
	}
}

.page__content section {
	.aidigi__grid {
		padding-left: 0;
		padding-right: 0;
	}
}
