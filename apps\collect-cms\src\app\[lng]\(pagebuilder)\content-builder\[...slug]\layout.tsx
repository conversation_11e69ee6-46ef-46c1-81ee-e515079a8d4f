import { cookies } from 'next/headers'
import { notFound, redirect } from 'next/navigation'
import { PageBuilderLayout } from '@/layouts/builder'
import { NavigationData } from '@/mock/Navigation'
import {
	getCollectionTypesData,
	getComponents,
	getConfiguration,
	getContentType,
	getGlobalInit,
	getSingleTypesData,
	type IResultDataProps,
} from 'common/cms'

export default async function ContentBuilderLayout({
	children,
	params: { lng, slug },
}: Readonly<{ children: React.ReactNode; params: { lng: string; slug: string[] } }>) {
	const cookieStore = cookies()
	const authToken = cookieStore.get('adminJwt')?.value
	const currentNav = NavigationData.find((nav) => nav.apiId === slug[0])
	const collectionApiId = slug[1]
	if (!currentNav) {
		notFound()
	}
	const currentCollection = currentNav.layouts.find((layout) => layout.apiId === collectionApiId)
	if (!currentCollection) {
		notFound()
	}
	const currentUid = currentCollection.uid
	let currentData: IResultDataProps | undefined
	const [globalInit, components, contentType, configuration, CmsData] = await Promise.all([
		getGlobalInit({ authToken }),
		getComponents(),
		getContentType(`api::${currentUid}.${currentUid}`),
		getConfiguration({ uid: currentUid, authToken }),
		currentCollection.kind === 'collectionType'
			? getCollectionTypesData({
					path: currentCollection.uid,
					authToken,
					filter: `filters[slug][$eq]=${slug[2]}`,
				})
			: getSingleTypesData({
					path: currentCollection.uid,
					authToken,
					filter: `filters[slug][$eq]=${slug[2]}`,
				}),
	])

	if ('results' in CmsData) {
		currentData = CmsData.results[0]
	} else {
		currentData = CmsData.data
	}

	if (!currentData) {
		notFound()
	}
	return (
		<PageBuilderLayout
			value={{
				components,
				globals: globalInit,
				configuration,
				data: { data: currentData },
				contentType,
				locale: lng,
				slug: slug,
			}}
		>
			{children}
		</PageBuilderLayout>
	)
}
