@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);

	:global(.aidigi__grid) {
		display: grid;
		gap: spacing(s5);
		// padding: 0;
		// padding-top: spacing(s12);
		// padding-bottom: spacing(s6);

		@include min-width('2md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}

	.content {
		@include min-width('2md') {
			grid-column-start: var(--position);
			grid-column-end: span var(--total);
			grid-template-columns: repeat(var(--total), 1fr);
		}

		display: grid;
		gap: spacing(s6);
	}

	.link {
		display: grid;
		grid-column: span var(--width);
		color: color('black', 100);
		a {
			display: flex;
			align-items: center;
			gap: px-to(10px, rem);
			font-weight: 500;
			@include fluid($font-size) {
				font-size: size('paragraph', 'md');
			}
		}

		&__icon {
			color: color('neutral-gray');
		}
		&__label {
			position: relative;
			&::after {
				content: '';
				position: absolute;
				left: 0;
				bottom: 0;
				height: px-to(1px, rem);
				width: 100%;
				background-color: color('black', 100);
			}
		}
	}
}
