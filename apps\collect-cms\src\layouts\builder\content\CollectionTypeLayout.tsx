'use client'

import { Icon, Input } from '@collective/core'
import { useIsomorphicLayoutEffect } from 'framer-motion'
import { usePathname, useRouter } from 'next/navigation'
import { useContext, useState } from 'react'
import { Column, DataTable } from '@/components/DataTable'
import { Paginator } from '@/components/Paginator'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import { NavigationData } from '@/mock/Navigation'
import { getCmsDataAdminClient, type IMultiDataProps, type IResultDataProps } from 'common/cms'
import styles from './contentbuilderlayout.module.scss'
import { SingleTypeLayout } from './SingleTypeLayout'

export const CollectionTypeLayout = ({ lng, uid }: { lng: string; uid: string }) => {
	const context = useContext(PageBuilderContext)
	const { slug: pageSlug } = context
	const router = useRouter()
	const pathname = usePathname()
	const [cmsData, setCmsData] = useState<IResultDataProps[]>([])
	const [pageSize, setPageSize] = useState(5)
	const [currentPage, setCurrentPage] = useState(1)

	const currentNav = NavigationData.find((nav) => nav.apiId === pageSlug[0])
	const currentType = currentNav?.layouts.find((layout) => layout.apiId === pageSlug[1])
	const isEditMode = pageSlug.length >= 4 && pageSlug[pageSlug.length - 1] === 'edit'
	const [curCmsDataIndex, setCurCmsDataIndex] = useState(-1)

	useIsomorphicLayoutEffect(() => {
		const fetchData = async () => {
			// Need implement cache for these data
			const res = await getCmsDataAdminClient<IMultiDataProps<IResultDataProps>>({
				path: `content-manager/collection-types/api::${uid}.${uid}`,
				deep: 3,
				locale: lng,
				filter: 'pageSize=1000',
			})
			setCmsData(res.results)
		}
		fetchData()
	}, [uid, lng])

	useIsomorphicLayoutEffect(() => {
		if (isEditMode === false) return
		if (!cmsData || cmsData.length < 1) return
		const index = cmsData.findIndex((data) => data.slug === pageSlug[pageSlug.length - 2])
		if (index === -1) {
			router.push(pathname.split('/').slice(0, 2).join('/'))
		}
		setCurCmsDataIndex(index)
	}, [cmsData, isEditMode])

	if (!currentType) {
		return null
	}

	if (isEditMode && curCmsDataIndex !== -1) {
		return <SingleTypeLayout lng={lng} uid={uid} initData={cmsData[curCmsDataIndex]} />
	}

	return (
		<>
			<div className={styles.collection__type}>
				<div className={styles.heading}>
					<h3 className="collect__heading">Database</h3>
					<Input
						startIcon={<Icon type="cms" variant="search" />}
						className={styles.input}
						placeholder="Search.."
					/>
				</div>

				<DataTable
					tableStyle={{ minWidth: '50em' }}
					value={cmsData.slice((currentPage - 1) * pageSize, currentPage * pageSize).map((item) => {
						if (item[currentType.identifierField] !== null) {
							if (currentType.defaultMode === 'builder') {
								item.rowLink = `/content-builder/${pathname.split('/').slice(2).join('/')}/${item[currentType.identifierField]}`
							} else {
								item.rowLink = `/content-manager/${pathname.split('/').slice(2).join('/')}/${item[currentType.identifierField]}/edit`
							}
							// item.rowLink = `/content-builder/${pathname.split('/').slice(2).join('/')}/${item[currentType.identifierField]}`
						}
						return item
					})}
					selectionMode="checkbox"
					onSelectionChange={(e) => console.log(e)}
					dataKey="documentId"
				>
					<Column selectionMode="multiple" headerStyle={{ width: '3rem' }}></Column>
					<Column field="id" header="ID"></Column>
					<Column field="Headline" header="Headline" />
					<Column field="slug" header="Slug" />
					<Column
						field="createdDate"
						header="Created Date"
						body={(row: IResultDataProps) => <div>{row.createdAt}</div>}
					/>
				</DataTable>
			</div>
			<Paginator
				totalCount={cmsData.length}
				onPageChange={(num) => setCurrentPage(num)}
				onPageSizeChange={(num) => setPageSize(num)}
				pageSize={pageSize}
				currentPage={currentPage}
			/>
		</>
	)
}
