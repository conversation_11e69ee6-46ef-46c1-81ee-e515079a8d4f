/**
 * Specific eslint rules for this app/package, extends the base rules
 *
 */

const { getDefaultIgnorePatterns } = require('@collective/eslint-config-bases/helpers')

module.exports = {
	root: true,
	parserOptions: {
		parser: '@typescript-eslint/parser',
		tsconfigRootDir: __dirname,
		project: './tsconfig.json',
	},
	ignorePatterns: [...getDefaultIgnorePatterns()],
	extends: [
		'@collective/eslint-config-bases/typescript',
		// Apply prettier and disable incompatible rules
		'@collective/eslint-config-bases/prettier-plugin',
	],
	rules: {
		// optional overrides per project
	},
	overrides: [
		// optional overrides per project file match
	],
}
