@use 'sass:map';
@import '@collective/core/styles/config';

$colors: map.deep-merge(
	$colors,
	(
		'default': (
			'white': (
				0: #ffffff,
				2: #fafafa,
				5: #f7f7f7,
			),
			'grey': (
				10: #f0f0f0,
				15: #e3e3e3,
				20: #d0d0d0,
				30: #b8b8b8,
				40: #a0a0a0,
				50: #898989,
				60: #717171,
				70: #5a5a5a,
				80: #414141,
				90: #2a2a2a,
			),
			'black': (
				0: #121212,
				5: #454545,
			),
			'yellow': (
				50: #fff4cc,
				60: #ffea9f,
				70: #ffd84c,
				80: #ffc700,
				90: #f2b200,
				100: #eba800,
			),
			'green': #309840,
			'red': #cf1313,
			'blue': (
				50: rgba(0, 154, 216, 0.5),
				100: #009ad8,
			),
		),
	)
);

$breakpoints: (
	'xs': px-to(320px, rem),
	'sm': px-to(768px, rem),
	'sm1': px-to(1024px, rem),
	'md': px-to(1200px, rem),
	'lg': px-to(1920px, rem),
	'xl': px-to(2560px, rem),
);

$font-size: map.deep-merge(
	$font-size,
	(
		'@sm': (
			'heading': (
				'h1': 48px,
				'h2': 38px,
				'h3': 32px,
				'h4': 28px,
				'h5': 24px,
				'h6': 20px,
			),
			'label': (
				'subheader': 18px,
				'up-subheader': 16px,
			),
			'body': (
				'lg': 18px,
				'md': 16px,
				'sm': 14px,
				'xs': 12px,
			),
			'button': (
				'xl': 16px,
				'lg': 14px,
				'md': 12px,
			),
		),
		'@md': (
			'heading': (
				'h1': 48px,
				'h2': 38px,
				'h3': 32px,
				'h4': 28px,
				'h5': 24px,
				'h6': 20px,
			),
			'label': (
				'subheader': 18px,
				'up-subheader': 16px,
			),
			'body': (
				'lg': 18px,
				'md': 16px,
				'sm': 14px,
				'xs': 12px,
			),
			'button': (
				'xl': 16px,
				'lg': 14px,
				'md': 12px,
			),
		),
	)
);

$spacing: (
	's0': 0,
	's1': 4px,
	's2': 8px,
	's3': 12px,
	's4': 16px,
	's5': 20px,
	's6': 24px,
	's7': 28px,
	's8': 32px,
	's9': 36px,
	's10': 40px,
	's11': 44px,
	's12': 48px,
	's13': 52px,
	's14': 56px,
	's15': 60px,
	's16': 64px,
	's22': 88px,
	's27': 108px,
	's30': 120px,
	's38': 152px,
	's47': 188px,
	's60': 240px,
	's100': 400px,
);
