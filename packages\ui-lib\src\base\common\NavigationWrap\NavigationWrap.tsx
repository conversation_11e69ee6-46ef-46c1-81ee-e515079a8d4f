// import { useIsomorphicLayoutEffect } from '@collective/core'
// import type { INavigationWrapProps } from '@collective/integration-lib/cms'
// import { searchNavigation } from '@collective/integration-lib/search/meilisearch'
import cn from 'classnames'
// import type { Hits } from 'meilisearch'
import Link from 'next/link'
// import { useState } from 'react'
import styles from './navigationwrap.module.scss'

type NavigationWrapProps = {
	cid?: string
	List: {
		Headline: string
		slug: string
		publishedAt: string | null
		Pages: {
			Headline: string
			slug: string
		}[]
	}[]
}

export const NavigationWrap = ({ cid, List: List0 }: NavigationWrapProps) => {
	// Strapi V5's bug that shows both draft and published data
	const List = List0.filter((item) => item.publishedAt !== null)
	// const [navigation, setNavigation] = useState<Hits<INavigationProps>>([
	// 	{
	// 		id: -99,
	// 		Headline: '',
	// 		slug: '',
	// 		Pages: [
	// 			{
	// 				Headline: '',
	// 				slug: '',
	// 			},
	// 		],
	// 	},
	// ])

	// const searchNavigationWrap = async () => {
	// 	const data = await searchNavigation('', {
	// 		hitsPerPage: List.data.attributes.map((item) => item).length,
	// 		page: 1,
	// 	})
	// 	setNavigation(data.hits)
	// }

	// useIsomorphicLayoutEffect(() => {
	// 	searchNavigationWrap()
	// }, [])
	return (
		<section id={cid} className={cn(styles.wrapper)}>
			<div className="aidigi__grid">
				<div className={styles.nav__wrapper}>
					<div className={styles.row__nav}>
						{List?.map((item, idx) => {
							const { Headline, slug, Pages } = item
							return (
								<div key={idx} className={styles.col__nav}>
									<h2 className="aidigi__heading">
										<Link href={`/${slug}`}>{Headline}</Link>
									</h2>
									<ul
										className={styles.nav__list}
										style={{
											gridTemplateColumns:
												// List divide 2 columns for (n - 1) % 3 === 0 and last item
												idx % 3 === 0 && idx === List.map((item) => item).length - 1
													? `repeat(2, 1fr)`
													: '',
										}}
									>
										{Pages?.map((child, count) => (
											<li key={count}>
												<Link href={`/${child.slug}/${child.slug}`}>{child.Headline}</Link>
											</li>
										))}
									</ul>
								</div>
							)
						})}
					</div>
				</div>
			</div>
		</section>
	)
}
