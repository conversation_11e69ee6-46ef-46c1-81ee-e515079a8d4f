import '@/styles/globals.scss'
import '@collective/ui-lib/styles/global.scss'
import { getNavigationData } from '@collective/integration-lib/cms'
import { searchGeneralSetting, type GeneralSettingProps } from '@collective/integration-lib/search'
import GeneralSettingProvider from '@collective/ui-lib/contexts/GeneralSettingContext'
import NavigationProvider from '@collective/ui-lib/contexts/NavigationContext'
import QuickNavigationContext from '@collective/ui-lib/contexts/QuickNavigationContext'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	// const [generalSetting, nav, quickNav] = await Promise.all([
	// 	searchGeneralSetting(),
	// 	getNavigationData(),
	// 	getNavigationData('2'),
	// ])
	const [generalSetting, nav, quickNav] = [{ hits: [{}] }, [], []]
	return (
		<GeneralSettingProvider data={generalSetting.hits[0] as GeneralSettingProps}>
			<NavigationProvider data={nav}>
				<QuickNavigationContext data={quickNav}>{children}</QuickNavigationContext>
			</NavigationProvider>
		</GeneralSettingProvider>
	)
}
