type PlainObject = {
    [key: string]: string;
};
export declare class ArrayUtils {
    static getRandom<T>(items: T[]): T | undefined;
    static getItem<T>(arr: T[], item: T): T | undefined;
    static updateItem<T>(arr: T[], item: T, data: T): T[];
    static removeItem<T>(arr: T[], item: T): T[];
    static groupBy<T extends PlainObject, K extends keyof T>(array: T[], propertyName: K): Record<T[K], T[]>;
}
export {};
