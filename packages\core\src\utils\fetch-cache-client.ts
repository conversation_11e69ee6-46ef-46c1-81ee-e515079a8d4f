import { get, set } from 'idb-keyval'
import { fetcher } from '../common'
import { getRandomInt } from './get-random-int'

type ICachedDataProps<DataProps> = {
	data: DataProps
	ttl: number
	createdAt: number
}

const updateCache = async <DataProps>(url: string, options: RequestInit, ttl: number) => {
	const data = await fetcher(url, options)
	// console.log('Cache update', data, Date.now())
	set(url, {
		data,
		ttl,
		createdAt: Date.now(),
	})
	return data as DataProps
}

export const cacheWrapper = async <DataProps>(
	url: string,
	options: RequestInit,
	ttl: number = 60,
	revalidateChance: number = 25
) => {
	const cachedData = await get<ICachedDataProps<DataProps>>(url)
	if (cachedData) {
		if (Date.now() - cachedData.createdAt < cachedData.ttl * 1000) {
			if (getRandomInt(0, 100) < revalidateChance) {
				// Chance to update the cache even if the data is still valid
				// This is to prevent the cache from being stale for too long
				// and to ensure that we always have the latest data
				updateCache<DataProps>(url, options, ttl)
			}
			// console.info('Cache hit', url)
			return cachedData.data
		}
		// console.info('Cache miss', cachedData, Date.now())
	}
	return updateCache<DataProps>(url, options, ttl)
}
