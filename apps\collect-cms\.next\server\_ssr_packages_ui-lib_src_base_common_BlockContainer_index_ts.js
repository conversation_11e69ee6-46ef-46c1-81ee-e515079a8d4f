/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_BlockContainer_index_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_BlockContainer_index_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* binding */ BlockContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/../../node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/../../node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var _blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blockcontainer.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss\");\n/* harmony import */ var _blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\n\nconst BlockContainer = ({ cid, Align, Size, Blocks, isFirstSection })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__grid\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().content)),\n                style: {\n                    \"--position\": Size === \"Full\" ? 0 : 7,\n                    \"--total\": Size === \"Full\" ? 12 : 6\n                },\n                children: Blocks.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block), `block__${item.ColorVariant}`),\n                        style: {\n                            \"--layout\": Size === \"Full\" ? Align === \"Horizontal\" ? 6 : 12 : Align === \"Horizontal\" ? 3 : 6\n                        },\n                        children: [\n                            (item.Icon || item.Headline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__title),\n                                children: [\n                                    item.Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_collective_core__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                        variant: item.Icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 24\n                                    }, undefined),\n                                    item.Headline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"aidigi__heading\"),\n                                        children: item.Headline\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 28\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 9\n                            }, undefined),\n                            item.Paragraph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_blockcontainer_module_scss__WEBPACK_IMPORTED_MODULE_2___default().block__content),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                    rehypePlugins: [\n                                        rehype_raw__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                    ],\n                                    children: item.Paragraph\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 10\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 7\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n            lineNumber: 28,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\BlockContainer\\\\BlockContainer.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/index.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: () => (/* reexport safe */ _BlockContainer__WEBPACK_IMPORTED_MODULE_0__.BlockContainer)\n/* harmony export */ });\n/* harmony import */ var _BlockContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContainer */ \"(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/BlockContainer.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRhaW5lci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250YWluZXIvaW5kZXgudHM/NTg3OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL0Jsb2NrQ29udGFpbmVyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"blockcontainer_wrapper__9pM15\",\n\t\"content\": \"blockcontainer_content__CVQDR\",\n\t\"block\": \"blockcontainer_block__FgkM7\",\n\t\"block__title\": \"blockcontainer_block__title__yzrl_\",\n\t\"block__content\": \"blockcontainer_block__content__pprpc\"\n};\n\nmodule.exports.__checksum = \"73f097d7ba0e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9CbG9ja0NvbnRhaW5lci9ibG9ja2NvbnRhaW5lci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vQmxvY2tDb250YWluZXIvYmxvY2tjb250YWluZXIubW9kdWxlLnNjc3M/NDVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiYmxvY2tjb250YWluZXJfd3JhcHBlcl9fOXBNMTVcIixcblx0XCJjb250ZW50XCI6IFwiYmxvY2tjb250YWluZXJfY29udGVudF9fQ1ZRRFJcIixcblx0XCJibG9ja1wiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19GZ2tNN1wiLFxuXHRcImJsb2NrX190aXRsZVwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX190aXRsZV9feXpybF9cIixcblx0XCJibG9ja19fY29udGVudFwiOiBcImJsb2NrY29udGFpbmVyX2Jsb2NrX19jb250ZW50X19wcHJwY1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI3M2YwOTdkN2JhMGVcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/BlockContainer/blockcontainer.module.scss\n");

/***/ })

};
;