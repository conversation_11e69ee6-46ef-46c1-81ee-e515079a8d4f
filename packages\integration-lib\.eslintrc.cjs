/**
 * Specific eslint rules for this app/package, extends the base rules
 *
 */

// Workaround for https://github.com/eslint/eslint/issues/3458 (re-export of @rushstack/eslint-patch)
require('@collective/eslint-config-bases/patch/modern-module-resolution')

const { getDefaultIgnorePatterns } = require('@collective/eslint-config-bases/helpers')

module.exports = {
	root: true,
	parserOptions: {
		parser: '@typescript-eslint/parser',
		tsconfigRootDir: __dirname,
		project: './tsconfig.json',
	},
	ignorePatterns: [...getDefaultIgnorePatterns(), '/storybook-static'],
	extends: [
		'@collective/eslint-config-bases/typescript',
		'@collective/eslint-config-bases/regexp',
		'@collective/eslint-config-bases/rtl',
		'@collective/eslint-config-bases/storybook',
		'@collective/eslint-config-bases/react',
		// Apply prettier and disable incompatible rules
		'@collective/eslint-config-bases/prettier-plugin',
	],
	rules: {
		// optional overrides per project
	},
	overrides: [
		{
			files: ['src/**/*.tsx'],
			rules: {
				'@typescript-eslint/naming-convention': 'off',
			},
		},
	],
}
