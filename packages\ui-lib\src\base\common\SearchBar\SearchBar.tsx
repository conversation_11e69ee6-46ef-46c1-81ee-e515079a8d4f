import { Icon, Input } from '@collective/core'
import cn from 'classnames'
import styles from './searchbar.module.scss'

type SearchBarProps = {
	cid?: string
	Headline: string
}

export const SearchBar = ({ cid, Headline }: SearchBarProps) => (
	<section id={cid} className={cn(styles.wrapper)}>
		<div className="aidigi__grid">
			<div className={styles.search__wrapper}>
				<h3 className={cn(styles.headline, 'aidigi__heading--h3')}>{Headline}</h3>
				<Input
					className={cn(styles.search__bar)}
					startIcon={<Icon className={cn(styles.search__icon)} variant="search-icon" />}
					placeholder="Logo usages"
				/>
			</div>
		</div>
	</section>
)
