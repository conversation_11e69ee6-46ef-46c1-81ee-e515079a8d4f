import {
	DOTS,
	Icon,
	Input,
	Select,
	SelectItem,
	usePagination,
	type OptionProps,
} from '@collective/core'
import cn from 'classnames'
import styles from './paginator.module.scss'

type PaginatorProps = {
	currentPage?: number
	totalCount?: number
	siblingCount?: number
	pageSize?: number
	pageOptions?: number[]
	onPageChange?: (num: number) => void
	onPageSizeChange?: (num: number) => void
}

export const Paginator = ({
	currentPage = 1,
	totalCount = 1,
	siblingCount = 1,
	pageSize = 5,
	pageOptions = [5, 10, 15, 20],
	onPageChange,
	onPageSizeChange,
}: PaginatorProps) => {
	const paginationRange = usePagination({
		currentPage,
		totalCount,
		siblingCount,
		pageSize,
	})

	const lastPage = paginationRange[paginationRange.length - 1]
	const pageSizeOption = pageOptions.find((item) => item === pageSize) || (pageOptions[0] as number)

	const onNext = (isDisabled: boolean) => {
		if (isDisabled) return
		onPageChange?.(currentPage + 1)
	}

	const onPrev = (isDisabled: boolean) => {
		if (isDisabled) return
		onPageChange?.(currentPage - 1)
	}

	const keyboardNavigate = (event: React.KeyboardEvent<HTMLInputElement>) => {
		const { key, which, target } = event
		const targetEvent = target as HTMLInputElement

		if (key === 'Enter' && which === 13) {
			event.preventDefault()
			const pageValue = Number(targetEvent.value)
			onPageChange?.(Math.min(pageValue < 1 ? 1 : pageValue, lastPage as number))
			targetEvent.blur()
		}
	}

	return (
		<div className={styles.wrapper}>
			<div />
			<div className={styles.navigation}>
				<button onClick={() => onPageChange?.(1)}>
					<Icon type="cms" variant="db-chevron-left" />
				</button>
				<button onClick={onPrev.bind(this, currentPage === 1)}>
					<Icon type="cms" variant="chevron-left" />
				</button>
				<ul className={styles.pages}>
					{paginationRange.map((pageNumber, index) => {
						if (pageNumber === DOTS) {
							return (
								<li key={index} className={styles.pagination__dots}>
									&#8230;
								</li>
							)
						}

						return (
							<li
								key={index}
								className={cn(
									styles.pagination__num,
									pageNumber === currentPage && styles.current__pagination
								)}
							>
								<button
									type="button"
									className="bvptw__paragraph-sm"
									onClick={() => onPageChange?.(pageNumber as number)}
								>
									{pageNumber}
								</button>
							</li>
						)
					})}
				</ul>
				<button onClick={onNext.bind(this, currentPage === lastPage)}>
					<Icon type="cms" variant="chevron-right" />
				</button>
				<button onClick={() => onPageChange?.(lastPage as number)}>
					<Icon type="cms" variant="db-chevron-right" />
				</button>
			</div>
			<div className={styles.page__options}>
				<div>
					<span>Show:</span>
					<Select
						className={styles.input}
						defaultOption={{ label: `${pageSizeOption} posts`, value: pageSizeOption }}
						endIcon={<Icon type="cms" variant="chevron-down" />}
						dropdownClass={styles.dropdown}
						onChange={(option) => onPageSizeChange?.(Number((option as OptionProps).value))}
					>
						{pageOptions.map((page, idx) => (
							<SelectItem value={page} key={idx}>
								{page} posts
							</SelectItem>
						))}
					</Select>
				</div>
				<div>
					<span>Go to:</span>
					<Input
						onKeyDown={keyboardNavigate}
						onBlur={(e) => {
							const pageValue = Number(e.target.value)
							onPageChange?.(Math.min(pageValue < 1 ? 1 : pageValue, lastPage as number))
						}}
						type="number"
						className={styles.input}
					/>
				</div>
			</div>
		</div>
	)
}
