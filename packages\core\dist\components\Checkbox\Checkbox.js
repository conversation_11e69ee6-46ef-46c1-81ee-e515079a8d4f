import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import React, { useId } from 'react';
import styles from './checkbox.module.scss';
export const Checkbox = React.forwardRef(({ 
// eslint-disable-next-line react-hooks/rules-of-hooks
id = useId(), label, type = 'checkbox', className, alignCenter, indeterminate, ...rest }, ref) => (_jsxs("div", { className: cn(styles.wrapper, styles[type], className, alignCenter && styles.alignCenter, indeterminate && 'indeterminate'), children: [_jsx("input", { ref: ref, className: styles.input, type: type, id: id, hidden: true, ...rest }), _jsx("label", { htmlFor: id, children: label })] })));
Checkbox.displayName = 'Checkbox';
