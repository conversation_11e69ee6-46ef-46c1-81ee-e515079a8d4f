import { languages } from '@collective/i18n'
import { dir } from 'i18next'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import NavigationProvider from '@/contexts/NavigationContext'
import { Template } from '@/layouts/Template'
import { NavigationData } from '@/mock/Navigation'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
	title: 'Create Next App',
	description: 'Generated by create next app',
}

export async function generateStaticParams() {
	return languages.map((lng) => ({ lng }))
}

export default async function RootLayout({
	children,
	params: { lng },
}: Readonly<{ children: React.ReactNode; params: { lng: string } }>) {
	return (
		<html lang={lng} dir={dir(lng)}>
			<body className={inter.className}>
				<NavigationProvider data={NavigationData}>{children}</NavigationProvider>
			</body>
		</html>
	)
}
