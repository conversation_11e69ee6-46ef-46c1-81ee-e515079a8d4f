/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Color/color.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.color_wrapper__yuBaS {
  margin-bottom: var(--section-mg-btm);
}
.color_wrapper__yuBaS .aidigi__grid {
  display: grid;
  gap: 1.25rem;
}
.color_wrapper__yuBaS .color_row__afjAf {
  display: grid;
  gap: 1.25rem;
  grid-template-columns: repeat(2, 1fr);
}
@media (min-width: 75rem) {
  .color_wrapper__yuBaS .color_row__afjAf {
    grid-template-columns: repeat(var(--count), 1fr);
  }
}
.color_wrapper__yuBaS .color_bg__color__UyYaA {
  position: relative;
  height: 12.5rem;
  border-radius: 1.5625rem;
  cursor: pointer;
}
.color_wrapper__yuBaS .color_bg__color__UyYaA:hover .color_tag__OFAjQ {
  opacity: 1;
  visibility: visible;
}
.color_wrapper__yuBaS .color_description__pExio {
  width: 100%;
  margin: 0.625rem;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}
.color_wrapper__yuBaS .color_description__pExio caption {
  margin-bottom: 1rem;
}
.color_wrapper__yuBaS .color_description__pExio th {
  width: auto;
  text-transform: uppercase;
}
.color_wrapper__yuBaS .color_description__pExio * {
  text-align: left;
}
.color_wrapper__yuBaS .color_tag__OFAjQ {
  visibility: hidden;
  opacity: 0;
  -webkit-user-select: none;
          user-select: none;
  pointer-events: none;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  color: #fff;
  border-radius: 0.3125rem;
  padding: 0.3125rem 0.625rem;
  background-color: rgba(0, 0, 0, 0.25);
  transition: 0.2s var(--ease-transition-2);
  display: flex;
  gap: 0.3125rem;
  align-items: center;
}
