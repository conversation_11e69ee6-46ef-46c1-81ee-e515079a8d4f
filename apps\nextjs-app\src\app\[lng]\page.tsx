import {
	createMetadata,
	getCmsData,
	type IComponentProps,
	type ISEOProps,
} from '@collective/integration-lib'
import { Wrapper } from '@collective/ui-lib/src/base/Wrapper'
import { notFound } from 'next/navigation'

// export const runtime = 'edge'
type Props = {
	components: IComponentProps[]
	seo: ISEOProps
}

export async function generateMetadata({ params }: { params: { lng: string } }) {
	try {
		const { lng } = params
		const CmsData = await getCmsData<Props>({ path: 'home-page', deep: 2, locale: lng })
		const { seo } = CmsData.data
		return createMetadata({ seo, defaultTitle: 'AI Digital Branding Home Page' })
	} catch (error) {
		notFound()
	}
}

export default async function Page({
	params: { lng },
}: Readonly<{
	params: { lng: string }
}>) {
	const CmsData = await getCmsData<Props, 'single'>({
		path: `home-page`,
		deep: 4,
		locale: lng,
		// filter: `filters[components][homepage.nagivation-wrap.List][publishedAt][$notNull]=true`,
	})
	const { components } = CmsData.data
	const filteredComponents = components.map((component) => {
		if (component.__component === 'homepage.nagivation-wrap' && Array.isArray(component.List)) {
			return {
				...component,
				List: component.List.filter((item) => item.publishedAt !== null),
			}
		}
		return component
	})
	return (
		<>
			{filteredComponents.map((component, index) => (
				<Wrapper key={index} commonData={{ locales: lng }} data={component} />
			))}
		</>
	)
}
