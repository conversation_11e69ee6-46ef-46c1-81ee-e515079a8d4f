/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_NavigationWrap_NavigationWrap_tsx";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_NavigationWrap_NavigationWrap_tsx"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationWrap: () => (/* binding */ NavigationWrap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./navigationwrap.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n// import { useIsomorphicLayoutEffect } from '@collective/core'\n// import type { INavigationWrapProps } from '@collective/integration-lib/cms'\n// import { searchNavigation } from '@collective/integration-lib/search/meilisearch'\n\n\n// import type { Hits } from 'meilisearch'\n\n// import { useState } from 'react'\n\nconst NavigationWrap = ({ cid, List: List0 })=>{\n    // Strapi V5's bug that shows both draft and published data\n    const List = List0.filter((item)=>item.publishedAt !== null);\n    // const [navigation, setNavigation] = useState<Hits<INavigationProps>>([\n    // \t{\n    // \t\tid: -99,\n    // \t\tHeadline: '',\n    // \t\tslug: '',\n    // \t\tPages: [\n    // \t\t\t{\n    // \t\t\t\tHeadline: '',\n    // \t\t\t\tslug: '',\n    // \t\t\t},\n    // \t\t],\n    // \t},\n    // ])\n    // const searchNavigationWrap = async () => {\n    // \tconst data = await searchNavigation('', {\n    // \t\thitsPerPage: List.data.attributes.map((item) => item).length,\n    // \t\tpage: 1,\n    // \t})\n    // \tsetNavigation(data.hits)\n    // }\n    // useIsomorphicLayoutEffect(() => {\n    // \tsearchNavigationWrap()\n    // }, [])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__wrapper),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().row__nav),\n                    children: List?.map((item, idx)=>{\n                        const { Headline, slug, Pages } = item;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().col__nav),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"aidigi__heading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: `/${slug}`,\n                                        children: Headline\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 11\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 10\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__list),\n                                    style: {\n                                        gridTemplateColumns: // List divide 2 columns for (n - 1) % 3 === 0 and last item\n                                        idx % 3 === 0 && idx === List.map((item)=>item).length - 1 ? `repeat(2, 1fr)` : \"\"\n                                    },\n                                    children: Pages?.map((child, count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: `/${child.slug}/${child.slug}`,\n                                                children: child.Headline\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        }, count, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 12\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 10\n                                }, undefined)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 6\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                lineNumber: 54,\n                columnNumber: 5\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n            lineNumber: 53,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"navigationwrap_wrapper__CWlTa\",\n\t\"nav__wrapper\": \"navigationwrap_nav__wrapper__23AWi\",\n\t\"row__nav\": \"navigationwrap_row__nav__N15Ox\",\n\t\"col__nav\": \"navigationwrap_col__nav__9d88W\",\n\t\"nav__list\": \"navigationwrap_nav__list__L5XYj\"\n};\n\nmodule.exports.__checksum = \"15553f962f3c\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9OYXZpZ2F0aW9uV3JhcC9uYXZpZ2F0aW9ud3JhcC5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vTmF2aWdhdGlvbldyYXAvbmF2aWdhdGlvbndyYXAubW9kdWxlLnNjc3M/YjUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwibmF2aWdhdGlvbndyYXBfd3JhcHBlcl9fQ1dsVGFcIixcblx0XCJuYXZfX3dyYXBwZXJcIjogXCJuYXZpZ2F0aW9ud3JhcF9uYXZfX3dyYXBwZXJfXzIzQVdpXCIsXG5cdFwicm93X19uYXZcIjogXCJuYXZpZ2F0aW9ud3JhcF9yb3dfX25hdl9fTjE1T3hcIixcblx0XCJjb2xfX25hdlwiOiBcIm5hdmlnYXRpb253cmFwX2NvbF9fbmF2X185ZDg4V1wiLFxuXHRcIm5hdl9fbGlzdFwiOiBcIm5hdmlnYXRpb253cmFwX25hdl9fbGlzdF9fTDVYWWpcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMTU1NTNmOTYyZjNjXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\n");

/***/ })

};
;