'use client'

import cn from 'classnames'
import {
	Children,
	type ComponentType,
	type Dispatch,
	type MouseEvent,
	type SetStateAction,
	createContext,
	useContext,
	useMemo,
	useRef,
	useState,
} from 'react'
import { createPortal } from 'react-dom'
import { useIsomorphicLayoutEffect, useWindowDimensions } from '../../hooks'
import styleSelect from './select.module.scss'
import * as SelectModule from './SelectMode'

export type OptionData = {
	isActive?: boolean
}

export type OptionProps = {
	value: string | number
	label: React.ReactNode
}

const DetectMode = {
	single: 'Single',
	multiple: 'Multiple',
}

type SelectProps = {
	mode?: keyof typeof DetectMode
	id?: string
	label?: string
	dropdownClass?: string
	className?: string
	placeholder?: string
	startIcon?: React.ReactElement
	endIcon?: React.ReactElement
	children?: React.ReactNode
	defaultOption?: OptionProps | OptionProps[]
	onChange?: (option: OptionProps | OptionProps[]) => void
	required?: boolean
}

type DefaultContextProps = {
	mode: keyof typeof DetectMode
	option: OptionProps | OptionProps[]
	setOption: Dispatch<SetStateAction<OptionProps | OptionProps[]>>
	setIsShow: Dispatch<SetStateAction<boolean>>
	isShow?: boolean
	onChange?: (option: OptionProps | OptionProps[]) => void
}

const defaultContext: DefaultContextProps = {
	option: {
		value: '',
		label: '',
	},
	setOption: () => {
		return ''
	},
	setIsShow: () => {
		return ''
	},
	mode: 'single',
	isShow: false,
	onChange: () => {
		return ''
	},
}

export const SelectContext = createContext(defaultContext)

export function Select({
	id,
	label,
	mode = 'single',
	placeholder,
	dropdownClass,
	className,
	startIcon,
	endIcon,
	children,
	defaultOption,
	onChange,
	required,
}: SelectProps) {
	const [isShow, setIsShow] = useState(false)
	const [wrapperPos, setWrapperPos] = useState<{ x: string; y: string }>({
		x: 'left',
		y: 'top',
	})

	const defaultDataOption =
		mode === 'single'
			? {
					value: '',
					label: placeholder || 'Select',
				}
			: []

	const [option, setOption] = useState<OptionProps | OptionProps[]>(defaultDataOption)

	useIsomorphicLayoutEffect(() => {
		if (!defaultOption) return setOption(defaultDataOption)
		return setOption(defaultOption)
	}, [defaultOption])

	const drawerRef = useRef<HTMLDivElement | null>(null)
	const triggerRef = useRef<HTMLElement | null>(null)
	const windowDimension = useWindowDimensions()

	const handleBlurInput = () => {
		const { current: element } = triggerRef
		element?.blur()
		setIsShow(false)
	}

	const handleOnClick = (event: MouseEvent): void => {
		const { target } = event
		if (target === triggerRef.current || drawerRef?.current?.contains(target as Node)) return
		handleBlurInput()
	}

	const placeWrapper = (wrapper: HTMLElement, element: DOMRect) => {
		const { top, left, height, width, right } = element
		console.log(element, left)
		const elmentWrapper = wrapper
		if (left + wrapper.clientWidth > window.innerWidth) {
			elmentWrapper.style.right = `${window.innerWidth - right}px`
			setWrapperPos({ ...wrapperPos, x: 'right' })
		} else {
			elmentWrapper.style.left = `${left}px`
		}

		if (top + wrapper.clientHeight > window.innerHeight) {
			elmentWrapper.style.top = `${window.scrollY + top - wrapper.clientHeight}px`
			setWrapperPos({ ...wrapperPos, y: 'bottom' })
		} else {
			elmentWrapper.style.top = `${window.scrollY + top + height}px`
			setWrapperPos({ ...wrapperPos, y: 'top' })
		}

		elmentWrapper.style.width = `${wrapper.clientWidth < width ? width : wrapper.clientWidth}px`
	}

	useIsomorphicLayoutEffect(() => {
		window.addEventListener('scroll', handleBlurInput)
		window.addEventListener('mousedown', handleOnClick as unknown as EventListener)

		return () => {
			window.removeEventListener('scroll', handleBlurInput)
			window.removeEventListener('mousedown', handleOnClick as unknown as EventListener)
		}
	}, [])

	useIsomorphicLayoutEffect(() => {
		handleBlurInput()
	}, [windowDimension])

	useIsomorphicLayoutEffect(() => {
		const { current: element } = triggerRef
		const elementPosition = element?.getBoundingClientRect()
		const { current: drawerPortal } = drawerRef
		if (!drawerPortal) return
		if (isShow) {
			placeWrapper(drawerPortal, elementPosition as DOMRect)
		} else {
			drawerPortal.removeAttribute('style')
			setWrapperPos({ ...wrapperPos, x: 'top', y: 'left' })
		}
	}, [isShow])

	const PortalElement = (
		<div
			ref={drawerRef}
			id={id && `${id}-wrapper`}
			className={cn(
				'select__dropdown',
				styleSelect.wrapper,
				styleSelect[`drawer__${wrapperPos.x}`],
				styleSelect[`drawer__${wrapperPos.y}`],
				dropdownClass
			)}
		>
			<div className="dropdown__inner">{children}</div>
		</div>
	)
	/* eslint-disable  @typescript-eslint/no-explicit-any */
	const SelectElements: Record<string, ComponentType<any>> = SelectModule
	const SelectMode = SelectElements[DetectMode[mode]] as ComponentType<any>
	const props = {
		label,
		className,
		startIcon,
		endIcon,
		placeholder,
		required,
		totalItem: Children.count(children),
	}
	const ValueContextMemo = useMemo(
		() => ({
			option,
			setOption,
			setIsShow,
			isShow,
			mode,
			onChange,
		}),
		[option, mode, isShow, onChange]
	)
	return (
		<SelectContext.Provider value={ValueContextMemo}>
			<SelectMode {...props} ref={triggerRef} />
			{isShow && createPortal(PortalElement, document.body)}
		</SelectContext.Provider>
	)
}

export function SelectItem({
	value,
	children,
	className,
}: {
	value: string | number
	children: React.ReactNode | ((data: OptionData) => React.ReactNode)
	className?: string
}) {
	const { mode, ...rest } = useContext(SelectContext)
	/* eslint-disable  @typescript-eslint/no-explicit-any */
	const SelectElements: Record<string, ComponentType<any>> = SelectModule
	const SelectMode = SelectElements[`${DetectMode[mode]}Item`] as ComponentType<any>
	return (
		<SelectMode className={className} value={value.toString()} {...rest}>
			{children}
		</SelectMode>
	)
}
