import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import cn from 'classnames';
import React from 'react';
import styles from './input.module.scss';
export const Input = React.forwardRef(({ type = 'text', label, placeholder, className, startIcon, endIcon, ...rest }, ref) => (_jsxs("div", { className: cn(styles['form-control-wrapper'], className, startIcon && styles['form-control-wrapper--icon-left'], endIcon && styles['form-control-wrapper--icon-right']), children: [label && (_jsxs("span", { className: styles.label, children: [label, rest.required && _jsx("span", { className: styles.mark, children: "*" })] })), _jsxs("div", { className: styles.wrapper, children: [startIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--left'], 'icon--left'), children: startIcon })), _jsx("input", { ref: ref, className: styles['form-control'], type: type, placeholder: placeholder, ...rest }), endIcon && (_jsx("span", { className: cn(styles.icon, styles['icon--right'], 'icon--right'), children: endIcon }))] })] })));
Input.displayName = 'Input';
export const TextArea = React.forwardRef(({ placeholder, label, className, maxLength, ...rest }, ref) => (_jsxs("div", { className: cn(styles['form-control-wrapper'], className), children: [label && (_jsxs("span", { className: styles.label, children: [label, rest.required && _jsx("span", { className: styles.mark, children: "*" })] })), _jsx("textarea", { ref: ref, className: styles['form-control'], placeholder: placeholder, ...rest })] })));
TextArea.displayName = 'TextArea';
