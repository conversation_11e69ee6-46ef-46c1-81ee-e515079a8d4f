import { useIsomorphicLayoutEffect } from '@collective/core'
import cn from 'classnames'
import { useState } from 'react'
import type { FieldProps } from '../../FieldEditor'
import styles from './boolean.module.scss'

export interface BooleanProps<T> extends FieldProps<T> {
	value?: T
	onChange: (props: { field: string; value: boolean }) => void
}

export const Boolean = <T,>(props: BooleanProps<T>) => {
	const { value = false, onChange, name, required } = props
	const [checked, setChecked] = useState(!!value)

	useIsomorphicLayoutEffect(() => {
		setChecked(!!value)
	}, [value])

	const handleToggle = () => {
		const newValue = !checked
		setChecked(newValue)
		onChange?.({ field: name as string, value: newValue })
	}

	return (
		<label className={styles.toggle}>
			<input
				type="checkbox"
				checked={checked}
				onChange={handleToggle}
				name={name}
				required={required}
			/>
			<span className={cn(styles.toggle__thumb, checked ? styles.checked : '')} />
			<span className={cn(styles.toggle__label, checked ? styles.active : '')}>On</span>
			<span className={cn(styles.toggle__label, !checked ? styles.active : '')}>Off</span>
		</label>
	)
}
