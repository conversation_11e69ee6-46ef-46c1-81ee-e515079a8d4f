"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-blurhash";
exports.ids = ["vendor-chunks/fast-blurhash"];
exports.modules = {

/***/ "(ssr)/../../node_modules/fast-blurhash/index.js":
/*!*************************************************!*\
  !*** ../../node_modules/fast-blurhash/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeBlurHash: () => (/* binding */ decodeBlurHash),\n/* harmony export */   getBlurHashAverageColor: () => (/* binding */ getBlurHashAverageColor)\n/* harmony export */ });\nconst digitLookup = new Uint8Array(128);\nfor (let i = 0; i < 83; i++) {\n    digitLookup[\n        '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz#$%*+,-.:;=?@[]^_{|}~'.charCodeAt(\n            i,\n        )\n    ] = i;\n}\nconst decode83 = (str, start, end) => {\n    let value = 0;\n    while (start < end) {\n        value *= 83;\n        value += digitLookup[str.charCodeAt(start++)];\n    }\n    return value;\n};\n\nconst pow = Math.pow;\nconst PI = Math.PI;\nconst PI2 = PI * 2;\n\nconst d = 3294.6;\nconst e = 269.025;\nconst sRGBToLinear = (value) =>\n    value > 10.31475 ? pow(value / e + 0.052132, 2.4) : value / d;\n\nconst linearTosRGB = (v) =>\n    ~~(v > 0.00001227 ? e * pow(v, 0.416666) - 13.025 : v * d + 1);\n\nconst signSqr = (x) => (x < 0 ? -1 : 1) * x * x;\n\n/**\n * Fast approximate cosine implementation\n * Based on FTrig https://github.com/netcell/FTrig\n */\nconst fastCos = (x) => {\n    x += PI / 2;\n    while (x > PI) {\n        x -= PI2;\n    }\n    const cos = 1.27323954 * x - 0.405284735 * signSqr(x);\n    return 0.225 * (signSqr(cos) - cos) + cos;\n};\n\n/**\n * Extracts average color from BlurHash image\n * @param {string} blurHash BlurHash image string\n * @returns {[number, number, number]}\n */\nfunction getBlurHashAverageColor(blurHash) {\n    const val = decode83(blurHash, 2, 6);\n    return [val >> 16, (val >> 8) & 255, val & 255];\n}\n\n/**\n * Decodes BlurHash image\n * @param {string} blurHash BlurHash image string\n * @param {number} width Output image width\n * @param {number} height Output image height\n * @param {?number} punch\n * @returns {Uint8ClampedArray}\n */\nfunction decodeBlurHash(blurHash, width, height, punch) {\n    const sizeFlag = decode83(blurHash, 0, 1);\n    const numX = (sizeFlag % 9) + 1;\n    const numY = ~~(sizeFlag / 9) + 1;\n    const size = numX * numY;\n\n    let i = 0,\n        j = 0,\n        x = 0,\n        y = 0,\n        r = 0,\n        g = 0,\n        b = 0,\n        basis = 0,\n        basisY = 0,\n        colorIndex = 0,\n        pixelIndex = 0,\n        value = 0;\n\n    const maximumValue = ((decode83(blurHash, 1, 2) + 1) / 13446) * (punch | 1);\n\n    const colors = new Float64Array(size * 3);\n\n    const averageColor = getBlurHashAverageColor(blurHash);\n    for (i = 0; i < 3; i++) {\n        colors[i] = sRGBToLinear(averageColor[i]);\n    }\n\n    for (i = 1; i < size; i++) {\n        value = decode83(blurHash, 4 + i * 2, 6 + i * 2);\n        colors[i * 3] = signSqr(~~(value / 361) - 9) * maximumValue;\n        colors[i * 3 + 1] = signSqr((~~(value / 19) % 19) - 9) * maximumValue;\n        colors[i * 3 + 2] = signSqr((value % 19) - 9) * maximumValue;\n    }\n\n    const cosinesY = new Float64Array(numY * height);\n    const cosinesX = new Float64Array(numX * width);\n    for (j = 0; j < numY; j++) {\n        for (y = 0; y < height; y++) {\n            cosinesY[j * height + y] = fastCos((PI * y * j) / height);\n        }\n    }\n    for (i = 0; i < numX; i++) {\n        for (x = 0; x < width; x++) {\n            cosinesX[i * width + x] = fastCos((PI * x * i) / width);\n        }\n    }\n\n    const bytesPerRow = width * 4;\n    const pixels = new Uint8ClampedArray(bytesPerRow * height);\n\n    for (y = 0; y < height; y++) {\n        for (x = 0; x < width; x++) {\n            r = g = b = 0;\n            for (j = 0; j < numY; j++) {\n                basisY = cosinesY[j * height + y];\n                for (i = 0; i < numX; i++) {\n                    basis = cosinesX[i * width + x] * basisY;\n                    colorIndex = (i + j * numX) * 3;\n                    r += colors[colorIndex] * basis;\n                    g += colors[colorIndex + 1] * basis;\n                    b += colors[colorIndex + 2] * basis;\n                }\n            }\n\n            pixelIndex = 4 * x + y * bytesPerRow;\n            pixels[pixelIndex] = linearTosRGB(r);\n            pixels[pixelIndex + 1] = linearTosRGB(g);\n            pixels[pixelIndex + 2] = linearTosRGB(b);\n            pixels[pixelIndex + 3] = 255; // alpha\n        }\n    }\n    return pixels;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fast-blurhash/index.js\n");

/***/ })

};
;