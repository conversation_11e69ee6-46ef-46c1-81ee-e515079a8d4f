# CollectCMS Documentation Structure

This document explains the organization of the CollectCMS documentation in the `.ai-planning` folder.

## Documentation Organization

The CollectCMS documentation is organized into two main categories:

1. **Core Documentation**: Provides a comprehensive overview of the system architecture, application flow, component structure, data models, and codebase organization.

2. **Specialized Documentation**: Focuses on specific aspects of the system such as routing, CMS integration, navigation, authentication, and the page builder.

## Documentation Files

### Core Documentation

1. **[Architecture Overview](./architecture-overview.md)**

   - Provides a high-level overview of the CollectCMS architecture
   - Explains the main components and their relationships
   - Includes system architecture diagrams
   - Describes the technology stack

2. **[Application Flow](./application-flow.md)**

   - Details the main user flows and processes
   - Includes flow diagrams for authentication, content management, and page building
   - Explains data fetching and navigation flows

3. **[Component Structure](./component-structure.md)**

   - Outlines the component architecture
   - Explains how different components interact
   - Describes the component hierarchy
   - Details key components and their responsibilities

4. **[Data Models and API Integration](./data-models-and-api.md)**

   - Documents the data models used in the application
   - Explains how the application integrates with the Strapi backend
   - Details API endpoints and data structures
   - Covers error handling and authentication

5. **[Codebase Structure](./codebase-structure.md)**
   - Provides an overview of the codebase organization
   - Explains directory structure and key files
   - Details module organization and dependencies
   - Covers build configuration

### Specialized Documentation

6. **[Routing Documentation](./routing-documentation.md)**

   - Explains the routing system in detail
   - Covers route types, groups, and dynamic routes
   - Details the middleware and authentication flow
   - Describes internationalization in routes

7. **[CMS Integration](./cms-integration.md)**

   - Focuses on the integration with Strapi
   - Explains API communication and data structures
   - Details content types and media management
   - Covers internationalization and error handling

8. **[Navigation Structure](./navigation-structure.md)**

   - Explains the navigation system
   - Details the navigation data structure
   - Describes sidebar and content type navigation
   - Covers route resolution and state management

9. **[Authentication Flow](./authentication-flow.md)**

   - Details the authentication system
   - Explains login, logout, and token management
   - Covers route protection and security considerations
   - Describes authentication state management

10. **[Page Builder System](./page-builder-system.md)**
    - Explains the page builder interface
    - Details component structure and editing
    - Describes the builder context and data flow
    - Covers integration with Strapi for content management

## How to Use This Documentation

1. **For a high-level overview**: Start with the [Architecture Overview](./architecture-overview.md) and [System Overview](./index.md#system-overview) section in the index.

2. **For understanding user flows**: Refer to the [Application Flow](./application-flow.md) document.

3. **For development work**:

   - Understand the [Codebase Structure](./codebase-structure.md) first
   - Then explore the [Component Structure](./component-structure.md)
   - Finally, dive into specialized documentation based on your task

4. **For specific features**:
   - Authentication: [Authentication Flow](./authentication-flow.md)
   - Content Management: [CMS Integration](./cms-integration.md)
   - Page Building: [Page Builder System](./page-builder-system.md)
   - Navigation: [Navigation Structure](./navigation-structure.md)
   - Routing: [Routing Documentation](./routing-documentation.md)

## Diagrams

The documentation includes several types of diagrams:

1. **Architecture Diagrams**: Show the high-level system structure
2. **Flow Diagrams**: Illustrate processes and user flows
3. **Component Hierarchies**: Display the organization of components
4. **Directory Structures**: Visualize the codebase organization

These diagrams are created using Mermaid, a markdown-based diagramming tool that renders directly in the documentation.

## Keeping Documentation Updated

When making significant changes to the codebase, consider updating the relevant documentation files to ensure they remain accurate and useful. Focus on:

1. Updating diagrams to reflect architectural changes
2. Modifying flow descriptions when processes change
3. Adding new components or features to the appropriate documents
4. Ensuring API integration details remain current

By maintaining up-to-date documentation, you help ensure that the CollectCMS system remains maintainable and accessible to new developers.
