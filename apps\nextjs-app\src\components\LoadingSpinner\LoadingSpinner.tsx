import cn from 'classnames'
import styles from './loadingspinner.module.scss'
type Props = {
	// className?: string
	type?: 'icon' | 'page'
	size?: number
	color?: string
}
export const LoadingSpinner = ({ type, size, color }: Props) => (
	// To-do: Add a skeleton loading animation
	<div className={cn(styles.loading, styles[type ? type : ''])}>
		<div
			className={styles['loading-spinner']}
			style={
				{
					'--size': size ? `${size}px` : '48px',
					'--color': color ? color : '#000',
				} as React.CSSProperties
			}
			role="status"
		></div>
	</div>
)
