# CollectCMS Data Models and API Integration

This document details the data models used in CollectCMS and how the application integrates with the Strapi backend API.

## Table of Contents

1. [Data Models](#data-models)
2. [API Integration](#api-integration)
3. [Authentication API](#authentication-api)
4. [Content Type API](#content-type-api)
5. [Content Management API](#content-management-api)
6. [Media API](#media-api)
7. [Error Handling](#error-handling)

## Data Models

### Navigation Model

```typescript
export type INavigationKind = 'collectionType' | 'singleType'

export type INavigationProps = {
  uid: string
  apiId: string
  isDisplayed: boolean
  isPinned: boolean
  info: {
    description?: string
    displayName: string
  }
  kind: 'group'
  layouts: {
    name: string
    uid: string
    apiId: string
    kind: INavigationKind
    visible: boolean
    identifierField: string
  }[]
}
```

### Content Type Model

```typescript
export interface IContentTypeProps {
  apiID: string
  uid: string
  schema: IContentTypeSchemaProps
}

export interface IContentTypeSchemaProps extends ISchemaBaseProps {
  kind: 'collectionType' | 'singleType'
  pluginOptions: { [key: string]: { [key: string]: unknown } }
  draftAndPublish: boolean
  pluralName: string
  singularName: string
  visible: boolean
  restrictRelationsTo?: string[] | null
}
```

### Component Model

```typescript
export type IServerComponentProps = {
  apiID: string
  uid: string
  schema: IComponentSchemaProps
  category: string
}

export interface IComponentSchemaProps extends ISchemaBaseProps {
  icon: string
}
```

### Content Data Model

```typescript
export interface IResultDataProps extends IResultBaseDataPropsWithUser {
  Headline?: string
  Title?: string
  components: IComponentProps[]
  [key: string]: unknown
}

interface IResultBaseDataProps {
  id?: number
  documentId?: string
  createdAt?: string
  createdDate?: string
  locale?: string | null
  updatedAt?: string
  publishedAt?: string | null
  status: 'published' | 'draft' | 'modified'
}
```

### Field Types

```typescript
export type IAttributesDataProps =
  | IAttributeString
  | IAttributeText
  | IAttributeRichText
  | IAttributeNumber
  | IAttributeEnum
  | IAttributeBoolean
  | IAttributeDate
  | IAttributeTime
  | IAttributeDateTime
  | IAttributeEmail
  | IAttributePassword
  | IAttributeUID
  | IAttributeComponent
  | IAttributeDynamicZone
  | IAttributeMedia
  | IAttributeJSON
  | IAttributeRelation
```

## API Integration

### Base API Client

The application uses a common pattern for API requests:

```typescript
export const getCmsData = async <T, K extends 'single' | 'multiple' = 'single'>({
  path,
  deep = 2,
  locale,
  filter,
}: {
  path: string
  deep?: number
  locale?: string
  filter?: string
}): Promise<K extends 'single' ? { data: T } : { data: T[] }> {
  const url = new URL(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`)
  
  // Add query parameters
  url.searchParams.append('populate', `deep,${deep}`)
  if (locale) url.searchParams.append('locale', locale)
  if (filter) url.searchParams.append(filter.split('=')[0], filter.split('=')[1])
  
  // Make the request
  const res = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
    },
    next: { revalidate: 60 },
  })
  
  // Handle response
  if (!res.ok) {
    throw new Error(`API error: ${res.status}`)
  }
  
  return res.json()
}
```

### Client-Side API Client

For authenticated client-side requests:

```typescript
export const getCmsDataAdminClient = async <PageProps>({
  path,
  deep = 4,
  locale = 'en',
  draft = false,
  filter,
}: {
  path: string
  deep?: number
  locale?: string
  draft?: boolean
  filter?: string
}): Promise<PageProps> => {
  if (typeof window === 'undefined') {
    return Promise.reject(new Error('This function is only available on the client'))
  }
  
  const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/${path}?pLevel=${deep}&locale=${locale}${draft ? '&publicationState=preview' : ''}${filter ? `&${filter}` : ''}`
  
  const options = {
    method: 'GET',
    headers: {
      authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
    },
  }
  
  return cacheWrapper<PageProps>(url, options)
}
```

### Server-Side API Client

For server-side authenticated requests:

```typescript
export const getCollectionTypesData = async ({
  path,
  authToken,
  deep = 4,
  page = 1,
  pageSize = 10,
  sort = 'updatedAt:DESC',
  filter,
}: {
  path: string
  authToken?: string
  deep?: number
  page?: number
  pageSize?: number
  sort?: string
  filter?: string
}): Promise<IMultiDataProps<IResultDataProps>> => {
  if (typeof window !== 'undefined') {
    return Promise.reject(new Error('This function is only available on the server'))
  }
  
  const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/collection-types/api::${path}.${[path]}?pLevel=${deep}&page=${page}&pageSize=${pageSize}&sort=${sort}${filter ? `&${filter}` : ''}`
  
  const options = {
    method: 'GET',
    headers: {
      authorization: `Bearer ${authToken}`,
    },
  }
  
  return fetcher(url, options)
}
```

## Authentication API

### Login

```typescript
const login = async (email: string, password: string) => {
  const response = await postJsonFormData<{
    data: {
      token: string
      user: { firstname: string; lastname: string | null; username: string | null }
    }
  }>({
    fullPath: `${process.env.NEXT_PUBLIC_STRAPI_HOST}/admin/login`,
    body: JSON.stringify({ email, password }),
  })
  
  return response.data
}
```

### Token Storage

```typescript
// Store token in cookies for server-side authentication
setCookie('adminJwt', token, rememberMe ? 30 : 1)

// Store token in browser storage for client-side authentication
if (rememberMe) {
  localStorage.setItem('adminJwt', token)
} else {
  sessionStorage.setItem('adminJwt', token)
}

// Store user data
localStorage.setItem('adminUser', JSON.stringify(userData))
```

### Logout

```typescript
const logout = () => {
  localStorage.removeItem('adminJwt')
  sessionStorage.removeItem('adminJwt')
  setCookie('adminJwt', '', 0)
  localStorage.removeItem('adminUser')
  window.location.href = '/login'
}
```

## Content Type API

### Get Content Types

```typescript
export const getContentTypes = async (): Promise<{ data: IContentTypeProps[] }> => {
  const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/content-types`
  const options = {
    method: 'GET',
    headers: {
      authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
    },
    next: { revalidate: 120 },
  }
  return fetcher(url, options)
}
```

### Get Single Content Type

```typescript
export const getContentType = async (type: string): Promise<{ data: IContentTypeProps }> => {
  const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/content-types/${type}`
  const options = {
    method: 'GET',
    headers: {
      authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
    },
    next: { revalidate: 120 },
  }
  return fetcher(url, options)
}
```

### Get Components

```typescript
export const getComponents = async (): Promise<{ data: IServerComponentProps[] }> => {
  const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/content-type-builder/components`
  const options = {
    method: 'GET',
    headers: {
      authorization: `Bearer ${process.env.STRAPI_COLLECT_CMS_API_KEY}`,
    },
    next: { revalidate: 120 },
  }
  return fetcher(url, options)
}
```

## Content Management API

### Get Collection Types Data

```typescript
export const getCollectionTypesData = async ({
  path,
  authToken,
  deep = 4,
  page = 1,
  pageSize = 10,
  sort = 'updatedAt:DESC',
  filter,
}: {
  path: string
  authToken?: string
  deep?: number
  page?: number
  pageSize?: number
  sort?: string
  filter?: string
}): Promise<IMultiDataProps<IResultDataProps>> => {
  // Implementation details
}
```

### Get Single Type Data

```typescript
export const getSingleTypeData = async ({
  path,
  authToken,
  deep = 4,
  locale = 'en',
}: {
  path: string
  authToken?: string
  deep?: number
  locale?: string
}): Promise<ISingleDataProps<IResultDataProps>> => {
  // Implementation details
}
```

### Update Collection Type Data

```typescript
export const updateCollectionTypeData = async ({
  path,
  id,
  data,
  authToken,
}: {
  path: string
  id: number
  data: Record<string, unknown>
  authToken?: string
}): Promise<ISingleDataProps<IResultDataProps>> => {
  // Implementation details
}
```

### Update Single Type Data

```typescript
export const updateSingleTypeData = async ({
  path,
  data,
  authToken,
}: {
  path: string
  data: Record<string, unknown>
  authToken?: string
}): Promise<ISingleDataProps<IResultDataProps>> => {
  // Implementation details
}
```

## Media API

### Upload Media

```typescript
export const uploadMedia = async ({
  file,
  authToken,
}: {
  file: File
  authToken?: string
}): Promise<{ id: number; url: string }> => {
  // Implementation details
}
```

### Get Media Library

```typescript
export const getMediaLibrary = async ({
  authToken,
  page = 1,
  pageSize = 10,
  sort = 'updatedAt:DESC',
  filter,
}: {
  authToken?: string
  page?: number
  pageSize?: number
  sort?: string
  filter?: string
}): Promise<IMultiDataProps<IMediaProps>> => {
  // Implementation details
}
```

## Error Handling

The API integration includes error handling:

```typescript
const fetcher = async <T>(url: string, options: RequestInit): Promise<T> => {
  try {
    const response = await fetch(url, options)
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        JSON.stringify({
          status: response.status,
          statusText: response.statusText,
          data: errorData,
        })
      )
    }
    
    return response.json()
  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}
```

Components handle these errors with try-catch blocks:

```typescript
try {
  const data = await getCmsData({ path: 'content-type' })
  // Process data
} catch (error) {
  // Handle error
  console.error('Failed to fetch data:', error)
  // Show error message to user
}
```

## API Response Formats

### Single Type Response

```typescript
{
  data: {
    id: 1,
    Headline: "Page Title",
    components: [
      {
        __component: "common.header",
        Headline: "Section Title",
        // Other component properties
      },
      // More components
    ],
    createdAt: "2023-01-01T00:00:00.000Z",
    updatedAt: "2023-01-02T00:00:00.000Z",
    publishedAt: "2023-01-02T00:00:00.000Z",
    // Other properties
  },
  meta: {
    // Metadata
  }
}
```

### Collection Type Response

```typescript
{
  results: [
    {
      id: 1,
      Headline: "First Item",
      // Other properties
    },
    {
      id: 2,
      Headline: "Second Item",
      // Other properties
    }
    // More items
  ],
  pagination: {
    page: 1,
    pageSize: 10,
    pageCount: 5,
    total: 42
  }
}
```
