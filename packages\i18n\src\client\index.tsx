/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import i18next from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import resourcesToBackend from 'i18next-resources-to-backend'
import { useEffect } from 'react'
import { initReactI18next, useTranslation as useTranslationOrg } from 'react-i18next'
// import LocizeBackend from 'i18next-locize-backend'
import { defaultNS, getOptions, languages } from '../settings'

const runsOnServerSide = typeof window === 'undefined'

let hasInit = false

const initialize = () => {
	if (hasInit) {
		return
	}
	hasInit = true

	// on client side the normal singleton is ok
	i18next
		.use(initReactI18next)
		.use(LanguageDetector)
		.use(
			resourcesToBackend(
				(language: string, namespace: string) =>
					import(`../../public/locales/${language}/${namespace}.json`)
			)
		)
		// .use(LocizeBackend) // locize backend could be used on client side, but prefer to keep it in sync with server side
		.init({
			...getOptions(),
			lng: undefined, // let detect the language on client side
			detection: {
				order: ['path', 'htmlTag', 'cookie', 'navigator'],
			},
			preload: runsOnServerSide ? languages : [],
		})
}

export function useClientTranslation(
	lng: string,
	ns: string | string[] = defaultNS,
	options: any = {}
) {
	initialize()
	const ret = useTranslationOrg(ns, options)
	const { i18n } = ret
	if (runsOnServerSide && i18n.resolvedLanguage !== lng) {
		i18n.changeLanguage(lng)
	} else {
		// eslint-disable-next-line react-hooks/rules-of-hooks
		useEffect(() => {
			if (i18n.resolvedLanguage === lng) return
			i18n.changeLanguage(lng)
		}, [lng, i18n])
	}
	return ret
}
