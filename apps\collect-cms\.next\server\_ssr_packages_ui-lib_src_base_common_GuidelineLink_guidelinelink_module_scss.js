/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_GuidelineLink_guidelinelink_module_scss";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_GuidelineLink_guidelinelink_module_scss"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss":
/*!*************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"guidelinelink_wrapper__2aoiX\",\n\t\"content\": \"guidelinelink_content__dM8Jn\",\n\t\"link\": \"guidelinelink_link__087_f\",\n\t\"link__icon\": \"guidelinelink_link__icon__q2qEL\",\n\t\"link__label\": \"guidelinelink_link__label__k4pQn\"\n};\n\nmodule.exports.__checksum = \"0df5107c3124\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9HdWlkZWxpbmVMaW5rL2d1aWRlbGluZWxpbmsubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0d1aWRlbGluZUxpbmsvZ3VpZGVsaW5lbGluay5tb2R1bGUuc2Nzcz9jOGMzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJndWlkZWxpbmVsaW5rX3dyYXBwZXJfXzJhb2lYXCIsXG5cdFwiY29udGVudFwiOiBcImd1aWRlbGluZWxpbmtfY29udGVudF9fZE04Sm5cIixcblx0XCJsaW5rXCI6IFwiZ3VpZGVsaW5lbGlua19saW5rX18wODdfZlwiLFxuXHRcImxpbmtfX2ljb25cIjogXCJndWlkZWxpbmVsaW5rX2xpbmtfX2ljb25fX3EycUVMXCIsXG5cdFwibGlua19fbGFiZWxcIjogXCJndWlkZWxpbmVsaW5rX2xpbmtfX2xhYmVsX19rNHBRblwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIwZGY1MTA3YzMxMjRcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/GuidelineLink/guidelinelink.module.scss\n");

/***/ })

};
;