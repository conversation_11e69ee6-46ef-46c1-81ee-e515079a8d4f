@import '@/styles/config';

.wrapper {
	position: relative;
	border-radius: spacing(s2);
	overflow: hidden;
	border: px-to(1px, rem) solid color('grey', 15);
	background-color: color('white', 0);
	textarea {
		position: relative;
		padding: spacing(s2);
		--input-bg: transparent;
		--input-border-color: transparent;
		z-index: 1;
		text-wrap: auto;
	}
	.counter {
		position: absolute;
		bottom: 0;
		background-color: color('white', 5);
		font-weight: 700;
		display: flex;
		width: 100%;
		align-items: center;
		justify-content: flex-end;
		color: color('grey', 50);
		padding: spacing(s2) spacing(s3) spacing(s1);
		z-index: 0;
		@include fluid($font-size) {
			font-size: size('body', 'xs');
		}
	}
}
