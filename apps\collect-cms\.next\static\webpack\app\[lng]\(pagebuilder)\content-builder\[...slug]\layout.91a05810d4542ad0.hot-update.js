"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // If this is the first level (idx = 0), we need to call the Component's handleRemove directly\n        if (idx === 0) {\n            // This is the root level - we need to find the original Component and call its remove function\n            // The current entry's onChange should handle the removal\n            if (currentEntry.onChange) {\n                // For root level, we need to determine if it's repeatable by checking the parent component\n                // Since we don't have direct access to the Component's state, we'll use a different approach\n                // We'll trigger the removal by setting the value to null or removing from array\n                // Check if the current value is part of an array (repeatable) or single object\n                var currentValue = currentEntry.value;\n                // Try to determine if this is part of an array by checking if there are similar entries\n                // This is a heuristic approach - in a real scenario, we'd need better context\n                currentEntry.onChange({\n                    field: \"remove\",\n                    value: null\n                });\n            }\n        } else {\n            // For nested levels, find the parent entry that contains this value\n            var parentEntry = null;\n            var targetIndex = -1;\n            // Search through previous entries to find the parent\n            for(var i = idx - 1; i >= 0; i--){\n                var entry = childCmp[i];\n                if (!entry) continue;\n                if (Array.isArray(entry.value)) {\n                    // Check if current value exists in this array\n                    targetIndex = entry.value.findIndex(function(item) {\n                        return item === currentEntry.value;\n                    });\n                    if (targetIndex !== -1) {\n                        parentEntry = entry;\n                        break;\n                    }\n                }\n            }\n            if (parentEntry && parentEntry.onChange && targetIndex !== -1) {\n                // Remove from parent array\n                var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n                parentValue.splice(targetIndex, 1);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx] || !childCmp[idx].onChange) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        // Determine if this is a repeatable component\n        var parentEntry = childCmp[idx - 1] // Previous level entry\n        ;\n        if (parentEntry && Array.isArray(parentEntry.value)) {\n            // This is a repeatable component - duplicate in array\n            var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n            var targetIndex = parentValue.findIndex(function(item) {\n                return item === currentValue;\n            });\n            if (targetIndex !== -1 && parentEntry.onChange) {\n                // Create a deep copy of the item to duplicate\n                var duplicatedItem = JSON.parse(JSON.stringify(currentValue));\n                parentValue.splice(targetIndex + 1, 0, duplicatedItem);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 151,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});