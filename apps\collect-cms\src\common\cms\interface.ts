// This file contains the interface for the CMS data
// This need to be unified for support multi-cms in the future

import type { IComponentProps } from '@collective/integration-lib/cms'
import type { IResultMetaProps, IUserDataProps } from './strapiV5'

export type IAttributeRelation = {
	type: 'relation'
	relation: string
	target: string
	inversedBy?: string
	targetAttribute?: string | null
	private: boolean
}
export type IAttributeString = {
	type: 'string'
	required?: boolean
	maxLength?: number
	minLength?: number
	regex?: string
	customField?: string
}
export type IAttributeRichText = {
	type: 'richtext'
	options?: { output: string; preset: string }
	customField?: string
	required?: boolean
}
export type IAttributeMedia = {
	type: 'media'
	multiple: boolean
	required: boolean
	allowedTypes: Array<'images' | 'files' | 'videos' | 'audios'>
}
export type IAttributeDynamicZone = {
	type: 'dynamiczone'
	components: string[]
	required: boolean
}

export type IAttributesDataProps =
	| IAttributeString
	| { type: 'boolean'; required?: boolean; default?: boolean }
	| { type: 'text'; regex?: string }
	| { type: 'json' }
	| { type: 'integer'; unique?: boolean; required?: boolean }
	| { type: 'decimal'; required?: boolean }
	| { type: 'datetime' }
	| { type: 'email'; required?: boolean }
	| { type: 'password'; required?: boolean; minLength?: number }
	| { type: 'biginteger'; required?: boolean }
	| IAttributeMedia
	| { type: 'component'; repeatable: boolean; component: string; required?: boolean }
	| { type: 'enumeration'; enum: string[]; required?: boolean }
	| IAttributeRelation
	| IAttributeRichText
	| IAttributeDynamicZone

export interface ISchemaBaseProps {
	displayName: string
	description: string
	collectionName: string
	attributes: Record<string, IAttributesDataProps>
}

export interface IContentTypeSchemaProps extends ISchemaBaseProps {
	kind: 'collectionType' | 'singleType'
	pluginOptions: { [key: string]: { [key: string]: unknown } }
	draftAndPublish: boolean
	pluralName: string
	singularName: string
	visible: boolean
	// Need to check if this is correct
	restrictRelationsTo?: string[] | null
}
export interface IComponentSchemaProps extends ISchemaBaseProps {
	icon: string
}

type IPaginationProps = {
	page: number
	pageSize: number
	pageCount: number
	total: number
}
export interface IMultiDataProps<ResultProps> {
	results: ResultProps[]
	pagination: IPaginationProps
}

export interface ISingleDataProps<ResultProps> {
	data: ResultProps
	meta: IResultMetaProps
}

interface IResultBaseDataProps {
	id?: number
	documentId?: string
	createdAt?: string
	createdDate?: string
	locale?: string | null
	updatedAt?: string
	publishedAt?: string | null
	status: 'published' | 'draft' | 'modified'
}

interface IResultBaseDataPropsWithUser extends IResultBaseDataProps {
	createdBy: IUserDataProps
	updatedBy: IUserDataProps
}
export interface IResultDataProps extends IResultBaseDataPropsWithUser {
	Headline?: string
	Title?: string
	components: IComponentProps[]
	[key: string]: unknown
}

export interface IRelationResultDataProps extends IResultBaseDataProps {
	Headline: string
}

type IConfigurationSettingsProps = {
	bulkable: boolean
	filterable: boolean
	searchable: boolean
	pageSize: number
	mainField: string
	defaultSortBy: string
	defaultSortOrder: 'ASC' | 'DESC'
}
type IConfigurationMetadataProps = {
	[key: string]: {
		edit: {
			label?: string
			description?: string
			placeholder?: string
			visible?: boolean
			editable?: boolean
			mainField?: string
		}
		list: {
			label: string
			searchable: boolean
			sortable: boolean
			mainField?: string
		}
	}
}

type IConfigurationLayoutProps = {
	edit: Array<Array<{ name: string; size: number }>>
	list: Array<string>
}

export interface IConfigurationProps {
	contentType: {
		uid: string
		settings: IConfigurationSettingsProps
		metadatas: IConfigurationMetadataProps
		layouts: IConfigurationLayoutProps
	}
	components: {
		[key: string]: {
			uid: string
			category: string
			settings: IConfigurationSettingsProps
			metadatas: IConfigurationMetadataProps
			layouts: IConfigurationLayoutProps
			[key: string]: unknown
		}
	}
}
