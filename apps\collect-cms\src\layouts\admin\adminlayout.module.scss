@import '@/styles/config';

.wrapper {
	display: grid;
	grid-template-columns: px-to(327px, rem) 1fr;
	position: relative;
	min-height: 100vh;

	aside {
		box-shadow: 0px 4px 24px 0px rgba(69, 69, 69, 0.15);
		position: sticky;
		z-index: 1;
		inset: 0;
		background: color('white', 0);
		display: grid;
		gap: spacing('s6');
		align-content: flex-start;

		.logo__banner {
			background-color: var(--collect-primary-color-80);
			min-height: px-to(75px, rem);
			display: grid;
			justify-content: center;
			align-items: center;

			svg {
				--icon-size: #{px-to(60px, rem)};
			}
		}

		.content__manager {
			display: grid;
			gap: px-to(40px, rem);
		}

		.link {
			margin-left: spacing(s7);
			margin-right: spacing(s7);
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: spacing(s2);
		}

		.navigation {
			padding-right: spacing(s7);
			padding-left: spacing(s7);
		}

		.navigation__block {
			&:not(:first-child) {
				padding-top: spacing(s6);
				margin-top: spacing(s6);
				border-top: 1px solid color('grey', 30);
			}
			display: grid;
			gap: spacing(s2);
		}

		.navigation__routes {
			margin-left: calc(-1 * spacing(s7));
			margin-right: calc(-1 * spacing(s7));

			.pin {
				transform: translateY(-50%);
				left: px-to(6px, rem);
				display: flex;
				position: absolute;
				top: 50%;
				color: color('grey', 30);
				cursor: pointer;
				opacity: 0;
				transition: inherit;
			}

			a {
				transition: inherit;
			}

			li {
				transition: 0.2s var(--ease-transition-2);
				position: relative;
				&:hover {
					background:
						linear-gradient(
							-90deg,
							rgba(color('yellow', 80), 0) 0%,
							rgba(color('yellow', 80), 0.2) 100%
						),
						color('white', 2);

					.pin {
						opacity: 1;
					}
				}
				padding: spacing(s1) spacing(s7);
			}

			.chevron {
				margin-left: auto;
			}
		}
	}

	main {
		background: color('grey', 10);
		height: 100vh;
		overflow-y: scroll;
		display: flex;
		flex-direction: column;

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: spacing(s4) spacing(s8);
			background-color: color('white', 0);
			box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);

			.header_title {
				font-size: px-to(20px, rem);
				font-weight: 600;
				color: color('grey', 90);
			}

			.header_actions {
				display: flex;
				align-items: center;
				gap: spacing(s4);
			}
		}

		.content {
			padding: spacing(s6) spacing(s8);
			flex: 1;
		}
	}
}
