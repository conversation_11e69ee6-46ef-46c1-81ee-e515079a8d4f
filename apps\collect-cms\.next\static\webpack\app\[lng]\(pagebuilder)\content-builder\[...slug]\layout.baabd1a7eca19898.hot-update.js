"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(idx);\n    // const childCmp = childComponentData\n    // if (repeatable && isArray(propsValue)) {\n    // \tconst newValue = [...propsValue]\n    // \tnewValue.splice(idx, 1)\n    // \tconsole.log('delete target:', propsValue[idx], childComponentData)\n    // \tsetPropsValue(newValue as unknown as NonNullable<T>)\n    // \tonChange({ field: name as string, value: newValue })\n    // \tsetChildComponentData(childCmp.filter((item) => item.value !== propsValue[idx]))\n    // } else {\n    // \tsetPropsValue('' as unknown as NonNullable<T>)\n    // \tonChange({ field: name as string, value: null })\n    // \tchildCmp.pop()\n    // \tsetChildComponentData(childCmp)\n    // }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(idx);\n    // const newValue = [...propsValue]\n    // newValue.push(newValue[idx])\n    // setPropsValue(newValue as unknown as NonNullable<T>)\n    // onChange({ field: name as string, value: newValue })\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 88,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});