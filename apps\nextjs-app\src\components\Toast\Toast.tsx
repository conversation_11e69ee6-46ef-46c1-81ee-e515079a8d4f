'use client'
import { Button, Icon, useIsomorphicLayoutEffect } from '@collective/core'
import { useToast, type ToastDataType } from '@collective/ui-lib/contexts/ToastContext'
import cn from 'classnames'
import styles from './toast.module.scss'

type Props = {
	duration?: number
	position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export const Toast = ({ duration = 3000, position = 'top-right' }: Props) => {
	const { toastData, onRemoveToast } = useToast()

	useIsomorphicLayoutEffect(() => {
		setTimeout(() => {
			toastData && toastData[0] && onRemoveToast(toastData[0].tId)
		}, duration)
	}, [toastData])

	if (!toastData) return null

	return (
		<div className={cn(styles.wrapper, position ? styles[position] : '')}>
			{toastData &&
				toastData.map((toast) => {
					const { tId, type, icon, message } = toast
					return (
						<div key={tId} className={cn(styles.toast, type ? styles[`type--${type}`] : '')}>
							<div className={styles.toast__inner}>
								{icon && <Icon className={styles.toast__icon} variant={icon} />}
								<span className={styles.toast__message}>{message}</span>
								<Button className={styles.toast__close} onClick={() => onRemoveToast(tId)}>
									<Icon variant="x" />
								</Button>
							</div>
						</div>
					)
				})}
		</div>
	)
}
