@use '@collective/ui-lib/styles/config' as *;

.accordion {
	border-bottom: 1px solid var(--border-color);
	margin-bottom: spacing('s4');

	@include min-width('sm') {
		margin-bottom: spacing('s7');
	}
}

.accordion__item {
	border-top: 1px solid var(--border-color);
	padding: spacing('s4') 0;

	@include min-width('sm') {
		padding: spacing('s5') 0;
	}

	.trigger {
		width: 100%;
		color: var(--text-color-1);
		display: flex;
		align-items: center;
		justify-content: space-between;
		cursor: pointer;
	}
	h4 {
		line-height: 1.3;
		font-weight: 700;
	}

	:global(.plus__icon) {
		vertical-align: middle;
		display: inline-block;
		--icon-size: var(--size, 16px);
		font-size: var(--icon-size);
		width: var(--icon-size);
		height: var(--icon-size);
		max-width: var(--icon-size);
		flex: 0 0 var(--icon-size);
		transition: transform 0.2s ease;

		:global(.plus__icon-horizontal) {
			transition: inherit;
			transform-origin: center;
		}
	}

	:global(.accordion__content) {
		line-height: 1.4;
		color: var(--text-color-2);

		:global(.content__inner) {
			padding-top: spacing('s3');
		}
	}

	:global(.is__active) {
		:global(.plus__icon) {
			transform: rotate(90deg);
		}
		:global(.plus__icon-horizontal) {
			transform: scaleX(0);
		}
	}
}
