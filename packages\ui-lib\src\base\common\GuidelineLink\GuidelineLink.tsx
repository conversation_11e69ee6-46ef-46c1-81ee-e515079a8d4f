import { Icon } from '@collective/core'
import cn from 'classnames'
import Link from 'next/link'
import styles from './guidelinelink.module.scss'

type GuidelineLinkProps = {
	cid?: string
	Variant: string
	Links: {
		Label: string
		Link: string
	}[]
}

export const GuidelineLink = ({ cid, Variant, Links }: GuidelineLinkProps) => (
	<section id={cid} className={cn(styles.wrapper)}>
		<div className={cn('aidigi__grid')}>
			<div
				className={cn(styles.content)}
				style={
					{
						'--position': Variant == 'fill' ? 0 : 7,
						'--total': Variant == 'fill' ? 12 : 6,
					} as React.CSSProperties
				}
			>
				{Links?.map((item, idx) => (
					<div
						key={idx}
						className={cn(styles.link)}
						style={
							{
								'--width': Variant == 'fill' ? 6 : 3,
							} as React.CSSProperties
						}
					>
						<Link href={item.Link} target="_blank">
							<Icon variant="book-open" className={cn(styles.link__icon)} />
							<span className={cn(styles.link__label)}>{item.Label}</span>
						</Link>
					</div>
				))}
			</div>
		</div>
	</section>
)
