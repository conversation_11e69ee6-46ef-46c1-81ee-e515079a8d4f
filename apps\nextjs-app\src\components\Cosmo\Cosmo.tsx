'use client'
import {
	<PERSON><PERSON>,
	checkIsLoginClient,
	Icon,
	set<PERSON><PERSON><PERSON>,
	Text<PERSON>rea,
	useIsomorphicLayoutEffect,
} from '@collective/core'
import {
	getCustomPathData,
	getDataClient,
	postJsonCustomPathData,
} from '@collective/integration-lib/cms'
import { ChatListContext } from '@collective/ui-lib/contexts/ChatListContext'
import { useToast } from '@collective/ui-lib/contexts/ToastContext'
import cn from 'classnames'
import { use } from 'framer-motion/client'
import { nanoid } from 'nanoid'
import { useRouter } from 'next/navigation'
import { useContext, useMemo, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner'
import styles from './cosmo.module.scss'

const samplePromts = [
	{ Label: 'What makes a brand truly memorable?', categoryName: 'Branding Design' },
	{ Label: 'What makes a brand truly memorable?', categoryName: 'Typography' },
	{
		Label: 'How do you determine the right color palette for the brand or product?',
		categoryName: 'Color',
	},
	{ Label: 'What makes a brand truly memorable?', categoryName: 'Voice and Tone' },
]

type RecommendationProps = {
	un?: string
	Lists: {
		Label: string
		categoryName: string
	}[]
	onPromptClick: (prompt: string) => void
}

const Recommendation = ({ un, Lists, onPromptClick }: RecommendationProps) => {
	return (
		<div className={cn(styles.wrapper)}>
			<div className={cn(styles.wrapper__head)}>
				<h1 className="aidigi__heading">Hello{un ? `, ${un}!` : ' there!'}</h1>
				<h3 className="aidigi__heading">What can I help with?</h3>
			</div>
			<div className={cn(styles.recommendation)}>
				{Lists?.map((item, idx) => (
					<button
						key={idx}
						className={cn(styles.recommendation__item)}
						onClick={() => onPromptClick(item.Label)}
						onKeyDown={(e) => {
							if (e.key === 'Enter' || e.key === ' ') {
								onPromptClick(item.Label)
							}
						}}
					>
						<p>{item.Label}</p>
						<span>{item.categoryName}</span>
					</button>
				))}
			</div>
		</div>
	)
}

type ChatProps = {
	uuid?: string | null
}

type UserProps = {
	username: string
	fullName: string
	email: string
	avatar?: string
}

export const Cosmo = ({ uuid }: ChatProps) => {
	const chatListContext = useContext(ChatListContext)
	const { getNewChatList } = chatListContext
	const [CoChat, setCoChat] = useState([
		{
			role: 'assistant',
			content: 'Welcome to Cōsmo! How can I help you today?',
			time: formatTime(new Date().toISOString()),
		},
	])
	const router = useRouter()
	const [value, setValue] = useState('')
	const [prompt, setPrompt] = useState('')
	const [isLoading, setIsLoading] = useState(false)
	const chatBodyRef = useRef<HTMLDivElement | null>(null)
	const textareaRef = useRef<HTMLTextAreaElement>(null)
	const userHasContent = useMemo(() => CoChat.some((message) => message.role === 'user'), [CoChat])
	const [data, setData] = useState<UserProps | null>(null)
	const [isLess180, setIsLess180] = useState(true)
	const { onPushToast } = useToast()

	const getChatData = async () => {
		if (!uuid) return
		const response = await getCustomPathData(`/api/getChat?uuid=${uuid}`)
		if (response.messages.length > 0) {
			setCoChat(response.messages)
		}
	}

	const handleCreateNewChat = async () => {
		const res = await postJsonCustomPathData<{ uuid: string }>('/api/createChat', '')
		return res.uuid
	}

	const handleChange = (evt: React.ChangeEvent<HTMLTextAreaElement>) => {
		console.log(textareaRef?.current?.style.height)
		if (textareaRef.current) {
			// textareaRef.current.style.height = 'auto' // Reset height
			const newHeight = textareaRef.current.value
				? Math.min(textareaRef.current.scrollHeight, 180)
				: 30
			textareaRef.current.style.height = `${newHeight}px` // Limit 180px or reset to 30px
			newHeight < 180 ? setIsLess180(true) : setIsLess180(false)
		}
		const val = evt.target?.value
		setValue(val)
	}

	const handleChat = async () => {
		if (!value.trim()) return
		setValue('')
		setIsLoading(true)

		try {
			const time = formatTime(new Date().toISOString())
			setCoChat((prevState) => [...prevState, { role: 'user', content: value, time: time }])
			if (!uuid) {
				const newUuid = await handleCreateNewChat()
				const response = await postJsonCustomPathData<{ answer: string }>(
					'/api/ask',
					JSON.stringify({
						text: value,
						time: time,
						uuid: newUuid,
					})
				)
				getNewChatList()
				setCoChat((prevState) => [
					...prevState,
					{ role: 'assistant', content: response.answer, time: time },
				])
				router.push(`/cosmo/${newUuid}`)
			} else {
				const response = await postJsonCustomPathData<{ answer: string }>(
					'/api/ask',
					JSON.stringify({
						text: value,
						time: time,
						uuid,
					})
				)
				setCoChat((prevState) => [
					...prevState,
					{ role: 'assistant', content: response.answer, time: time },
				])
			}
		} catch (error) {
			console.error(error)
		} finally {
			setIsLoading(false)
		}
	}

	const handlePromptClick = (prompt: string) => {
		setPrompt(prompt)
		setValue(prompt)
	}

	const convertMarkdownToPlainText = (markdown: string) => {
		// Xử lý HTML tags
		let text = markdown.replace(/<[^>]*>/g, '')

		// Tiếp tục xử lý Markdown
		text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
		text = text.replace(/(\*\*|__)(.*?)\1/g, '$2')
		text = text.replace(/(\*|_)(.*?)\1/g, '$2')
		text = text.replace(/```[a-z]*\n[\s\S]*?\n```/g, '')
		text = text.replace(/`([^`]+)`/g, '$1')
		text = text.replace(/#{1,6}\s+([^\n]+)/g, '$1')
		text = text.replace(/^\s*[-*+]\s+/gm, '')
		text = text.replace(/^\s*\d+\.\s+/gm, '')
		text = text.replace(/\n\s*\n/g, '\n')

		// Xử lý các entities HTML
		text = text.replace(/&nbsp;/g, ' ')
		text = text.replace(/&amp;/g, '&')
		text = text.replace(/&lt;/g, '<')
		text = text.replace(/&gt;/g, '>')
		text = text.replace(/&quot;/g, '"')
		text = text.replace(/&#39;/g, "'")

		return text.trim()
	}

	const handleCopyContent = (content: string) => {
		const tempElement = document.createElement('div')
		tempElement.innerHTML = content
		let plainText = tempElement.textContent || tempElement.innerText || ''
		plainText = plainText
			.split('\n')
			.map((line) => line.trim())
			.filter((line) => line.length > 0)
			.join('\n')
		navigator.clipboard.writeText(plainText)
		onPushToast({
			tId: nanoid(),
			type: 'success',
			icon: 'tick',
			message: 'Copied to clipboard!',
		})
	}

	useIsomorphicLayoutEffect(() => {
		if (prompt) {
			handleChat()
		}
	}, [prompt])

	useIsomorphicLayoutEffect(() => {
		getChatData()
	}, [uuid])

	useIsomorphicLayoutEffect(() => {
		if (chatBodyRef.current) {
			chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight
		}
	}, [CoChat])

	useIsomorphicLayoutEffect(() => {
		const getUser = async () => {
			const user = await checkIsLoginClient()
			if (user !== null) {
				const data = await getDataClient(`users/me`, user?.strapiToken, 4)
				setData({
					username: data.username,
					email: data.email,
					fullName: data.fullName,
					avatar: data.avatar,
				})
			} else {
				localStorage.removeItem('token')
				sessionStorage.removeItem('token')
				setCookie('token', '', 0)
			}
		}
		getUser()
	}, [])
	return (
		<div className={styles.cosmo}>
			<div className="aidigi__grid">
				{!userHasContent ? (
					<Recommendation
						un={data?.fullName}
						Lists={samplePromts}
						onPromptClick={handlePromptClick}
					/>
				) : (
					<div className={styles.dialog}>
						<div className={styles.dialog__body} ref={chatBodyRef}>
							{CoChat.map((value, index) => {
								if (value.role === 'assistant') {
									return (
										<div key={index} data-message-role="bot">
											<div className={styles.box__chat}>
												<div className={cn('aidigi__paragraph--md', styles.box)}>
													{value && (
														<>
															<ReactMarkdown rehypePlugins={[rehypeRaw]}>
																{value.content.replaceAll('\n    ', '\n')}
															</ReactMarkdown>
															<Button
																className={styles.box__fn}
																onClick={() => handleCopyContent(value.content)}
																title="Copy"
															>
																<Icon variant="copy" />
															</Button>
														</>
													)}
												</div>
											</div>
										</div>
									)
								} else {
									return (
										<div key={index} data-message-role="user">
											<div className={styles.box__chat}>
												<div className={cn('aidigi__paragraph--md', styles.box)}>
													{value.content}
												</div>
											</div>
										</div>
									)
								}
							})}
							{isLoading && (
								<div data-message-role="bot">
									<div className={styles.box__chat}>
										<div className={cn('aidigi__paragraph--md', styles.box)}>
											<LoadingSpinner type="icon" size={36} color="#6e6e73" />
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
				)}

				<div className={styles.message}>
					<div
						className={cn(styles.message__chatbox, value ? styles.valued : '')}
						style={
							{
								'--radius':
									textareaRef?.current?.value.trim() === undefined ||
									textareaRef?.current?.value.trim() === ''
										? '50px'
										: '16px',
							} as React.CSSProperties
						}
					>
						<TextArea
							ref={textareaRef}
							className={cn(styles.message__content, isLess180 ? styles['less180'] : '')}
							value={value}
							onChange={handleChange}
							onKeyDown={(e) => {
								if (e.key === 'Enter' && !e.shiftKey) {
									handleChat()
								}
							}}
							placeholder="Write a message"
						/>
						<Button onClick={handleChat} className={styles.message__btn} disabled={isLoading}>
							<Icon variant="send" />
						</Button>
					</div>
					<span className={styles.message__tips}>
						Cōsmo provides branding insights only and is not a source of scientific or factual
						accuracy. Use discretion for critical decisions.
					</span>
				</div>
			</div>
		</div>
	)
}

const formatTime = (isoTime: string) => {
	return new Date(isoTime).toLocaleTimeString([], {
		hour: '2-digit',
		minute: '2-digit',
		hour12: true,
	})
}
