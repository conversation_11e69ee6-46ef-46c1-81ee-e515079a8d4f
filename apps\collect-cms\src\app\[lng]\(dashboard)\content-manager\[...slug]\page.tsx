import { Icon } from '@collective/core'
import { cookies } from 'next/headers'
import Link from 'next/link'
import { notFound, redirect } from 'next/navigation'
import type { INavigationKind } from '@/contexts/NavigationContext'
import { ContentBuilderlayout } from '@/layouts/builder'
import { NavigationData } from '@/mock/Navigation'
import {
	getComponents,
	getConfiguration,
	getContentType,
	getGlobalInit,
	type IResultDataProps,
} from 'common/cms'

// export const runtime = 'edge'
export default async function Home({
	params: { lng, slug },
}: Readonly<{ params: { lng: string; slug: string[] } }>) {
	const cookieStore = cookies()
	const authToken = cookieStore.get('adminJwt')?.value
	console.log('page slug: ', slug)

	const currentNav = NavigationData.find((nav) => nav.apiId === slug[0])
	if (!currentNav) {
		notFound()
	}

	const typeUid = slug[1]
	const activeChild = currentNav.layouts.findIndex((layout) => layout.apiId === typeUid)
	const { layouts } = currentNav

	if (!currentNav) {
		notFound()
	}
	if (slug.length < 2) {
		redirect(`/content-manager/${slug[0]}/${currentNav.layouts?.[0]?.apiId}`)
	}
	const currentType = currentNav.layouts.find((layout) => layout.apiId === typeUid)
	if (!currentType) {
		notFound()
	}
	// if (slug.length === 3) {
	// 	redirect(`/content-manager/${slug[0]}/${slug[1]}/${slug[2]}/edit`)
	// }
	const currentUid = currentType.uid
	const [globalInit, components, contentType, configuration] = await Promise.all([
		getGlobalInit({ authToken }),
		getComponents(),
		getContentType(`api::${currentUid}.${currentUid}`),
		getConfiguration({ uid: currentUid, authToken }),
	])

	return (
		<div className="content__manager">
			<div className="page__header">
				<button className="text-w-icon">
					<Icon variant="chevron-left" type="cms" /> Back
				</button>
				<div className="page__headline">
					<h1 className="collect__heading">Contact Us</h1>
					<Link href="#" target="blank">
						<Icon variant="newtab" type="cms" />
					</Link>
				</div>
			</div>
			<ContentBuilderlayout
				lng={lng}
				value={{
					components,
					globals: globalInit,
					configuration,
					// data here isn't needed as each Single/CollectionTypeLayout will fetch their own data
					// but we need to pass these to the PageBuilderProvider to avoid error
					data: { data: { components: [] } as unknown as IResultDataProps },
					contentType,
					locale: lng,
					slug: slug,
				}}
				data={layouts}
				activeTabIndex={activeChild}
			/>
		</div>
	)
}
