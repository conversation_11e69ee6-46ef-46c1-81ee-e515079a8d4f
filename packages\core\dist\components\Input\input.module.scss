/* form elements */
@use '@/styles/config' as *;

.form-control {
	transition: all 0.2s ease;
	box-shadow: inset 0 0 0 1px var(--input-border-color);
	padding: var(--input-padding-y) var(--input-padding-x);
	display: flex;
	align-items: center;
	text-wrap: nowrap;

	&::placeholder {
		color: set-lightness(color('neutral', '900'), 0.5);
	}

	&:focus,
	&:focus-within {
		--input-border-color: #{color('primary')};

		box-shadow:
			inset 0 0 0 1px transparent,
			0 0 0 2px var(--input-border-color);
		outline: none;
	}
}

/* component */
.form-control-wrapper {
	--label-color: #{color('neutral', '900')};
	--input-border-color: #{color('neutral', '500')};
	--input-radius: #{px-to(5px, rem)};
	--input-padding-x: #{spacing('s4')};
	--input-padding-y: #{spacing('s3')};
	--icon-gap: #{px-to(9px, rem)};
	--input-icon-size: #{px-to(16px, rem)};
	--input-height: #{px-to(54px, rem)};
	--textarea-height: #{px-to(200px, rem)};
	--input-bg: transparent;
	--required-mark: red;

	position: relative;

	.label {
		@include fluid($font-size) {
			font-size: size('paragraph', 'md');
		}

		margin-bottom: spacing('s3');
		font-weight: 700;
		display: inline-block;
		color: var(--label-color);

		@include min-width('md') {
			margin-bottom: spacing('s4');
		}
	}

	.mark {
		color: var(--required-mark);
	}

	.form-control {
		width: 100%;
		font-weight: 500;
		border-radius: var(--input-radius);
		background-color: var(--input-bg);

		* {
			pointer-events: none;
		}

		&::-webkit-outer-spin-button,
		&::-webkit-inner-spin-button {
			appearance: none;
			margin: 0;
		}

		&[type='number'] {
			appearance: textfield;
		}

		min-height: var(--input-height);
	}

	textarea.form-control {
		width: 100%;
		min-height: var(--textarea-height);
	}

	.wrapper {
		position: relative;
	}

	.icon {
		color: color('fresh-green');
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		width: var(--input-icon-size);
		height: var(--input-icon-size);
		top: 50%;
		transform: translateY(-50%);
		pointer-events: none;

		svg {
			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	&.form-control-wrapper--icon-left .icon.icon--left {
		left: var(--input-padding-x);
	}

	&.form-control-wrapper--icon-left .form-control {
		padding-left: calc(var(--input-padding-x) + var(--icon-gap) + var(--input-icon-size));
	}

	&.form-control-wrapper--icon-right .icon.icon--right {
		right: var(--input-padding-x);
	}

	&.form-control-wrapper--icon-right .form-control {
		padding-right: calc(var(--input-padding-x) + var(--icon-gap) + var(--input-icon-size));
	}
}
