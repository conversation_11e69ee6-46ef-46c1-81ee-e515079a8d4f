# CollectCMS Codebase Structure

This document provides an overview of the CollectCMS codebase structure, explaining the organization of directories and files.

## Table of Contents

1. [Directory Structure](#directory-structure)
2. [Key Files](#key-files)
3. [Module Organization](#module-organization)
4. [Dependency Structure](#dependency-structure)
5. [Build Configuration](#build-configuration)

## Directory Structure

```mermaid
graph TD
    Root[apps/collect-cms] --> SRC[src]
    Root --> Public[public]
    Root --> Config[Configuration Files]
    
    SRC --> App[app]
    SRC --> Components[components]
    SRC --> Common[common]
    SRC --> Contexts[contexts]
    SRC --> Layouts[layouts]
    SRC --> Mock[mock]
    SRC --> Styles[styles]
    
    App --> AppLayout[layout.tsx]
    App --> LngFolder[/[lng]]
    
    LngFolder --> LngLayout[layout.tsx]
    LngFolder --> Dashboard[/(dashboard)]
    LngFolder --> PageBuilder[/(pagebuilder)]
    
    Dashboard --> ContentManager[/content-manager]
    Dashboard --> Login[/login]
    
    PageBuilder --> ContentBuilder[/content-builder]
    
    Components --> BuilderComponents[/Builder]
    Components --> UserMenu[/UserMenu]
    Components --> Other[Other Components]
    
    Common --> CMS[/cms]
    Common --> Utils[/utils]
    
    CMS --> StrapiV5[/strapiV5]
    CMS --> Interface[interface.ts]
    
    Contexts --> BuilderContext[BuilderContext.tsx]
    Contexts --> ManagerContext[ManagerContext.tsx]
    Contexts --> NavigationContext[NavigationContext.tsx]
    
    Layouts --> Admin[/admin]
    Layouts --> Builder[/builder]
    Layouts --> Auth[/auth]
```

## Key Files

### Configuration Files

- `package.json`: Project dependencies and scripts
- `next.config.js`: Next.js configuration
- `tsconfig.json`: TypeScript configuration
- `_routes.json`: Cloudflare routes configuration

### Core Application Files

- `src/app/layout.tsx`: Root layout with global providers
- `src/app/[lng]/layout.tsx`: Language-specific layout
- `src/middleware.ts`: Authentication and i18n middleware

### Context Providers

- `src/contexts/BuilderContext.tsx`: Page builder state management
- `src/contexts/ManagerContext.tsx`: Content manager state management
- `src/contexts/NavigationContext.tsx`: Navigation structure

### API Integration

- `src/common/cms/strapiV5/server.ts`: Server-side API functions
- `src/common/cms/strapiV5/client.ts`: Client-side API functions
- `src/common/cms/interface.ts`: Common interfaces for CMS data

### Layout Components

- `src/layouts/admin/AdminLayout.tsx`: Admin interface layout
- `src/layouts/builder/PageBuilderLayout.tsx`: Page builder layout
- `src/layouts/auth/AuthLayout.tsx`: Authentication pages layout

### Builder Components

- `src/components/Builder/LayoutEditor/LayoutEditor.tsx`: Page layout editor
- `src/components/Builder/ComponentEditor/ComponentEditor.tsx`: Component editor
- `src/components/Builder/FieldEditor/FieldEditor.tsx`: Field type editors
- `src/components/Builder/ComponentMenu/ComponentMenu.tsx`: Component selection menu

### Authentication Components

- `src/app/[lng]/(dashboard)/login/page.tsx`: Login page
- `src/components/UserMenu/UserMenu.tsx`: User profile and logout

## Module Organization

The codebase is organized into several logical modules:

### App Module

The `app` directory follows Next.js App Router conventions:

- Route segments are represented by directories
- `page.tsx` files define the UI for a route
- `layout.tsx` files define shared layouts
- Route groups (in parentheses) organize routes without affecting URLs

```
app/
├── layout.tsx                # Root layout
├── [lng]/                    # Language parameter
│   ├── layout.tsx            # Language-specific layout
│   ├── (dashboard)/          # Dashboard route group
│   │   ├── layout.tsx        # Dashboard layout
│   │   ├── page.tsx          # Dashboard home page
│   │   ├── login/            # Login page
│   │   └── content-manager/  # Content management routes
│   └── (pagebuilder)/        # Page builder route group
│       └── content-builder/  # Content builder routes
```

### Components Module

The `components` directory contains reusable UI components:

```
components/
├── Builder/                  # Page builder components
│   ├── LayoutEditor/         # Layout editing components
│   ├── ComponentEditor/      # Component editing components
│   ├── FieldEditor/          # Field editing components
│   └── ComponentMenu/        # Component selection menu
├── UserMenu/                 # User profile and logout
└── [Other Components]/       # Other UI components
```

### Common Module

The `common` directory contains shared utilities and services:

```
common/
├── cms/                      # CMS integration
│   ├── strapiV5/             # Strapi V5 integration
│   │   ├── client.ts         # Client-side API functions
│   │   └── server.ts         # Server-side API functions
│   └── interface.ts          # Common interfaces
└── utils/                    # Utility functions
```

### Contexts Module

The `contexts` directory contains React context providers:

```
contexts/
├── BuilderContext.tsx        # Page builder state
├── ManagerContext.tsx        # Content manager state
└── NavigationContext.tsx     # Navigation structure
```

### Layouts Module

The `layouts` directory contains layout components:

```
layouts/
├── admin/                    # Admin interface layouts
├── builder/                  # Page builder layouts
└── auth/                     # Authentication layouts
```

### Mock Module

The `mock` directory contains mock data for development:

```
mock/
├── Navigation.ts             # Navigation structure
├── Builder.ts                # Page builder data
└── [Other Mock Data]         # Other mock data
```

### Styles Module

The `styles` directory contains global styles and SCSS modules:

```
styles/
├── globals.scss              # Global styles
├── config.scss               # SCSS variables and mixins
└── [Component Styles]        # Component-specific styles
```

## Dependency Structure

The application has several key dependencies:

### Internal Dependencies

```mermaid
graph TD
    CollectCMS[apps/collect-cms] --> CorePackage[packages/core]
    CollectCMS --> UILibPackage[packages/ui-lib]
    CollectCMS --> IntegrationLibPackage[packages/integration-lib]
    CollectCMS --> I18nPackage[packages/i18n]
    CollectCMS --> TSConfigPackage[packages/tsconfig]
    
    UILibPackage --> CorePackage
    IntegrationLibPackage --> CorePackage
```

### External Dependencies

- **Next.js**: React framework
- **React**: UI library
- **SCSS**: Styling
- **CKEditor**: Rich text editing
- **Framer Motion**: Animations
- **Atlaskit Drag and Drop**: Drag and drop functionality
- **Cloudflare**: Deployment

## Build Configuration

### Next.js Configuration

```javascript
// next.config.js
const nextConfig = {
  reactStrictMode: true,
  images: {
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  transpilePackages: ['@collective/i18n'],
  optimizeFonts: true,
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
  webpack(config, options) {
    // Webpack configuration
    return config
  },
  // Other configuration options
}
```

### TypeScript Configuration

```json
// tsconfig.json
{
  "extends": "@collective/tsconfig/base.json",
  "compilerOptions": {
    "baseUrl": "./src",
    "paths": {
      "@/layouts/*": ["./layouts/*"],
      "@/contexts/*": ["./contexts/*"],
      "@/components/*": ["./components/*"],
      "@/app/*": ["./app/*"],
      "@/public/*": ["../public/*"],
      "@/mock/*": ["./mock/*"],
      "@/styles/*": ["./styles/*"],
      "@collective/ui-lib/*": ["../../../packages/ui-lib/src/*"],
      "@collective/ui-lib": ["../../../packages/ui-lib/src/index"],
      "@collective/integration-lib/*": ["../../../packages/integration-lib/src/*"],
      "@collective/integration-lib": ["../../../packages/integration-lib/src/index"],
      "@collective/core/*": ["@collective/core/dist/*"],
      "@collective/core": ["@collective/core/dist/index"]
    },
    // Other compiler options
  }
}
```

### Package Scripts

```json
// package.json scripts
{
  "scripts": {
    "dev": "next dev",
    "build-cloudflare": "next-on-pages",
    "build": "next build",
    "start": "next start",
    "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql --cache --cache-location ../../.cache/eslint/nextjs-app.eslintcache",
    "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs,.mdx,.graphql . --fix",
    "clean": "rimraf ./.next ./.out ./coverage ./tsconfig.tsbuildinfo ./node_modules/.cache",
    "typecheck": "tsc --project ./tsconfig.json --noEmit"
  }
}
```
