'use client'

import type { INavigationProps } from '@collective/integration-lib/cms'
import { createContext } from 'react'

const GLOBAL_ROUTE = [
	{
		label: 'Terms and Services',
		path: '/terms-of-services',
		isLocked: false,
	},
	{
		label: 'Privacy',
		path: '/privacy',
		isLocked: false,
	},
]

const defaultContext: INavigationProps[] = GLOBAL_ROUTE

export const QuickNavigationContext = createContext(defaultContext)

export default function NavigationProvider({
	data,
	children,
}: {
	data: INavigationProps[]
	children: React.ReactNode
}) {
	return <QuickNavigationContext.Provider value={data}>{children}</QuickNavigationContext.Provider>
}
