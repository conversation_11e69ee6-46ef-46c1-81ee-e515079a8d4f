"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx] || !childCmp[idx].onChange) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        // Determine if this is a repeatable component by checking if the parent value is an array\n        // We need to check the parent's structure to understand if this is part of an array\n        var parentEntry = childCmp[idx - 1] // Previous level entry\n        ;\n        if (parentEntry && Array.isArray(parentEntry.value)) {\n            // This is a repeatable component - remove from array\n            var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n            var targetIndex = parentValue.findIndex(function(item) {\n                return item === currentValue;\n            });\n            if (targetIndex !== -1 && parentEntry.onChange) {\n                parentValue.splice(targetIndex, 1);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        } else {\n            // This is a non-repeatable component - set to null/empty\n            if (parentEntry && parentEntry.onChange) {\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: null\n                });\n            }\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx] || !childCmp[idx].onChange) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        // Determine if this is a repeatable component\n        var parentEntry = childCmp[idx - 1] // Previous level entry\n        ;\n        if (parentEntry && Array.isArray(parentEntry.value)) {\n            // This is a repeatable component - duplicate in array\n            var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n            var targetIndex = parentValue.findIndex(function(item) {\n                return item === currentValue;\n            });\n            if (targetIndex !== -1) {\n                // Create a deep copy of the item to duplicate\n                var duplicatedItem = JSON.parse(JSON.stringify(currentValue));\n                parentValue.splice(targetIndex + 1, 0, duplicatedItem);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 128,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});