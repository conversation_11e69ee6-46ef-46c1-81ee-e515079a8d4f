export const RowSelection = <T extends Record<string, unknown>>(rows: T[], row: T, key: string) => {
	const isExisted = rows.some((item) => row[key] === item[key])
	if (!isExisted) return [...rows, row]
	return rows.filter((item) => row[key] !== item[key])
}

export const isMatchedAllRows = <T extends Record<string, unknown>>(
	rows: T[],
	selectedRows: T[],
	dataKey = 'id'
) => {
	return (
		rows.length > 0 &&
		rows?.every((item) => selectedRows.some((row) => row[dataKey] === item[dataKey]))
	)
}

export const AllRowsSelection = <T extends Record<string, unknown>>(
	rows: T[],
	selectedRows: T[]
) => {
	const isMatchedAll = isMatchedAllRows(rows, selectedRows)

	if (!isMatchedAll) {
		const mergedRows = [...selectedRows, ...rows].filter(
			(value, index, array) => array.indexOf(value) === index
		)
		return mergedRows
	} else {
		return []
	}
}

export class PaginatorClass {
	currentPage: number
	totalPages: number

	constructor(totalPages: number, currentPage = 1) {
		this.totalPages = totalPages
		this.currentPage = currentPage
	}

	next() {
		return new PaginatorClass(this.totalPages, Math.min(this.currentPage + 1, this.totalPages))
	}

	prev() {
		return new PaginatorClass(this.totalPages, Math.max(this.currentPage - 1, 1))
	}

	moveTo(page: number) {
		return new PaginatorClass(this.totalPages, Math.min(page < 1 ? 1 : page, this.totalPages))
	}
}
