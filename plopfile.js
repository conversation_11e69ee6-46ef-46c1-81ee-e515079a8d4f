#!/usr/bin/env node

const defaultPrompts = [
	{
		type: 'input',
		name: 'name',
		message: 'Name',
	},
	{
		type: 'input',
		name: 'path',
		message: 'Path',
	},
]

const buildAddManyActions = (data, directory) => {
	switch (directory) {
		case 'page':
			return [
				{
					type: 'addMany',
					destination: `${process.cwd()}/apps/nextjs-app/src/app/[lng]/${data.path}/{{lowerCase name }}/`,
					templateFiles: `plop-templates/${directory}/**/*.hbs`,
					base: `plop-templates/${directory}`,
					skipIfExists: true,
				},
			]
		case 'mock':
			return [
				{
					type: 'add',
					path: `${process.cwd()}/apps/nextjs-app/src/mock/{{ pascalCase name }}.ts/`,
					template: `export const InitialData = {
  Components: [],
};
`,
					skipIfExists: true,
				},
			]
		default:
			return [
				{
					type: 'addMany',
					destination: `${process.cwd()}/packages/ui-lib/src/base/${data.path}/{{ pascalCase name }}/`,
					templateFiles: `plop-templates/${directory}/**/*.hbs`,
					base: `plop-templates/${directory}`,
				},
				{
					type: 'add',
					path: `${process.cwd()}/packages/ui-lib/src/base/${data.path}/index.ts`,
					template: '// Export modules',
					skipIfExists: true,
				},
				{
					type: 'append',
					path: `${process.cwd()}/packages/ui-lib/src/base/${data.path}/index.ts`,
					pattern: /\/\/ Export modules/,
					template: `export * from './{{pascalCase name}}';`,
				},
			]
	}
}

module.exports = ({ setGenerator, setHelper }) => {
	// HELPER

	setHelper('loud', (aString) => aString.split(' '))

	// GENERATOR

	setGenerator('component', {
		description: 'Typescript component',
		prompts: defaultPrompts,
		actions: (data) => buildAddManyActions(data, 'component'),
	})

	setGenerator('mock', {
		description: 'Mock data',
		prompts: [
			{
				type: 'input',
				name: 'name',
				message: 'Name',
			},
		],
		actions: (data) => buildAddManyActions(data, 'mock'),
	})

	setGenerator('page', {
		description: 'Page',
		prompts: [
			...defaultPrompts,
			{
				type: 'input',
				name: 'initialData',
				message: 'Initial Data',
			},
		],
		actions: (data) => buildAddManyActions(data, 'page'),
	})
}
