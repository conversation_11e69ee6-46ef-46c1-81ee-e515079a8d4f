import { Button, useIsomorphicLayoutEffect } from '@collective/core'
import cn from 'classnames'
import { useRouter, usePathname } from 'next/navigation'
import { useContext, useMemo } from 'react'
import { FieldEditor } from '@/components/Builder'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import { LayerSidebarLayout } from './LayerSidebarLayout'
import styles from './pagebuilderlayout.module.scss'

export const RightSidebarLayout = () => {
	const context = useContext(PageBuilderContext)
	const {
		expandedSidebar,
		contentType,
		configuration,
		data,
		setData,
		childComponentData,
		setChildComponentData,
	} = context
	const { data: commonData } = data || {}
	const router = useRouter()
	const pathname = usePathname()

	const globalFields = useMemo(() => {
		if (!contentType.data || !configuration.data) return []
		const { layouts, settings } = configuration.data.contentType
		const mainFieldKey = settings.mainField

		const {
			[mainFieldKey as keyof typeof contentType.data.schema.attributes]: mainField,
			components,
			...fields
		} = contentType.data.schema.attributes

		const normalizedFields = layouts.edit
			.flat()
			.filter((item) => !['components', mainFieldKey].includes(item.name))
			.map((item) => {
				return {
					...item,
					...fields[item.name],
				}
			})
			.filter((item) => 'type' in item)

		return normalizedFields
	}, [contentType, configuration])

	useIsomorphicLayoutEffect(() => {
		!expandedSidebar.right && setChildComponentData([])
	}, [expandedSidebar.right])

	return (
		<aside className={cn(styles.sidebar, !expandedSidebar.right && styles.is__hidden)}>
			<Button
				className={cn('collect__button', 'collect__button--lg', 'black')}
				onClick={() => {
					const newPath = pathname.replace('content-builder', 'content-manager').split('/')
					if (newPath[newPath.length - 1] !== 'edit') {
						newPath.push('edit')
					}
					router.push(newPath.join('/'))
				}}
			>
				Switch to Manager Mode
			</Button>
			<div className={styles.component__wrapper}>
				{childComponentData && childComponentData[0]?.name !== '' && <LayerSidebarLayout />}
				<div className={styles.component__title}>
					<h5 className="collect__heading collect__heading--h5">Page Settings</h5>
				</div>

				{globalFields?.map((field, idx) => (
					<FieldEditor
						key={idx}
						{...field}
						layerPos="right"
						value={commonData && commonData[field.name as never]}
						onChange={(props) => {
							setData((prevData) => {
								const newData = { ...prevData }
								const { field, value } = props

								newData.data = {
									...newData.data,
									[field.trim()]: value,
								}
								console.log(`[FieldEditor] New data after update:`, newData)
								return newData
							})
						}}
					/>
				))}
			</div>
		</aside>
	)
}
