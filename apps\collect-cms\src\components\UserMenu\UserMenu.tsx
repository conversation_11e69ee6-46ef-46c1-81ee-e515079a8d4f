'use client'

import {
	Button,
	Icon,
	Image,
	checkIsLoginClient,
	setCookie,
	useIsomorphicLayoutEffect,
} from '@collective/core'
import cn from 'classnames'
import { useState } from 'react'
import styles from './usermenu.module.scss'

type UserProps = {
	firstname: string
	lastname: string | null
	username: string | null
	email?: string
}

export const UserMenu = () => {
	const [data, setData] = useState<UserProps | null>(null)
	const [isMenuOpen, setIsMenuOpen] = useState(false)

	useIsomorphicLayoutEffect(() => {
		const getUser = async () => {
			// Get user data from localStorage
			const userDataString = localStorage.getItem('adminUser')
			if (userDataString) {
				try {
					const userData = JSON.parse(userDataString)
					setData(userData)
				} catch (error) {
					console.error('Error parsing user data:', error)
				}
			}
		}
		getUser()
	}, [])

	const handleSignOut = () => {
		localStorage.removeItem('adminJwt')
		sessionStorage.removeItem('adminJwt')
		setCookie('adminJwt', '', 0)
		localStorage.removeItem('adminUser')
		setData(null)
		window.location.href = '/login'
	}

	const toggleMenu = () => {
		setIsMenuOpen(!isMenuOpen)
	}

	const fullName = data ? `${data.firstname}${data.lastname ? ' ' + data.lastname : ''}` : 'User'

	return (
		<div className={styles.user_menu}>
			<button
				className={styles.user_button}
				onClick={toggleMenu}
				aria-expanded={isMenuOpen}
				aria-haspopup="true"
			>
				<div className={styles.user_avatar}>{fullName.charAt(0).toUpperCase()}</div>
				<span className={styles.user_name}>{fullName}</span>
				<Icon
					variant="chevron-down"
					type="cms"
					className={cn(styles.chevron, isMenuOpen && styles.open)}
				/>
			</button>

			{isMenuOpen && (
				<div className={styles.dropdown_menu}>
					<div className={styles.dropdown_header}>
						<div className={styles.user_avatar_large}>{fullName.charAt(0).toUpperCase()}</div>
						<div className={styles.user_info}>
							<span className={styles.user_fullname}>{fullName}</span>
							{data?.username && <span className={styles.user_username}>@{data.username}</span>}
						</div>
					</div>
					<div className={styles.dropdown_divider}></div>
					<div className={styles.dropdown_items}>
						<button className={styles.dropdown_item}>
							<Icon variant="user" type="cms" />
							<span>Profile</span>
						</button>
						<button className={styles.dropdown_item}>
							<Icon variant="settings" type="cms" />
							<span>Settings</span>
						</button>
						<div className={styles.dropdown_divider}></div>
						<button className={styles.dropdown_item} onClick={handleSignOut}>
							<Icon variant="signout" type="cms" />
							<span>Sign out</span>
						</button>
					</div>
				</div>
			)}
		</div>
	)
}
