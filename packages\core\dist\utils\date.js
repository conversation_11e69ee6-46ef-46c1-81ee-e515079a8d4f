import dayjs, { extend } from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
extend(utc);
extend(timezone);
extend(relativeTime);
export class DateUtil {
    timeZone;
    useUTC;
    static NORM_YEAR_PATTERN = 'YYYY';
    static NORM_MONTH_PATTERN = 'YYYY-MM';
    static NORM_DATE_PATTERN = 'YYYY-MM-DD';
    static NORM_DATETIME_MINUTE_PATTERN = 'YYYY-MM-DD HH:mm';
    static NORM_DATETIME_PATTERN = 'YYYY-MM-DD HH:mm:ss';
    static NORM_DATETIME_MS_PATTERN = 'YYYY-MM-DD HH:mm:ss.SSS';
    static UTC_SIMPLE_PATTERN = 'YYYY-MM-DDTHH:mm:ss';
    static UTC_SIMPLE_MS_PATTERN = 'YYYY-MM-DDTHH:mm:ss.SSS';
    static UTC_WITH_ZONE_OFFSET_PATTERN = 'YYYY-MM-DDTHH:mm:ssZ';
    static UTC_MS_WITH_ZONE_OFFSET_PATTERN = 'YYYY-MM-DDTHH:mm:ss.SSSZ';
    constructor(timeZone, useUTC = true) {
        this.timeZone = timeZone;
        this.useUTC = useUTC;
    }
    /**
     * Current time
     *
     * @param date Date
     * @return Current time
     */
    date(date) {
        return (this.useUTC ? dayjs(date).utc() : dayjs(date)).tz(this.timeZone);
    }
    /**
     * Current time, in the format YYYY-MM-DD HH:mm:ss
     *
     * @return The current time in standard form string
     */
    now() {
        return this.date().format(DateUtil.NORM_DATETIME_PATTERN);
    }
    /**
     * Current date, in the format YYYY-MM-DD
     *
     * @return Standard form string of the current date
     */
    today() {
        return this.date().format(DateUtil.NORM_DATE_PATTERN);
    }
    /**
     * Offset days
     *
     * @param offset offset days, positive numbers offset to the future, negative numbers offset to history
     * @param date Date
     * @return offset date
     */
    offsetDay(offset, date = this.date()) {
        return this.offset('day', offset, date);
    }
    /**
     * Offset week
     *
     * @param offset offset week, positive number offset to future, negative number offset to history
     * @param date Date
     * @return offset date
     */
    offsetWeek(offset, date = this.date()) {
        return this.offset('week', offset, date);
    }
    /**
     * Offset month
     *
     * @param offset offset months, positive offset to the future, negative offset to history
     * @param date Date
     * @return offset date
     */
    offsetMonth(offset, date = this.date()) {
        return this.offset('month', offset, date);
    }
    /**
     * Get the time after the specified date offset from the specified time, the generated offset date does not affect the original date
     *
     * @param dateField The granularity size of the offset (hour, day, month, etc.) {@link ManipulateType}
     * @param offset offset, positive number is backward offset, negative number is forward offset
     * @param date the base date
     * @return offset date
     */
    offset(dateField, offset, date = this.date()) {
        if (offset === 0) {
            return date;
        }
        return date[offset > 0 ? 'add' : 'subtract'](Math.abs(offset), dateField);
    }
    /**
     * Calculates relative time from one date to another.
     *
     * @param date Date
     * @return relative time
     */
    relativeTime(date) {
        const specificDate = this.date(date);
        // If the year is different, return the full date in format MM/DD/YYYY
        if (this.date().year() !== specificDate.year()) {
            return specificDate.format('DD/MM/YYYY');
        }
        const diffMinutes = this.date().diff(specificDate, 'minute');
        const diffHours = this.date().diff(specificDate, 'hour');
        const diffDays = this.date().diff(specificDate, 'day');
        // Handle minutes ago
        if (diffMinutes < 60) {
            return diffMinutes === 1 ? 'a minute ago' : `${diffMinutes} minutes ago`;
        }
        // Handle hours ago
        if (diffHours < 24) {
            return diffHours === 1 ? 'an hour ago' : `${diffHours} hours ago`;
        }
        // Handle days ago
        if (diffDays === 1) {
            return 'yesterday';
        }
        else if (diffDays < 7) {
            return `${diffDays} days ago`;
        }
        else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} weeks ago`;
        }
        // Handle months ago
        const months = this.date().diff(specificDate, 'month');
        if (months < 12) {
            return `${months} months ago`;
        }
        // Fallback to full date
        return specificDate.from(this.date());
    }
    /**
     * Tomorrow
     *
     * @return Tomorrow
     */
    tomorrow() {
        return this.offsetDay(1);
    }
    /**
     * Yesterday
     *
     * @return yesterday
     */
    yesterday() {
        return this.offsetDay(-1);
    }
    /**
     * Last week
     *
     * @return Last week
     */
    lastWeek() {
        return this.offsetWeek(-1);
    }
    /**
     * Next week
     *
     * @return Next week
     */
    nextWeek() {
        return this.offsetWeek(1);
    }
    /**
     * Last month
     *
     * @return Last month
     */
    lastMonth() {
        return this.offsetMonth(-1);
    }
    /**
     * Next month
     *
     * @return Next month
     */
    nextMonth() {
        return this.offsetMonth(1);
    }
}
