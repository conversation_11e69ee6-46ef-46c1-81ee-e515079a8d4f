export declare enum IdPrefix {
    Space = "spc",
    Base = "bse",
    Table = "tbl",
    Field = "fld",
    View = "viw",
    Record = "rec",
    Attachment = "act",
    Choice = "cho",
    Workflow = "wfl",
    WorkflowTrigger = "wtr",
    WorkflowAction = "wac",
    WorkflowDecision = "wde",
    User = "usr",
    Account = "aco",
    Invitation = "inv",
    Share = "shr",
    Notification = "not",
    AccessToken = "acc",
    AuthorityMatrix = "aut",
    AuthorityMatrixRole = "aur"
}
export declare function getRandomString(len: number): string;
export declare function generateTableId(): string;
export declare function generateFieldId(): string;
export declare function generateViewId(): string;
export declare function generateRecordId(): string;
export declare function generateChoiceId(): string;
export declare function generateAttachmentId(): string;
export declare function generateWorkflowId(): string;
export declare function generateWorkflowTriggerId(): string;
export declare function generateWorkflowActionId(): string;
export declare function generateWorkflowDecisionId(): string;
export declare function generateUserId(): string;
export declare function identify(id: string): IdPrefix | undefined;
export declare function generateSpaceId(): string;
export declare function generateBaseId(): string;
export declare function generateInvitationId(): string;
export declare function generateShareId(): string;
export declare function generateNotificationId(): string;
export declare function generateAccessTokenId(): string;
export declare function generateAccountId(): string;
export declare function generateAuthorityMatrixId(): string;
export declare function generateAuthorityMatrixRoleId(): string;
