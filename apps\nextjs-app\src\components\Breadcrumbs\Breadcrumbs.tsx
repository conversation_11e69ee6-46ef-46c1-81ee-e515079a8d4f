import Link from 'next/link'
import styles from './breadcrumbs.module.scss'

type Props = {
	currentCategory?: {
		Headline: string
		slug: string
	}
	currentPage?: {
		Headline: string
		slug: string
	}
	lng?: string
}

export const Breadcrumbs = ({ currentCategory, currentPage, lng = 'en' }: Props) => {
	return (
		<div className="aidigi__grid">
			<ul className={styles.breadcrumbs}>
				<li className={styles.breadcrumbs__item}>
					<Link href={`/${lng}/${currentCategory?.slug}`}>{currentCategory?.Headline}</Link>
				</li>
				<li className={styles.breadcrumbs__item}>/</li>
				<li className={styles.breadcrumbs__item}>{currentPage?.Headline}</li>
			</ul>
		</div>
	)
}
