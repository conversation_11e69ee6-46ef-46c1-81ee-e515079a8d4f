'use client'
import { Button, Checkbox, Icon, Input, setCookie } from '@collective/core'
import { postJsonCustomPathData } from '@collective/integration-lib/cms'
import { useModal } from '@collective/ui-lib/contexts/ModalContext'
import cn from 'classnames'
import Link from 'next/link'
import { useState, type FormEvent } from 'react'
import styles from './modal.module.scss'

type ModalProps = {
	data?: {
		heading?: string
		message?: string
		onConfirm?: () => void
	}
	onClose: () => void
}

const ModalSignin = ({ onClose }: ModalProps) => {
	const [isLoading, setIsLoading] = useState(false)
	const [isPwVisible, setIsPwVisible] = useState(false)
	const [rememberMe, setRememberMe] = useState(false)
	const [formData, setFormData] = useState({ email: '', password: '' })
	const [errors, setErrors] = useState({ email: '', password: '', overall: '' })

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target
		setFormData((prev) => ({ ...prev, [name]: value }))
	}

	const handleValidate = () => {
		const newErrors = { email: '', password: '', overall: '' }

		if (!formData.email) newErrors.email = 'Email is required!'
		else if (!/^\S[^\s@]*@\S[^\s.]*\.\S+$/.test(formData.email))
			newErrors.email = 'Invalid email format!'

		if (!formData.password) newErrors.password = 'Password is required!'
		else if (formData.password.length < 6)
			newErrors.password = 'Password must be at least 6 characters long!'

		setErrors(newErrors)
		return Object.values(newErrors).every((err) => err === '')
	}

	const handleSignIn = async (event: FormEvent<HTMLFormElement>): Promise<void> => {
		event.preventDefault()
		setIsLoading(true)

		// Validating...
		if (!handleValidate()) {
			setIsLoading(false)
			return
		}

		const data = { identifier: formData.email, password: formData.password }

		try {
			if (localStorage) {
				const response = await postJsonCustomPathData<{
					token: string
				}>('/api/login', JSON.stringify(data))
				// Clear any existing tokens
				localStorage.removeItem('token')
				sessionStorage.removeItem('token')
				setCookie('token', '', 1)

				// Set the token in the appropriate storage based on "Remember me"
				setCookie('token', response.token || '', rememberMe ? 30 : 1)

				if (rememberMe) {
					localStorage.setItem('token', response.token || '')
				} else {
					sessionStorage.setItem('token', response.token || '')
				}

				setTimeout(() => {
					onClose()
					setErrors({ email: '', password: '', overall: '' })
					location.reload()
				}, 1500)
			}
		} catch (error) {
			const errorData = JSON.parse(error as string)
			setErrors((prev) => ({ ...prev, overall: errorData.title }))
		} finally {
			setIsLoading(false)
		}
	}

	const handleShowPws = () => {
		setIsPwVisible(!isPwVisible)
	}

	const handleRememberMeChange = (checked: boolean) => {
		setRememberMe(checked)
	}

	return (
		<div className={styles.modal__wrapper}>
			<div className={styles.modal__header}>
				<h2 className="aidigi__heading">Sign in</h2>
			</div>
			<form onSubmit={handleSignIn} noValidate>
				<div className={styles.modal__body}>
					{/* Email */}
					<div className={cn(styles.controller, errors.email ? styles.controller__error : '')}>
						<Input
							name="email"
							type="email"
							label="Email Address"
							placeholder="Enter your email address"
							value={formData.email}
							onChange={handleChange}
							autoComplete="off"
						/>
						<small className={styles.controller__error__msg}>{errors.email && errors.email}</small>
					</div>

					{/* Password */}
					<div className={cn(styles.controller, errors.password ? styles.controller__error : '')}>
						<Button className={styles.showhide} onClick={handleShowPws}>
							{isPwVisible ? (
								<>
									<Icon variant="hide" /> Hide
								</>
							) : (
								<>
									<Icon variant="show" /> Show
								</>
							)}
						</Button>
						<Input
							name="password"
							type={isPwVisible ? 'input' : 'password'}
							label="Password"
							placeholder="Password"
							value={formData.password}
							onChange={handleChange}
							autoComplete="off"
						/>
						<small className={styles.controller__error__msg}>
							{errors.password && errors.password}
						</small>
					</div>
				</div>
				<div className={styles.modal__footer}>
					<div className={styles.remember}>
						<Checkbox
							label="Remember me"
							checked={rememberMe}
							onChange={(e) => handleRememberMeChange(e.target.checked)}
						/>
						<Link href="#">Forget Password</Link>
					</div>

					{/* Submit */}
					<Button
						type="submit"
						className={cn('aidigi__button', 'outline', 'submit', isLoading ? 'disabled' : '')}
						disabled={isLoading}
					>
						{isLoading ? 'Signing in...' : 'Sign in'}
					</Button>

					{/* Alert */}
					<div className={styles.alert}>{errors.overall && errors.overall}</div>
				</div>
			</form>
		</div>
	)
}

const ModalConfirm = ({ onClose, data }: ModalProps) => {
	return (
		<div className={cn(styles.modal__wrapper, styles.modal__confirm)}>
			<div className={styles.modal__header}>
				<h3 className="aidigi__heading">{data?.heading}</h3>
			</div>
			<div className={styles.modal__body}>
				<span>{data?.message}</span>
			</div>
			<div className={styles.modal__footer}>
				<Button className={cn('aidigi__button', 'outline')} onClick={onClose}>
					Cancel
				</Button>
				<Button className={cn('aidigi__button', 'red')} onClick={data?.onConfirm}>
					Delete
				</Button>
			</div>
		</div>
	)
}

export const Modal = () => {
	const { modalData, onCloseModal } = useModal()

	if (!modalData) return null

	let content: React.ReactNode
	switch (modalData.type) {
		case 'signin':
			content = <ModalSignin onClose={onCloseModal} data={modalData.data} />
			break
		case 'confirm':
			content = <ModalConfirm onClose={onCloseModal} data={modalData.data} />
			break
		default:
			return null
	}

	return (
		<div className={styles.wrapper}>
			<div className={styles.modal}>
				<Button className={styles.close} onClick={onCloseModal}>
					<Icon variant="x" />
				</Button>
				{content}
			</div>
		</div>
	)
}
