{"name": "@collective/core", "version": "1.0.0", "private": true, "sideEffects": false, "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "export": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "yarn clean && tsc --build tsconfig.build.json && copyfiles -u 1 -s src/**/*.scss dist/", "build-storybook": "storybook build --output-dir build/storybook", "clean": "rimraf ./dist ./build ./tsconfig.tsbuildinfo ./tsconfig.build.tsbuildinfo ./node_modules/.cache", "dev": "microbundle watch --tsconfig ./tsconfig.build.json", "fix-all-files": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs . --fix", "lint": "eslint . --ext .ts,.tsx,.js,.jsx,.cjs,.mjs --cache --cache-location ../../.cache/eslint/ui-lib.eslintcache", "serve-storybook": "sirv build/storybook --cors --port 8888", "storybook": "storybook dev -p 6006", "typecheck": "tsc --project ./tsconfig.json --noEmit"}, "peerDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.2.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "devDependencies": {"@collective/eslint-config-bases": "workspace:^", "@collective/tsconfig": "workspace:^", "@types/copyfiles": "^2.4.4", "@types/node": "20.17.25", "@types/react": "18.3.19", "@types/react-dom": "18.3.1", "copyfiles": "2.4.1", "cross-env": "7.0.3", "eslint": "8.57.1", "microbundle": "0.15.1", "next": "14.2.26", "npm-run-all2": "6.2.6", "prettier": "3.5.3", "react": "18.3.1", "react-dom": "18.3.1", "rimraf": "6.0.1", "sirv": "3.0.0", "sirv-cli": "3.0.0", "storybook": "8.3.5", "typescript": "5.8.2"}, "dependencies": {"classnames": "^2.5.1", "dayjs": "^1.11.13", "fast-blurhash": "^1.1.4", "gsap": "^3.12.5", "idb-keyval": "6.2.1", "sass": "^1.86.0"}}