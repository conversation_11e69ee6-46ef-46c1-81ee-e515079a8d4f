@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	.search__wrapper {
		display: grid;
		gap: px-to(10px, rem);
		h3 {
			font-weight: 400;
		}
	}

	.search__bar {
		--input-radius: 9999px;
		--input-padding-x: #{spacing(s8)};
		--input-padding-y: #{spacing(s2)};
		--input-height: #{px-to(64px, rem)};
		--input-border-color: #dddde3;
		--icon-gap: #{px-to(10px, rem)};
		margin-bottom: spacing(s5);

		svg {
			color: #808089;
			--size: #{spacing(s5)};
		}

		input {
			font-size: spacing(s5);
			font-weight: 400;

			&:focus {
				--input-border-color: #{color('black', 50)};
			}
		}
	}

	.nav__wrapper {
		display: grid;
		gap: spacing(s5);

		.row__nav {
			display: grid;
			gap: spacing(s5);

			@include min-width('md') {
				grid-template-columns: repeat(calc(var(--count)), 1fr);
			}
		}

		.col__nav {
			background-color: color('white', 100);
			padding: px-to(40px, rem);
			border-radius: px-to(25px, rem);
			min-height: px-to(240px, rem);
			display: grid;
			gap: px-to(50px, rem);
			align-content: flex-start;

			h3 {
				font-weight: 400;
			}
		}
	}

	.nav__list {
		line-height: 180%; /* 36px */
		text-decoration-line: underline;
		text-decoration-style: solid;
		text-decoration-skip-ink: none;
		text-decoration-thickness: auto;
		text-underline-offset: auto;
		text-underline-position: from-font;
		color: #686868;
		gap: spacing(s12);
	}
}
