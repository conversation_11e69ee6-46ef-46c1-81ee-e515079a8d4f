import { checkIsLogin } from '@collective/core'
import { createNewChat } from '@collective/integration-lib/cms'
import { cookies } from 'next/headers'

// export const runtime = 'edge'

export async function POST(request: Request) {
	const cookieStore = cookies()
	const authHeaeder = cookieStore.get('token')?.value

	if (!authHeaeder) {
		return Response.json({ error: 'Invalid token' }, { status: 401 })
	}
	const payload = await checkIsLogin(authHeaeder)
	if (payload === null) {
		return Response.json({ error: 'Token not valid' }, { status: 403 })
	}
	try {
		const response = await createNewChat(payload.id)
		return Response.json({
			status: 200,
			uuid: response.data.uuid,
		})
	} catch (error) {
		return Response.json({ error: 'Internal Server Error' }, { status: 500 })
	}
}
