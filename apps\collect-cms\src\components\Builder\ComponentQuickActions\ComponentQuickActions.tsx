import { Icon } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import { useContext, useState, useRef, useEffect } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import styles from './componentquickactions.module.scss'

interface ComponentQuickActionsProps {
	index?: number
	id?: number
	isVisible?: boolean
}

export const ComponentQuickActions = ({ index, isVisible = false }: ComponentQuickActionsProps) => {
	const context = useContext(PageBuilderContext)
	const { data, setData, normalizedData } = context
	const [showMoreMenu, setShowMoreMenu] = useState(false)
	const moreMenuRef = useRef<HTMLDivElement>(null)

	// <PERSON>le click outside to close more menu
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
				setShowMoreMenu(false)
			}
		}

		document.addEventListener('mousedown', handleClickOutside)
		return () => {
			document.removeEventListener('mousedown', handleClickOutside)
		}
	}, [])

	// Move component up
	const handleMoveUp = () => {
		if (index === undefined || index <= 0) return

		const components = [...normalizedData.components]
		const temp = components[index] as IComponentProps
		components[index] = components[index - 1] as IComponentProps
		components[index - 1] = temp

		setData({
			...data,
			data: {
				...data.data,
				components,
			},
		})
	}

	// Move component down
	const handleMoveDown = () => {
		if (index === undefined || index >= normalizedData.components.length - 1) return

		const components = [...normalizedData.components]
		const temp = components[index] as IComponentProps
		components[index] = components[index + 1] as IComponentProps
		components[index + 1] = temp

		setData({
			...data,
			data: {
				...data.data,
				components,
			},
		})
	}

	// Duplicate component
	const handleDuplicate = () => {
		if (index === undefined) return

		const components = [...normalizedData.components]
		const componentToDuplicate = { ...components[index] }

		// Create a new temp key for the duplicated component
		const maxKey = Math.max(
			...components.map((c) => (typeof c.__temp_key__ === 'number' ? c.__temp_key__ : 0))
		)
		componentToDuplicate.__temp_key__ = maxKey + 1

		// Insert the duplicated component after the original
		components.splice(index + 1, 0, componentToDuplicate)

		setData({
			...data,
			data: {
				...data.data,
				components,
			},
		})

		setShowMoreMenu(false)
	}

	// Remove component
	const handleRemove = () => {
		if (index === undefined) return

		const components = [...normalizedData.components]
		components.splice(index, 1)

		setData({
			...data,
			data: {
				...data.data,
				components,
			},
		})

		setShowMoreMenu(false)
	}

	// Toggle more menu
	const toggleMoreMenu = () => {
		setShowMoreMenu(!showMoreMenu)
	}

	return (
		<div className={`${styles.wrapper} ${isVisible ? styles.is__visible : ''}`}>
			<button className={styles.action} onClick={handleMoveUp} disabled={index === 0}>
				<Icon variant="chevron-up" type="cms" />
			</button>
			<div className={styles.more_container} ref={moreMenuRef}>
				<button className={styles.action} onClick={toggleMoreMenu}>
					<Icon variant="more" type="cms" />
				</button>
				{showMoreMenu && (
					<div className={styles.more_menu}>
						<button className={styles.menu_item} onClick={handleDuplicate}>
							<Icon variant="plus-circle" type="cms" />
							<span>Duplicate</span>
						</button>
						<button className={styles.menu_item} onClick={handleRemove}>
							<Icon variant="x" type="cms" />
							<span>Remove</span>
						</button>
					</div>
				)}
			</div>
			<button
				className={styles.action}
				onClick={handleMoveDown}
				disabled={index !== undefined && index >= normalizedData.components.length - 1}
			>
				<Icon variant="chevron-down" type="cms" />
			</button>
		</div>
	)
}
