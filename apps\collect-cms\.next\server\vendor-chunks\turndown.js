"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/turndown";
exports.ids = ["vendor-chunks/turndown"];
exports.modules = {

/***/ "(ssr)/../../node_modules/turndown/lib/turndown.es.js":
/*!******************************************************!*\
  !*** ../../node_modules/turndown/lib/turndown.es.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction extend (destination) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (source.hasOwnProperty(key)) destination[key] = source[key];\n    }\n  }\n  return destination\n}\n\nfunction repeat (character, count) {\n  return Array(count + 1).join(character)\n}\n\nfunction trimLeadingNewlines (string) {\n  return string.replace(/^\\n*/, '')\n}\n\nfunction trimTrailingNewlines (string) {\n  // avoid match-at-end regexp bottleneck, see #370\n  var indexEnd = string.length;\n  while (indexEnd > 0 && string[indexEnd - 1] === '\\n') indexEnd--;\n  return string.substring(0, indexEnd)\n}\n\nvar blockElements = [\n  'ADDRESS', 'ARTICLE', 'ASIDE', 'AUDIO', 'BLOCKQUOTE', 'BODY', 'CANVAS',\n  'CENTER', 'DD', 'DIR', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE',\n  'FOOTER', 'FORM', 'FRAMESET', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER',\n  'HGROUP', 'HR', 'HTML', 'ISINDEX', 'LI', 'MAIN', 'MENU', 'NAV', 'NOFRAMES',\n  'NOSCRIPT', 'OL', 'OUTPUT', 'P', 'PRE', 'SECTION', 'TABLE', 'TBODY', 'TD',\n  'TFOOT', 'TH', 'THEAD', 'TR', 'UL'\n];\n\nfunction isBlock (node) {\n  return is(node, blockElements)\n}\n\nvar voidElements = [\n  'AREA', 'BASE', 'BR', 'COL', 'COMMAND', 'EMBED', 'HR', 'IMG', 'INPUT',\n  'KEYGEN', 'LINK', 'META', 'PARAM', 'SOURCE', 'TRACK', 'WBR'\n];\n\nfunction isVoid (node) {\n  return is(node, voidElements)\n}\n\nfunction hasVoid (node) {\n  return has(node, voidElements)\n}\n\nvar meaningfulWhenBlankElements = [\n  'A', 'TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TH', 'TD', 'IFRAME', 'SCRIPT',\n  'AUDIO', 'VIDEO'\n];\n\nfunction isMeaningfulWhenBlank (node) {\n  return is(node, meaningfulWhenBlankElements)\n}\n\nfunction hasMeaningfulWhenBlank (node) {\n  return has(node, meaningfulWhenBlankElements)\n}\n\nfunction is (node, tagNames) {\n  return tagNames.indexOf(node.nodeName) >= 0\n}\n\nfunction has (node, tagNames) {\n  return (\n    node.getElementsByTagName &&\n    tagNames.some(function (tagName) {\n      return node.getElementsByTagName(tagName).length\n    })\n  )\n}\n\nvar rules = {};\n\nrules.paragraph = {\n  filter: 'p',\n\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.lineBreak = {\n  filter: 'br',\n\n  replacement: function (content, node, options) {\n    return options.br + '\\n'\n  }\n};\n\nrules.heading = {\n  filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n  replacement: function (content, node, options) {\n    var hLevel = Number(node.nodeName.charAt(1));\n\n    if (options.headingStyle === 'setext' && hLevel < 3) {\n      var underline = repeat((hLevel === 1 ? '=' : '-'), content.length);\n      return (\n        '\\n\\n' + content + '\\n' + underline + '\\n\\n'\n      )\n    } else {\n      return '\\n\\n' + repeat('#', hLevel) + ' ' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.blockquote = {\n  filter: 'blockquote',\n\n  replacement: function (content) {\n    content = content.replace(/^\\n+|\\n+$/g, '');\n    content = content.replace(/^/gm, '> ');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.list = {\n  filter: ['ul', 'ol'],\n\n  replacement: function (content, node) {\n    var parent = node.parentNode;\n    if (parent.nodeName === 'LI' && parent.lastElementChild === node) {\n      return '\\n' + content\n    } else {\n      return '\\n\\n' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.listItem = {\n  filter: 'li',\n\n  replacement: function (content, node, options) {\n    content = content\n      .replace(/^\\n+/, '') // remove leading newlines\n      .replace(/\\n+$/, '\\n') // replace trailing newlines with just a single one\n      .replace(/\\n/gm, '\\n    '); // indent\n    var prefix = options.bulletListMarker + '   ';\n    var parent = node.parentNode;\n    if (parent.nodeName === 'OL') {\n      var start = parent.getAttribute('start');\n      var index = Array.prototype.indexOf.call(parent.children, node);\n      prefix = (start ? Number(start) + index : index + 1) + '.  ';\n    }\n    return (\n      prefix + content + (node.nextSibling && !/\\n$/.test(content) ? '\\n' : '')\n    )\n  }\n};\n\nrules.indentedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'indented' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    return (\n      '\\n\\n    ' +\n      node.firstChild.textContent.replace(/\\n/g, '\\n    ') +\n      '\\n\\n'\n    )\n  }\n};\n\nrules.fencedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'fenced' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var className = node.firstChild.getAttribute('class') || '';\n    var language = (className.match(/language-(\\S+)/) || [null, ''])[1];\n    var code = node.firstChild.textContent;\n\n    var fenceChar = options.fence.charAt(0);\n    var fenceSize = 3;\n    var fenceInCodeRegex = new RegExp('^' + fenceChar + '{3,}', 'gm');\n\n    var match;\n    while ((match = fenceInCodeRegex.exec(code))) {\n      if (match[0].length >= fenceSize) {\n        fenceSize = match[0].length + 1;\n      }\n    }\n\n    var fence = repeat(fenceChar, fenceSize);\n\n    return (\n      '\\n\\n' + fence + language + '\\n' +\n      code.replace(/\\n$/, '') +\n      '\\n' + fence + '\\n\\n'\n    )\n  }\n};\n\nrules.horizontalRule = {\n  filter: 'hr',\n\n  replacement: function (content, node, options) {\n    return '\\n\\n' + options.hr + '\\n\\n'\n  }\n};\n\nrules.inlineLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'inlined' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node) {\n    var href = node.getAttribute('href');\n    if (href) href = href.replace(/([()])/g, '\\\\$1');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title.replace(/\"/g, '\\\\\"') + '\"';\n    return '[' + content + '](' + href + title + ')'\n  }\n};\n\nrules.referenceLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'referenced' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var href = node.getAttribute('href');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title + '\"';\n    var replacement;\n    var reference;\n\n    switch (options.linkReferenceStyle) {\n      case 'collapsed':\n        replacement = '[' + content + '][]';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      case 'shortcut':\n        replacement = '[' + content + ']';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      default:\n        var id = this.references.length + 1;\n        replacement = '[' + content + '][' + id + ']';\n        reference = '[' + id + ']: ' + href + title;\n    }\n\n    this.references.push(reference);\n    return replacement\n  },\n\n  references: [],\n\n  append: function (options) {\n    var references = '';\n    if (this.references.length) {\n      references = '\\n\\n' + this.references.join('\\n') + '\\n\\n';\n      this.references = []; // Reset references\n    }\n    return references\n  }\n};\n\nrules.emphasis = {\n  filter: ['em', 'i'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.emDelimiter + content + options.emDelimiter\n  }\n};\n\nrules.strong = {\n  filter: ['strong', 'b'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.strongDelimiter + content + options.strongDelimiter\n  }\n};\n\nrules.code = {\n  filter: function (node) {\n    var hasSiblings = node.previousSibling || node.nextSibling;\n    var isCodeBlock = node.parentNode.nodeName === 'PRE' && !hasSiblings;\n\n    return node.nodeName === 'CODE' && !isCodeBlock\n  },\n\n  replacement: function (content) {\n    if (!content) return ''\n    content = content.replace(/\\r?\\n|\\r/g, ' ');\n\n    var extraSpace = /^`|^ .*?[^ ].* $|`$/.test(content) ? ' ' : '';\n    var delimiter = '`';\n    var matches = content.match(/`+/gm) || [];\n    while (matches.indexOf(delimiter) !== -1) delimiter = delimiter + '`';\n\n    return delimiter + extraSpace + content + extraSpace + delimiter\n  }\n};\n\nrules.image = {\n  filter: 'img',\n\n  replacement: function (content, node) {\n    var alt = cleanAttribute(node.getAttribute('alt'));\n    var src = node.getAttribute('src') || '';\n    var title = cleanAttribute(node.getAttribute('title'));\n    var titlePart = title ? ' \"' + title + '\"' : '';\n    return src ? '![' + alt + ']' + '(' + src + titlePart + ')' : ''\n  }\n};\n\nfunction cleanAttribute (attribute) {\n  return attribute ? attribute.replace(/(\\n+\\s*)+/g, '\\n') : ''\n}\n\n/**\n * Manages a collection of rules used to convert HTML to Markdown\n */\n\nfunction Rules (options) {\n  this.options = options;\n  this._keep = [];\n  this._remove = [];\n\n  this.blankRule = {\n    replacement: options.blankReplacement\n  };\n\n  this.keepReplacement = options.keepReplacement;\n\n  this.defaultRule = {\n    replacement: options.defaultReplacement\n  };\n\n  this.array = [];\n  for (var key in options.rules) this.array.push(options.rules[key]);\n}\n\nRules.prototype = {\n  add: function (key, rule) {\n    this.array.unshift(rule);\n  },\n\n  keep: function (filter) {\n    this._keep.unshift({\n      filter: filter,\n      replacement: this.keepReplacement\n    });\n  },\n\n  remove: function (filter) {\n    this._remove.unshift({\n      filter: filter,\n      replacement: function () {\n        return ''\n      }\n    });\n  },\n\n  forNode: function (node) {\n    if (node.isBlank) return this.blankRule\n    var rule;\n\n    if ((rule = findRule(this.array, node, this.options))) return rule\n    if ((rule = findRule(this._keep, node, this.options))) return rule\n    if ((rule = findRule(this._remove, node, this.options))) return rule\n\n    return this.defaultRule\n  },\n\n  forEach: function (fn) {\n    for (var i = 0; i < this.array.length; i++) fn(this.array[i], i);\n  }\n};\n\nfunction findRule (rules, node, options) {\n  for (var i = 0; i < rules.length; i++) {\n    var rule = rules[i];\n    if (filterValue(rule, node, options)) return rule\n  }\n  return void 0\n}\n\nfunction filterValue (rule, node, options) {\n  var filter = rule.filter;\n  if (typeof filter === 'string') {\n    if (filter === node.nodeName.toLowerCase()) return true\n  } else if (Array.isArray(filter)) {\n    if (filter.indexOf(node.nodeName.toLowerCase()) > -1) return true\n  } else if (typeof filter === 'function') {\n    if (filter.call(rule, node, options)) return true\n  } else {\n    throw new TypeError('`filter` needs to be a string, array, or function')\n  }\n}\n\n/**\n * The collapseWhitespace function is adapted from collapse-whitespace\n * by Luc Thevenard.\n *\n * The MIT License (MIT)\n *\n * Copyright (c) 2014 Luc Thevenard <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * collapseWhitespace(options) removes extraneous whitespace from an the given element.\n *\n * @param {Object} options\n */\nfunction collapseWhitespace (options) {\n  var element = options.element;\n  var isBlock = options.isBlock;\n  var isVoid = options.isVoid;\n  var isPre = options.isPre || function (node) {\n    return node.nodeName === 'PRE'\n  };\n\n  if (!element.firstChild || isPre(element)) return\n\n  var prevText = null;\n  var keepLeadingWs = false;\n\n  var prev = null;\n  var node = next(prev, element, isPre);\n\n  while (node !== element) {\n    if (node.nodeType === 3 || node.nodeType === 4) { // Node.TEXT_NODE or Node.CDATA_SECTION_NODE\n      var text = node.data.replace(/[ \\r\\n\\t]+/g, ' ');\n\n      if ((!prevText || / $/.test(prevText.data)) &&\n          !keepLeadingWs && text[0] === ' ') {\n        text = text.substr(1);\n      }\n\n      // `text` might be empty at this point.\n      if (!text) {\n        node = remove(node);\n        continue\n      }\n\n      node.data = text;\n\n      prevText = node;\n    } else if (node.nodeType === 1) { // Node.ELEMENT_NODE\n      if (isBlock(node) || node.nodeName === 'BR') {\n        if (prevText) {\n          prevText.data = prevText.data.replace(/ $/, '');\n        }\n\n        prevText = null;\n        keepLeadingWs = false;\n      } else if (isVoid(node) || isPre(node)) {\n        // Avoid trimming space around non-block, non-BR void elements and inline PRE.\n        prevText = null;\n        keepLeadingWs = true;\n      } else if (prevText) {\n        // Drop protection if set previously.\n        keepLeadingWs = false;\n      }\n    } else {\n      node = remove(node);\n      continue\n    }\n\n    var nextNode = next(prev, node, isPre);\n    prev = node;\n    node = nextNode;\n  }\n\n  if (prevText) {\n    prevText.data = prevText.data.replace(/ $/, '');\n    if (!prevText.data) {\n      remove(prevText);\n    }\n  }\n}\n\n/**\n * remove(node) removes the given node from the DOM and returns the\n * next node in the sequence.\n *\n * @param {Node} node\n * @return {Node} node\n */\nfunction remove (node) {\n  var next = node.nextSibling || node.parentNode;\n\n  node.parentNode.removeChild(node);\n\n  return next\n}\n\n/**\n * next(prev, current, isPre) returns the next node in the sequence, given the\n * current and previous nodes.\n *\n * @param {Node} prev\n * @param {Node} current\n * @param {Function} isPre\n * @return {Node}\n */\nfunction next (prev, current, isPre) {\n  if ((prev && prev.parentNode === current) || isPre(current)) {\n    return current.nextSibling || current.parentNode\n  }\n\n  return current.firstChild || current.nextSibling || current.parentNode\n}\n\n/*\n * Set up window for Node.js\n */\n\nvar root = (typeof window !== 'undefined' ? window : {});\n\n/*\n * Parsing HTML strings\n */\n\nfunction canParseHTMLNatively () {\n  var Parser = root.DOMParser;\n  var canParse = false;\n\n  // Adapted from https://gist.github.com/1129031\n  // Firefox/Opera/IE throw errors on unsupported types\n  try {\n    // WebKit returns null on unsupported types\n    if (new Parser().parseFromString('', 'text/html')) {\n      canParse = true;\n    }\n  } catch (e) {}\n\n  return canParse\n}\n\nfunction createHTMLParser () {\n  var Parser = function () {};\n\n  {\n    var domino = __webpack_require__(/*! @mixmark-io/domino */ \"(ssr)/../../node_modules/@mixmark-io/domino/lib/index.js\");\n    Parser.prototype.parseFromString = function (string) {\n      return domino.createDocument(string)\n    };\n  }\n  return Parser\n}\n\nvar HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();\n\nfunction RootNode (input, options) {\n  var root;\n  if (typeof input === 'string') {\n    var doc = htmlParser().parseFromString(\n      // DOM parsers arrange elements in the <head> and <body>.\n      // Wrapping in a custom element ensures elements are reliably arranged in\n      // a single element.\n      '<x-turndown id=\"turndown-root\">' + input + '</x-turndown>',\n      'text/html'\n    );\n    root = doc.getElementById('turndown-root');\n  } else {\n    root = input.cloneNode(true);\n  }\n  collapseWhitespace({\n    element: root,\n    isBlock: isBlock,\n    isVoid: isVoid,\n    isPre: options.preformattedCode ? isPreOrCode : null\n  });\n\n  return root\n}\n\nvar _htmlParser;\nfunction htmlParser () {\n  _htmlParser = _htmlParser || new HTMLParser();\n  return _htmlParser\n}\n\nfunction isPreOrCode (node) {\n  return node.nodeName === 'PRE' || node.nodeName === 'CODE'\n}\n\nfunction Node (node, options) {\n  node.isBlock = isBlock(node);\n  node.isCode = node.nodeName === 'CODE' || node.parentNode.isCode;\n  node.isBlank = isBlank(node);\n  node.flankingWhitespace = flankingWhitespace(node, options);\n  return node\n}\n\nfunction isBlank (node) {\n  return (\n    !isVoid(node) &&\n    !isMeaningfulWhenBlank(node) &&\n    /^\\s*$/i.test(node.textContent) &&\n    !hasVoid(node) &&\n    !hasMeaningfulWhenBlank(node)\n  )\n}\n\nfunction flankingWhitespace (node, options) {\n  if (node.isBlock || (options.preformattedCode && node.isCode)) {\n    return { leading: '', trailing: '' }\n  }\n\n  var edges = edgeWhitespace(node.textContent);\n\n  // abandon leading ASCII WS if left-flanked by ASCII WS\n  if (edges.leadingAscii && isFlankedByWhitespace('left', node, options)) {\n    edges.leading = edges.leadingNonAscii;\n  }\n\n  // abandon trailing ASCII WS if right-flanked by ASCII WS\n  if (edges.trailingAscii && isFlankedByWhitespace('right', node, options)) {\n    edges.trailing = edges.trailingNonAscii;\n  }\n\n  return { leading: edges.leading, trailing: edges.trailing }\n}\n\nfunction edgeWhitespace (string) {\n  var m = string.match(/^(([ \\t\\r\\n]*)(\\s*))(?:(?=\\S)[\\s\\S]*\\S)?((\\s*?)([ \\t\\r\\n]*))$/);\n  return {\n    leading: m[1], // whole string for whitespace-only strings\n    leadingAscii: m[2],\n    leadingNonAscii: m[3],\n    trailing: m[4], // empty for whitespace-only strings\n    trailingNonAscii: m[5],\n    trailingAscii: m[6]\n  }\n}\n\nfunction isFlankedByWhitespace (side, node, options) {\n  var sibling;\n  var regExp;\n  var isFlanked;\n\n  if (side === 'left') {\n    sibling = node.previousSibling;\n    regExp = / $/;\n  } else {\n    sibling = node.nextSibling;\n    regExp = /^ /;\n  }\n\n  if (sibling) {\n    if (sibling.nodeType === 3) {\n      isFlanked = regExp.test(sibling.nodeValue);\n    } else if (options.preformattedCode && sibling.nodeName === 'CODE') {\n      isFlanked = false;\n    } else if (sibling.nodeType === 1 && !isBlock(sibling)) {\n      isFlanked = regExp.test(sibling.textContent);\n    }\n  }\n  return isFlanked\n}\n\nvar reduce = Array.prototype.reduce;\nvar escapes = [\n  [/\\\\/g, '\\\\\\\\'],\n  [/\\*/g, '\\\\*'],\n  [/^-/g, '\\\\-'],\n  [/^\\+ /g, '\\\\+ '],\n  [/^(=+)/g, '\\\\$1'],\n  [/^(#{1,6}) /g, '\\\\$1 '],\n  [/`/g, '\\\\`'],\n  [/^~~~/g, '\\\\~~~'],\n  [/\\[/g, '\\\\['],\n  [/\\]/g, '\\\\]'],\n  [/^>/g, '\\\\>'],\n  [/_/g, '\\\\_'],\n  [/^(\\d+)\\. /g, '$1\\\\. ']\n];\n\nfunction TurndownService (options) {\n  if (!(this instanceof TurndownService)) return new TurndownService(options)\n\n  var defaults = {\n    rules: rules,\n    headingStyle: 'setext',\n    hr: '* * *',\n    bulletListMarker: '*',\n    codeBlockStyle: 'indented',\n    fence: '```',\n    emDelimiter: '_',\n    strongDelimiter: '**',\n    linkStyle: 'inlined',\n    linkReferenceStyle: 'full',\n    br: '  ',\n    preformattedCode: false,\n    blankReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' : ''\n    },\n    keepReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + node.outerHTML + '\\n\\n' : node.outerHTML\n    },\n    defaultReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + content + '\\n\\n' : content\n    }\n  };\n  this.options = extend({}, defaults, options);\n  this.rules = new Rules(this.options);\n}\n\nTurndownService.prototype = {\n  /**\n   * The entry point for converting a string or DOM node to Markdown\n   * @public\n   * @param {String|HTMLElement} input The string or DOM node to convert\n   * @returns A Markdown representation of the input\n   * @type String\n   */\n\n  turndown: function (input) {\n    if (!canConvert(input)) {\n      throw new TypeError(\n        input + ' is not a string, or an element/document/fragment node.'\n      )\n    }\n\n    if (input === '') return ''\n\n    var output = process.call(this, new RootNode(input, this.options));\n    return postProcess.call(this, output)\n  },\n\n  /**\n   * Add one or more plugins\n   * @public\n   * @param {Function|Array} plugin The plugin or array of plugins to add\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  use: function (plugin) {\n    if (Array.isArray(plugin)) {\n      for (var i = 0; i < plugin.length; i++) this.use(plugin[i]);\n    } else if (typeof plugin === 'function') {\n      plugin(this);\n    } else {\n      throw new TypeError('plugin must be a Function or an Array of Functions')\n    }\n    return this\n  },\n\n  /**\n   * Adds a rule\n   * @public\n   * @param {String} key The unique key of the rule\n   * @param {Object} rule The rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  addRule: function (key, rule) {\n    this.rules.add(key, rule);\n    return this\n  },\n\n  /**\n   * Keep a node (as HTML) that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  keep: function (filter) {\n    this.rules.keep(filter);\n    return this\n  },\n\n  /**\n   * Remove a node that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  remove: function (filter) {\n    this.rules.remove(filter);\n    return this\n  },\n\n  /**\n   * Escapes Markdown syntax\n   * @public\n   * @param {String} string The string to escape\n   * @returns A string with Markdown syntax escaped\n   * @type String\n   */\n\n  escape: function (string) {\n    return escapes.reduce(function (accumulator, escape) {\n      return accumulator.replace(escape[0], escape[1])\n    }, string)\n  }\n};\n\n/**\n * Reduces a DOM node down to its Markdown string equivalent\n * @private\n * @param {HTMLElement} parentNode The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction process (parentNode) {\n  var self = this;\n  return reduce.call(parentNode.childNodes, function (output, node) {\n    node = new Node(node, self.options);\n\n    var replacement = '';\n    if (node.nodeType === 3) {\n      replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);\n    } else if (node.nodeType === 1) {\n      replacement = replacementForNode.call(self, node);\n    }\n\n    return join(output, replacement)\n  }, '')\n}\n\n/**\n * Appends strings as each rule requires and trims the output\n * @private\n * @param {String} output The conversion output\n * @returns A trimmed version of the ouput\n * @type String\n */\n\nfunction postProcess (output) {\n  var self = this;\n  this.rules.forEach(function (rule) {\n    if (typeof rule.append === 'function') {\n      output = join(output, rule.append(self.options));\n    }\n  });\n\n  return output.replace(/^[\\t\\r\\n]+/, '').replace(/[\\t\\r\\n\\s]+$/, '')\n}\n\n/**\n * Converts an element node to its Markdown equivalent\n * @private\n * @param {HTMLElement} node The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction replacementForNode (node) {\n  var rule = this.rules.forNode(node);\n  var content = process.call(this, node);\n  var whitespace = node.flankingWhitespace;\n  if (whitespace.leading || whitespace.trailing) content = content.trim();\n  return (\n    whitespace.leading +\n    rule.replacement(content, node, this.options) +\n    whitespace.trailing\n  )\n}\n\n/**\n * Joins replacement to the current output with appropriate number of new lines\n * @private\n * @param {String} output The current conversion output\n * @param {String} replacement The string to append to the output\n * @returns Joined output\n * @type String\n */\n\nfunction join (output, replacement) {\n  var s1 = trimTrailingNewlines(output);\n  var s2 = trimLeadingNewlines(replacement);\n  var nls = Math.max(output.length - s1.length, replacement.length - s2.length);\n  var separator = '\\n\\n'.substring(0, nls);\n\n  return s1 + separator + s2\n}\n\n/**\n * Determines whether an input can be converted\n * @private\n * @param {String|HTMLElement} input Describe this parameter\n * @returns Describe what it returns\n * @type String|Object|Array|Boolean|Number\n */\n\nfunction canConvert (input) {\n  return (\n    input != null && (\n      typeof input === 'string' ||\n      (input.nodeType && (\n        input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11\n      ))\n    )\n  )\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TurndownService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/turndown/lib/turndown.es.js\n");

/***/ })

};
;