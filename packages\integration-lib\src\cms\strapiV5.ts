/* eslint-disable @typescript-eslint/naming-convention */
const cfClientID = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID
const cfClientSecret = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET
const cloudflareHeader = {
	// eslint-disable-next-line @typescript-eslint/naming-convention
	'CF-Access-Client-Id': `${cfClientID}`,
	// eslint-disable-next-line @typescript-eslint/naming-convention
	'CF-Access-Client-Secret': `${cfClientSecret}`,
}

enum MediaFormat {
	THUMBNAIL = 'thumbnail',
}

export type StrapiUserProps = {
	id: number
	username: string
	fullName: string
	email: string
	provider: string
	confirmed: boolean
	blocked: boolean
	avatar?: IMediaProps
}

export type StrapiLoginUserProps = {
	jwt: string
	user: StrapiUserProps
}

type IMediaBaseSingleProps = {
	id?: number
	alternativeText?: string | null
	blurhash?: string | null
	height?: number
	name: string
	width?: number
	url: string
	hash?: string
	ext?: string
	mime?: string
	size?: number
	updatedAt?: string
	caption?: string
	formats?: {
		[key in MediaFormat]?: {
			name: string
			hash?: string
			ext?: string
			width?: number
			height?: number
			url: string
			blurhash?: string | null
		}
	}
}

type IMediaBaseProps<Type extends 'single' | 'multiple'> = Type extends 'multiple'
	? IMediaBaseSingleProps[]
	: IMediaBaseSingleProps

export type IMediaProps<Type extends 'single' | 'multiple' = 'single'> = IMediaBaseProps<Type>

export type IBaseCMSPageProps<PageProps = Record<string, unknown>> = {
	id: number
	documentId: string
} & PageProps

export type ICMSPageProps<
	Type extends 'single' | 'multiple',
	PageProps = Record<string, unknown>,
> = Type extends 'multiple'
	? { data: IBaseCMSPageProps<PageProps>[] }
	: { data: IBaseCMSPageProps<PageProps> }

export type IComponentProps = {
	id?: string | number
	__component?: string
	__temp_key__?: string | number
	enable?: boolean
	[key: string]: unknown
}

export const getCmsData = async <PageProps, Type extends 'single' | 'multiple' = 'single'>({
	path,
	deep = 4,
	locale = 'en',
	draft = false,
	filter,
	revalidate = 120,
}: {
	path: string
	deep?: number
	locale?: string
	draft?: boolean
	filter?: string
	revalidate?: number
}): Promise<ICMSPageProps<Type, PageProps>> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}?pLevel=${deep}&locale=${locale}${draft ? '&publicationState=preview' : ''}${filter ? `&${filter}` : ''}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
				...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
			},
			next: { revalidate },
		}
	)
	if (!response.ok) {
		const errorData = await response.text()
		console.info(path, errorData)
		return Promise.reject(new Error(`Failed to fetch ${path} with error ${errorData}`))
	}

	return response.json()
}

// Navigation
type IUINavigationProps = {
	id: number
	title: string
	menuAttached: boolean
	path: string
	type: 'INTERNAL' | 'EXTERNAL' | 'WRAPPER'
	items: IUINavigationProps[]
}

export type INavigationProps = {
	label: string
	path: string
	isLocked: boolean
	children?: INavigationProps[]
}

export const getNavigationData = async (
	name: string = '1',
	type: string = 'TREE'
): Promise<INavigationProps[]> => {
	const response = await fetch(
		`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/navigation/render/${name}?type=${type}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
				...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
			},
			next: { revalidate: 120 },
		}
	)
	if (!response.ok) {
		return Promise.reject(new Error('Failed to fetch'))
	}
	const data = ((await response.json()) as IUINavigationProps[]).filter((item) => item.menuAttached)
	const processedData = data.map((item) => {
		return {
			label: item.title,
			path: item.path,
			isLocked: false,
			children:
				item.items?.map((child: { title: string; path: string }) => {
					return {
						label: child.title,
						path: child.path,
						isLocked: false,
					}
				}) || [],
		}
	})
	return processedData
}

/**
 * Post form data to Strapi CMS
 * @param param0
 * @returns
 */
export const postFormCmsData = async ({ path, body }: { path: string; body: FormData }) => {
	const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`, {
		method: 'POST',
		headers: {
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_POST_API_KEY}`,
			...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
		},
		body,
	})
	if (!response.ok) {
		return Promise.reject(new Error(`Failed to post ${path} with error ${await response.text()}`))
	}
	return response.json()
}

/**
 * Post Json data to Strapi CMS
 * @param param0
 * @returns
 */
export const postJsonFormData = async <PageProps>({
	fullPath,
	body,
}: {
	fullPath: string
	body: string
}): Promise<PageProps> => {
	const cfClientID = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_ID
	const cfClientSecret = process.env.NEXT_PUBLIC_CLOUDFLARE_CLIENT_SECRET

	const response = await fetch(fullPath, {
		method: 'POST',
		headers: {
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_POST_API_KEY}`,
			// eslint-disable-next-line @typescript-eslint/naming-convention
			'Content-Type': 'application/json',
			...(cfClientSecret && cfClientID ? cloudflareHeader : {}),
		},
		body,
	})
	if (!response.ok) {
		return Promise.reject(
			new Error(`Failed to post ${fullPath} with error ${await response.text()}`)
		)
	}
	return response.json()
}

/**
 * Post Json Custom data to Strapi CMS
 * @param path
 * @param raw
 * @returns
 */
export const postJsonCustomPathData = async <Props>(
	path: string,
	raw: string | FormData
): Promise<Props> => {
	const response = await fetch(path, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
		body: raw,
	})
	if (!response.ok) {
		return Promise.reject(await response.text())
	}
	return response.json()
}

/**
 * Post Json data to Strapi CMS
 * @param path
 * @param raw
 * @returns
 */
export const postJsonData = async <Props>(path: string, raw: string | FormData): Promise<Props> => {
	return postJsonCustomPathData(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/${path}`, raw)
}

/**
 *
 * @returns
 */
export const createNewChat = async (userID: number) => {
	const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/chats`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
		body: JSON.stringify({
			data: {
				user: userID,
				Message: [],
				uuid: '',
			},
		}),
	})

	if (!response.ok) {
		return Promise.reject(new Error(`Failed to createChat with error ${await response.text()}`))
	}
	return response.json()
}

/**
 *
 * @param chatID
 * @returns
 */
export const deleteChat = async (chatID: string) => {
	const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_HOST}/api/chats/${chatID}`, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
	})
	if (!response.ok) {
		return Promise.reject(new Error(`Failed to deleteChat with error ${await response.text()}`))
	}
	return response
}

export const deleteCustomPath = async <Props>(path: string): Promise<Props> => {
	const response = await fetch(path, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
	})
	if (!response.ok) {
		return Promise.reject(await response.text())
	}
	return response.json()
}
/**
 * Get data from Strapi CMS
 * @param path
 * @returns
 */
export const getData = async (
	path: string,
	deep: number = 4,
	locale: string = 'en',
	revalidate: number = 120,
	filter?: string
) => {
	const response = await fetch(
		`${
			process.env.NEXT_PUBLIC_STRAPI_HOST
		}/api/${path}?pLevel=${deep}&locale=${locale}${filter ? `&${filter}` : ''}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
			},
			next: { revalidate },
		}
	)
	if (!response.ok) {
		return null
	}
	return response.json()
}

/**
 * Get data from Strapi CMS
 * @param path
 * @returns
 */
export const getDataClient = async (
	path: string,
	strapiToken: string,
	deep: number = 4,
	locale: string = 'en',
	revalidate: number = 120,
	filter?: string
) => {
	const response = await fetch(
		`${
			process.env.NEXT_PUBLIC_STRAPI_HOST
		}/api/${path}?pLevel=${deep}&locale=${locale}${filter ? `&${filter}` : ''}`,
		{
			method: 'GET',
			headers: {
				authorization: `Bearer ${strapiToken || ''}`,
			},
			next: { revalidate },
		}
	)
	if (!response.ok) {
		return null
	}
	return response.json()
}

/**
 * Get data from Strapi CMS
 * @param path
 * @returns
 */
export const getCustomPathData = async (path: string) => {
	const response = await fetch(path, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_KEY}`,
		},
	})
	if (!response.ok) {
		return null
	}
	return response.json()
}

/**
 * Get data from Strapi CMS
 * @param path
 * @returns
 */
export const getCheckData = async (path: string, bearer: string) => {
	const response = await fetch(path, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${bearer}`,
		},
	})
	if (!response.ok) {
		return null
	}
	return response.json()
}
