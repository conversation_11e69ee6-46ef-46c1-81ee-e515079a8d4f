"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // We need to simulate the same logic as Component.tsx handleRemove\n        // The key insight is that we need to find which Component instance this entry belongs to\n        // and trigger its remove logic\n        if (idx === 0) {\n            // This is the root level - we need to trigger the Component's own handleRemove\n            // Since we can't directly call it, we'll use a special field name to signal removal\n            if (currentEntry.onChange) {\n                // Use a special field name to indicate this is a remove operation\n                // The Component's onChange should handle this appropriately\n                currentEntry.onChange({\n                    field: \"__REMOVE_ENTRY__\",\n                    value: true\n                });\n            }\n        } else {\n            // For nested levels, find the parent entry that contains this value\n            var parentEntry = null;\n            var targetIndex = -1;\n            // Search through previous entries to find the parent\n            for(var i = idx - 1; i >= 0; i--){\n                var entry = childCmp[i];\n                if (!entry) continue;\n                if (Array.isArray(entry.value)) {\n                    // Check if current value exists in this array\n                    targetIndex = entry.value.findIndex(function(item) {\n                        return item === currentEntry.value;\n                    });\n                    if (targetIndex !== -1) {\n                        parentEntry = entry;\n                        break;\n                    }\n                }\n            }\n            if (parentEntry && parentEntry.onChange && targetIndex !== -1) {\n                // Remove from parent array\n                var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n                parentValue.splice(targetIndex, 1);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        var currentValue = currentEntry.value;\n        if (idx === 0) {\n            // This is the root level - we need to trigger the Component's own handleDuplicate\n            if (currentEntry.onChange) {\n                // Use a special field name to indicate this is a duplicate operation\n                currentEntry.onChange({\n                    field: \"__DUPLICATE_ENTRY__\",\n                    value: currentValue\n                });\n            }\n        } else {\n            // For nested levels, find the parent entry that contains this value\n            var parentEntry = null;\n            var targetIndex = -1;\n            // Search through previous entries to find the parent\n            for(var i = idx - 1; i >= 0; i--){\n                var entry = childCmp[i];\n                if (!entry) continue;\n                if (Array.isArray(entry.value)) {\n                    // Check if current value exists in this array\n                    targetIndex = entry.value.findIndex(function(item) {\n                        return item === currentValue;\n                    });\n                    if (targetIndex !== -1) {\n                        parentEntry = entry;\n                        break;\n                    }\n                }\n            }\n            if (parentEntry && parentEntry.onChange && targetIndex !== -1) {\n                // Duplicate in parent array\n                var parentValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(parentEntry.value);\n                var duplicatedItem = JSON.parse(JSON.stringify(currentValue));\n                parentValue.splice(targetIndex + 1, 0, duplicatedItem);\n                parentEntry.onChange({\n                    field: \"value\",\n                    value: parentValue\n                });\n            }\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 166,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});