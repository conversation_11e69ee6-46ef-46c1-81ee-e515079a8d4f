/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_homepage_index_ts"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/heroscene.module.scss":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/HeroScene/heroscene.module.scss ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"heroscene_wrapper__U967n\",\"media\":\"heroscene_media__pjqsP\"};\n    if(true) {\n      // 1748273819760\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"e3ac764ba2b5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvSGVyb1NjZW5lL2hlcm9zY2VuZS5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQjtBQUNsQixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUosY0FBYyxzREFBc0Q7QUFDclAsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2hvbWVwYWdlL0hlcm9TY2VuZS9oZXJvc2NlbmUubW9kdWxlLnNjc3M/MmMyYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wid3JhcHBlclwiOlwiaGVyb3NjZW5lX3dyYXBwZXJfX1U5NjduXCIsXCJtZWRpYVwiOlwiaGVyb3NjZW5lX21lZGlhX19wanFzUFwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ4MjczODE5NzYwXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkQ6L0NEQS9yZXBvcy9icmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlL2FwcHMvY29sbGVjdC1jbXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gIFxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZTNhYzc2NGJhMmI1XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/heroscene.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/searchbar.module.scss":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/SearchBar/searchbar.module.scss ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"searchbar_wrapper__Xoeeq\",\"search__wrapper\":\"searchbar_search__wrapper__8dStd\",\"search__bar\":\"searchbar_search__bar__OPP_m\",\"nav__wrapper\":\"searchbar_nav__wrapper__WhL0L\",\"row__nav\":\"searchbar_row__nav__2_Ygr\",\"col__nav\":\"searchbar_col__nav__6Qzj_\",\"nav__list\":\"searchbar_nav__list__le1DN\"};\n    if(true) {\n      // 1748273819819\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"36f665b6ae87\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvU2VhcmNoQmFyL3NlYXJjaGJhci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQjtBQUNsQixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUosY0FBYyxzREFBc0Q7QUFDclAsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2hvbWVwYWdlL1NlYXJjaEJhci9zZWFyY2hiYXIubW9kdWxlLnNjc3M/YTAyZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wid3JhcHBlclwiOlwic2VhcmNoYmFyX3dyYXBwZXJfX1hvZWVxXCIsXCJzZWFyY2hfX3dyYXBwZXJcIjpcInNlYXJjaGJhcl9zZWFyY2hfX3dyYXBwZXJfXzhkU3RkXCIsXCJzZWFyY2hfX2JhclwiOlwic2VhcmNoYmFyX3NlYXJjaF9fYmFyX19PUFBfbVwiLFwibmF2X193cmFwcGVyXCI6XCJzZWFyY2hiYXJfbmF2X193cmFwcGVyX19XaEwwTFwiLFwicm93X19uYXZcIjpcInNlYXJjaGJhcl9yb3dfX25hdl9fMl9ZZ3JcIixcImNvbF9fbmF2XCI6XCJzZWFyY2hiYXJfY29sX19uYXZfXzZRempfXCIsXCJuYXZfX2xpc3RcIjpcInNlYXJjaGJhcl9uYXZfX2xpc3RfX2xlMUROXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDgyNzM4MTk4MTlcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovQ0RBL3JlcG9zL2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUvYXBwcy9jb2xsZWN0LWNtcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIzNmY2NjViNmFlODdcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/searchbar.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/HeroScene.tsx":
/*!***********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/HeroScene/HeroScene.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroScene: function() { return /* binding */ HeroScene; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Image_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Image!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _heroscene_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./heroscene.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/heroscene.module.scss\");\n/* harmony import */ var _heroscene_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_heroscene_module_scss__WEBPACK_IMPORTED_MODULE_2__);\nvar _this = undefined;\n\n\n\n\nvar HeroScene = function(param) {\n    var cid = param.cid, Media = param.Media;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_heroscene_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_collective_core__WEBPACK_IMPORTED_MODULE_3__.Image, {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_heroscene_module_scss__WEBPACK_IMPORTED_MODULE_2___default().media)),\n            media: Media,\n            alt: \"\"\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\HeroScene\\\\HeroScene.tsx\",\n            lineNumber: 13,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\HeroScene\\\\HeroScene.tsx\",\n        lineNumber: 12,\n        columnNumber: 2\n    }, _this);\n};\n_c = HeroScene;\nvar _c;\n$RefreshReg$(_c, \"HeroScene\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvSGVyb1NjZW5lL0hlcm9TY2VuZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXdDO0FBRWI7QUFDaUI7QUFPckMsSUFBTUcsWUFBWTtRQUFHQyxZQUFBQSxLQUFLQyxjQUFBQTt5QkFDaEMsOERBQUNDO1FBQVFDLElBQUlIO1FBQUtJLFdBQVdQLGlEQUFFQSxDQUFDQyx1RUFBYztrQkFDN0MsNEVBQUNGLCtFQUFLQTtZQUFDUSxXQUFXUCxpREFBRUEsQ0FBQ0MscUVBQVk7WUFBR1EsT0FBT0w7WUFBT00sS0FBSTs7Ozs7Ozs7Ozs7RUFFdkQ7S0FKWVIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9ob21lcGFnZS9IZXJvU2NlbmUvSGVyb1NjZW5lLnRzeD80MDA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEltYWdlIH0gZnJvbSAnQGNvbGxlY3RpdmUvY29yZSdcbmltcG9ydCB0eXBlIHsgSU1lZGlhUHJvcHMgfSBmcm9tICdAY29sbGVjdGl2ZS9pbnRlZ3JhdGlvbi1saWIvY21zJ1xuaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnXG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vaGVyb3NjZW5lLm1vZHVsZS5zY3NzJ1xuXG50eXBlIEhlcm9TY2VuZVByb3BzID0ge1xuXHRjaWQ/OiBzdHJpbmdcblx0TWVkaWE6IElNZWRpYVByb3BzXG59XG5cbmV4cG9ydCBjb25zdCBIZXJvU2NlbmUgPSAoeyBjaWQsIE1lZGlhIH06IEhlcm9TY2VuZVByb3BzKSA9PiAoXG5cdDxzZWN0aW9uIGlkPXtjaWR9IGNsYXNzTmFtZT17Y24oc3R5bGVzLndyYXBwZXIpfT5cblx0XHQ8SW1hZ2UgY2xhc3NOYW1lPXtjbihzdHlsZXMubWVkaWEpfSBtZWRpYT17TWVkaWF9IGFsdD1cIlwiIC8+XG5cdDwvc2VjdGlvbj5cbilcbiJdLCJuYW1lcyI6WyJJbWFnZSIsImNuIiwic3R5bGVzIiwiSGVyb1NjZW5lIiwiY2lkIiwiTWVkaWEiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJ3cmFwcGVyIiwibWVkaWEiLCJhbHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/HeroScene.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/index.ts":
/*!******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/HeroScene/index.ts ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroScene: function() { return /* reexport safe */ _HeroScene__WEBPACK_IMPORTED_MODULE_0__.HeroScene; }\n/* harmony export */ });\n/* harmony import */ var _HeroScene__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HeroScene */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/HeroScene.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvSGVyb1NjZW5lL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvSGVyb1NjZW5lL2luZGV4LnRzPzM3YTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9IZXJvU2NlbmUnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/SearchBar.tsx":
/*!***********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/SearchBar/SearchBar.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: function() { return /* binding */ SearchBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./searchbar.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/searchbar.module.scss\");\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__);\nvar _this = undefined;\n\n\n\n\nvar SearchBar = function(param) {\n    var cid = param.cid, Headline = param.Headline;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading--h3\"),\n                        children: Headline\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        className: (_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__bar),\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().icon)),\n                            variant: \"search-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 17\n                        }, void 0),\n                        placeholder: \"Logo usages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n                lineNumber: 13,\n                columnNumber: 4\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\homepage\\\\SearchBar\\\\SearchBar.tsx\",\n        lineNumber: 11,\n        columnNumber: 2\n    }, _this);\n};\n_c = SearchBar;\nvar _c;\n$RefreshReg$(_c, \"SearchBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvU2VhcmNoQmFyL1NlYXJjaEJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNuQjtBQUNpQjtBQU9yQyxJQUFNSSxZQUFZO1FBQUdDLFlBQUFBLEtBQUtDLGlCQUFBQTt5QkFDaEMsOERBQUNDO1FBQVFDLElBQUlIO1FBQUtJLFdBQVdQLGlEQUFFQSxDQUFDQyx1RUFBYztrQkFDN0MsNEVBQUNRO1lBQUlGLFdBQVU7c0JBQ2QsNEVBQUNFO2dCQUFJRixXQUFXTiwrRUFBc0I7O2tDQUNyQyw4REFBQ1U7d0JBQUdKLFdBQVdQLGlEQUFFQSxDQUFDQyx3RUFBZSxFQUFFO2tDQUF5Qkc7Ozs7OztrQ0FDNUQsOERBQUNMLG9GQUFLQTt3QkFDTFEsV0FBV04sMkVBQWtCO3dCQUM3QmEseUJBQVcsOERBQUNoQixtRkFBSUE7NEJBQUNTLFdBQVdQLGlEQUFFQSxDQUFDQyxvRUFBVzs0QkFBR2UsU0FBUTs7Ozs7O3dCQUNyREMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztFQUtoQjtLQWJZZiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2hvbWVwYWdlL1NlYXJjaEJhci9TZWFyY2hCYXIudHN4P2Y5NjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSWNvbiwgSW5wdXQgfSBmcm9tICdAY29sbGVjdGl2ZS9jb3JlJ1xuaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnXG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vc2VhcmNoYmFyLm1vZHVsZS5zY3NzJ1xuXG50eXBlIFNlYXJjaEJhclByb3BzID0ge1xuXHRjaWQ/OiBzdHJpbmdcblx0SGVhZGxpbmU6IHN0cmluZ1xufVxuXG5leHBvcnQgY29uc3QgU2VhcmNoQmFyID0gKHsgY2lkLCBIZWFkbGluZSB9OiBTZWFyY2hCYXJQcm9wcykgPT4gKFxuXHQ8c2VjdGlvbiBpZD17Y2lkfSBjbGFzc05hbWU9e2NuKHN0eWxlcy53cmFwcGVyKX0+XG5cdFx0PGRpdiBjbGFzc05hbWU9XCJhaWRpZ2lfX2dyaWRcIj5cblx0XHRcdDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuc2VhcmNoX193cmFwcGVyfT5cblx0XHRcdFx0PGgzIGNsYXNzTmFtZT17Y24oc3R5bGVzLmhlYWRsaW5lLCAnYWlkaWdpX19oZWFkaW5nLS1oMycpfT57SGVhZGxpbmV9PC9oMz5cblx0XHRcdFx0PElucHV0XG5cdFx0XHRcdFx0Y2xhc3NOYW1lPXtzdHlsZXMuc2VhcmNoX19iYXJ9XG5cdFx0XHRcdFx0c3RhcnRJY29uPXs8SWNvbiBjbGFzc05hbWU9e2NuKHN0eWxlcy5pY29uKX0gdmFyaWFudD1cInNlYXJjaC1pY29uXCIgLz59XG5cdFx0XHRcdFx0cGxhY2Vob2xkZXI9XCJMb2dvIHVzYWdlc1wiXG5cdFx0XHRcdC8+XG5cdFx0XHQ8L2Rpdj5cblx0XHQ8L2Rpdj5cblx0PC9zZWN0aW9uPlxuKVxuIl0sIm5hbWVzIjpbIkljb24iLCJJbnB1dCIsImNuIiwic3R5bGVzIiwiU2VhcmNoQmFyIiwiY2lkIiwiSGVhZGxpbmUiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJ3cmFwcGVyIiwiZGl2Iiwic2VhcmNoX193cmFwcGVyIiwiaDMiLCJoZWFkbGluZSIsInNlYXJjaF9fYmFyIiwic3RhcnRJY29uIiwiaWNvbiIsInZhcmlhbnQiLCJwbGFjZWhvbGRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/SearchBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/index.ts":
/*!******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/SearchBar/index.ts ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_0__.SearchBar; }\n/* harmony export */ });\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SearchBar */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/SearchBar.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvU2VhcmNoQmFyL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvU2VhcmNoQmFyL2luZGV4LnRzP2VlODkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9TZWFyY2hCYXInXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/homepage/index.ts":
/*!********************************************************!*\
  !*** ../../packages/ui-lib/src/base/homepage/index.ts ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroScene: function() { return /* reexport safe */ _HeroScene__WEBPACK_IMPORTED_MODULE_0__.HeroScene; },\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_1__.SearchBar; }\n/* harmony export */ });\n/* harmony import */ var _HeroScene__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HeroScene */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/HeroScene/index.ts\");\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SearchBar */ \"(app-pages-browser)/../../packages/ui-lib/src/base/homepage/SearchBar/index.ts\");\n// Export modules\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaG9tZXBhZ2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLGlCQUFpQjtBQUNVO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9ob21lcGFnZS9pbmRleC50cz8xYTQyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBtb2R1bGVzXG5leHBvcnQgKiBmcm9tICcuL0hlcm9TY2VuZSdcbmV4cG9ydCAqIGZyb20gJy4vU2VhcmNoQmFyJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/homepage/index.ts\n"));

/***/ })

}]);