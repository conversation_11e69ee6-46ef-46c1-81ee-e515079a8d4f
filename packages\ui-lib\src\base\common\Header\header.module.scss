@use '@collective/ui-lib/styles/config' as *;

.wrapper {
	margin-bottom: var(--section-mg-btm);
	// padding-top: px-to(96px, rem);

	// &:first-child {
	// 	padding-top: 0;
	// 	border: 1px solid;
	// }

	:global(.aidigi__grid) {
		display: flex;
		flex-flow: wrap;
		justify-content: space-between;
		gap: spacing(s5);
		// padding: 0;
	}

	.headline {
		display: grid;
		gap: spacing(s3);

		@include min-width('2md') {
			flex-basis: calc(75% - spacing(s5));
		}

		h1 {
			font-weight: 500;
		}

		h3 {
			font-weight: 400;
		}
	}

	.download {
		text-align: right;

		@include min-width('2md') {
			flex-basis: calc(25% - spacing(s5));
		}
	}
}
